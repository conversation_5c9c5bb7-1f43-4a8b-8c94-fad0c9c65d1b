package controllerv2

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"

	"github.com/risingwavelabs/eris"
	"github.com/risingwavelabs/risingwave-cloud/account/email"
	"github.com/risingwavelabs/risingwave-cloud/account/services"
	"github.com/risingwavelabs/risingwave-cloud/shared/ginx"
	ginauth "github.com/risingwavelabs/risingwave-cloud/shared/ginx/auth"
	"github.com/risingwavelabs/risingwave-cloud/shared/spec/acc_spec_v2"
)

func (controller *UserController) GetNotifications(c *gin.Context, params acc_spec_v2.GetNotificationsParams) {
	userInfo, err := ginauth.GetUserInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	var createdAtAsc bool
	if params.Order != nil && *params.Order == acc_spec_v2.CreatedAtAsc {
		createdAtAsc = true
	}
	pagination, err := controller.notificationService.GetAllNotificationsByUserID(c, userInfo.UserResourceID, params.Offset, params.Limit, createdAtAsc)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	ret := make([]acc_spec_v2.Notification, len(pagination.NotificationsList))
	for i := range pagination.NotificationsList {
		ret[i] = acc_spec_v2.Notification{
			Id:   pagination.NotificationsList[i].ID,
			Time: pagination.NotificationsList[i].CreatedAt,
			Sender: acc_spec_v2.NotificationSender{
				Email: pagination.NotificationsList[i].SenderEmail,
				Name:  pagination.NotificationsList[i].SenderName,
			},
			Content: pagination.NotificationsList[i].Content,
			Type:    acc_spec_v2.NotificationType(pagination.NotificationsList[i].Type),
			Title:   pagination.NotificationsList[i].Title,
			Status:  acc_spec_v2.NotificationStatus(pagination.NotificationsList[i].Status),
		}
	}

	c.JSON(200, acc_spec_v2.NotificationPagination{
		Pagination: &acc_spec_v2.Pagination{
			Limit:  pagination.Limit,
			Offset: pagination.Offset,
			Size:   pagination.Size,
		},
		Notifications: ret,
		UnreadNum:     uint64(pagination.UnreadNum),
	})
}

func (controller *UserController) PutNotificationsId(c *gin.Context, id uint64) {
	userInfo, err := ginauth.GetUserInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	var req acc_spec_v2.PutNotificationsIdJSONRequestBody
	if err = c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}

	err = controller.model.UpdateNotificationsStatusByID(c, userInfo.UserResourceID, []int32{int32(id)}, string(req.Status))
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	ginx.JustOk(c)
}

func (controller *UserController) DeleteRecipientsRecipientId(c *gin.Context, id uuid.UUID) {
	userInfo, err := ginauth.GetUserInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	err = controller.notificationService.DeleteRecipientByID(c, id, userInfo.OrgID)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	ginx.JustOk(c)
}

func (controller *UserController) PostRecipients(c *gin.Context) {
	userInfo, err := ginauth.GetUserInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	var req acc_spec_v2.PostRecipientsJSONRequestBody
	if err = c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}

	recipientType, err := req.Config.Discriminator()
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	var recipientConfig services.RecipientConfig

	switch recipientType {
	case string(acc_spec_v2.RecipientTypeEmail):
		config, err := req.Config.AsEmailRecipientConfig()
		if err != nil {
			ginx.InternalError(c, err)
			return
		}
		recipientConfig = services.EmailRecipientConfig{
			Email: config.Email,
			Type:  string(config.Type),
		}
	case string(acc_spec_v2.RecipientTypeSlack):
		config, err := req.Config.AsSlackRecipientConfig()
		if err != nil {
			ginx.InternalError(c, err)
			return
		}
		recipientConfig = services.SlackRecipientConfig{
			WebhookURL: config.WebhookURL,
			Type:       string(config.Type),
		}
	case string(acc_spec_v2.RecipientTypeUser):
		config, err := req.Config.AsUserRecipientConfig()
		if err != nil {
			ginx.InternalError(c, err)
			return
		}
		recipientConfig = services.UserRecipientConfig{
			UserID: config.UserId,
			Type:   string(config.Type),
		}
	default:
		ginx.InvalidArgument(c, "invalid recipient type")
		return
	}

	err = controller.notificationService.CreateRecipient(c, userInfo.OrgID, recipientType, recipientConfig)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	ginx.JustOk(c)
}

func (controller *UserController) GetRecipients(c *gin.Context) {
	userInfo, err := ginauth.GetUserInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	recipients, err := controller.notificationService.GetRecipientsByOrgID(c, userInfo.OrgID)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	recipientsSpec, err := services.ToRecipientsSpec(recipients)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	c.JSON(http.StatusOK, recipientsSpec)
}

func (controller *UserController) DeleteSubscriptionsSubId(c *gin.Context, _ uuid.UUID) {
	ginx.NotImplementedf(c, "not implemented")
}

func (controller *UserController) GetSubscriptions(c *gin.Context) {
	userInfo, err := ginauth.GetUserInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	subscriptions, err := controller.notificationService.GetSubscriptionsByOrgID(c, userInfo.OrgID)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	subscriptionsSpec, err := services.ToSubscriptionsSpec(subscriptions)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	c.JSON(http.StatusOK, subscriptionsSpec)
}

func (controller *UserController) PostSubscriptions(c *gin.Context) {
	ginx.NotImplementedf(c, "not implemented")
}

func (controller *UserController) PutRecipientsRecipientIdSubscriptions(c *gin.Context, recipientID uuid.UUID) {
	userInfo, err := ginauth.GetUserInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	var req acc_spec_v2.PutRecipientSubscriptionRequestBody
	if err = c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}

	err = controller.notificationService.UpdateSubscriptionsByRecipientID(c, recipientID, userInfo.OrgID, req.Severities)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	ginx.JustOk(c)
}

func (controller *UserController) GetAlertTypes(c *gin.Context) {
	alertTypes, err := controller.alertService.GetAlertTypes()
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	alertTypesSpec, err := toAlertTypesSpec(alertTypes)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	c.JSON(http.StatusOK, alertTypesSpec)
}

func (controller *UserController) PostRecipientsRecipientIdTest(c *gin.Context, recipientId uuid.UUID) {
	userInfo, err := ginauth.GetUserInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	recipient, err := controller.notificationService.GetRecipientByID(c, recipientId, userInfo.OrgID)
	if err != nil {
		if eris.GetCode(err) == eris.CodeNotFound {
			ginx.NotFoundError(c, err)
			return
		}
		ginx.InternalError(c, err)
		return
	}

	if recipient.Type != string(acc_spec_v2.RecipientTypeEmail) {
		ginx.InvalidArgument(c, "sending test notification is not implemented for this recipient type")
		return
	}

	config, err := recipient.Config.ToRecipientConfigSpec()
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	emailConfig, err := config.AsEmailRecipientConfig()
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	err = controller.emailService.SendEmailsByTemplate(c, []string{emailConfig.Email}, "This is a test notification from RisingWave Cloud.", email.TestNotificationTemplateName, nil)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	ginx.JustOk(c)
}

func toAlertTypesSpec(alertGroups []services.AlertType) (acc_spec_v2.AlertTypeArray, error) {
	if alertGroups == nil {
		return nil, nil
	}
	alertGroupsSpec := make(acc_spec_v2.AlertTypeArray, 0, len(alertGroups))

	err := copier.Copy(&alertGroupsSpec, &alertGroups)
	if err != nil {
		return nil, err
	}
	return alertGroupsSpec, nil
}
