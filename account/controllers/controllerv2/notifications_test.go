package controllerv2

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"gotest.tools/v3/assert"

	"github.com/risingwavelabs/risingwave-cloud/account/config"
	"github.com/risingwavelabs/risingwave-cloud/account/email"
	"github.com/risingwavelabs/risingwave-cloud/account/email/client/mock"
	"github.com/risingwavelabs/risingwave-cloud/account/model"
	model_mock "github.com/risingwavelabs/risingwave-cloud/account/model/mock"
	"github.com/risingwavelabs/risingwave-cloud/account/model/querier"
	"github.com/risingwavelabs/risingwave-cloud/account/services"
	services_mock "github.com/risingwavelabs/risingwave-cloud/account/services/mock"
	ginauth "github.com/risingwavelabs/risingwave-cloud/shared/ginx/auth"
	"github.com/risingwavelabs/risingwave-cloud/shared/paging"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"
	"github.com/risingwavelabs/risingwave-cloud/shared/spec/acc_spec_v2"
)

func TestGetNotifications(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	rec := httptest.NewRecorder()
	orgID := uuid.New()
	userResourceID := uuid.New()
	userInfo := &ginauth.ControllerUserInfo{
		OrgID:          orgID,
		UserResourceID: userResourceID,
	}
	ctx := buildTestContext(rec, nil, userInfo.ToAuthInfo())

	mockNotificationService := services_mock.NewMockNotificationServiceInterface(ctrl)
	mockNotificationService.EXPECT().
		GetAllNotificationsByUserID(gomock.Any(), gomock.Eq(userResourceID), gomock.Eq(ptr.Ptr(uint64(0))), gomock.Eq(ptr.Ptr(uint64(10))), gomock.Eq(false)).
		Times(1).
		Return(services.NotificationPagination{
			Page: paging.Page{
				Offset: uint64(0),
				Limit:  uint64(10),
			},
			Size:              uint64(20),
			NotificationsList: []*model.Notification{},
		}, nil)

	controller := &UserController{
		notificationService: mockNotificationService,
	}

	controller.GetNotifications(ctx, acc_spec_v2.GetNotificationsParams{
		Offset: ptr.Ptr(uint64(0)),
		Limit:  ptr.Ptr(uint64(10)),
	})
	assert.Equal(t, http.StatusOK, rec.Result().StatusCode)
}

func TestPutNotificationsId(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	rec := httptest.NewRecorder()
	userResourceID := uuid.New()

	reqParams := &acc_spec_v2.PutNotificationsIdJSONRequestBody{
		Status: acc_spec_v2.PutNotificationsStatusRequestBodyStatusRead,
	}
	orgID := uuid.New()
	userInfo := &ginauth.ControllerUserInfo{
		OrgID:          orgID,
		UserResourceID: userResourceID,
	}
	ctx := buildTestContext(rec, reqParams, userInfo.ToAuthInfo())

	q := model_mock.NewMockQuerier(ctrl)
	q.EXPECT().
		BulkReadNotificationsById(gomock.Any(), gomock.Eq(querier.BulkReadNotificationsByIdParams{
			Status:         "Read",
			UserResourceID: userResourceID,
			ID:             []int32{111},
		})).Times(1).
		Return(nil)

	controller := &UserController{
		model: model.NewModel(q),
	}

	controller.PutNotificationsId(ctx, 111)
	assert.Equal(t, http.StatusOK, rec.Result().StatusCode)
}

func TestPostRecipients(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	rec := httptest.NewRecorder()
	userResourceID := uuid.New()
	email := "<EMAIL>"
	emailRecipientConfig := acc_spec_v2.EmailRecipientConfig{
		Type:  acc_spec_v2.RecipientTypeEmail,
		Email: email,
	}
	var recipientConfig acc_spec_v2.RecipientConfig
	err := recipientConfig.FromEmailRecipientConfig(emailRecipientConfig)
	require.NoError(t, err)

	reqParams := &acc_spec_v2.PostRecipientRequestBody{
		Config: recipientConfig,
	}
	orgID := uuid.New()
	userInfo := &ginauth.ControllerUserInfo{
		OrgID:          orgID,
		UserResourceID: userResourceID,
	}
	ctx := buildTestContext(rec, reqParams, userInfo.ToAuthInfo())

	mockNotificationService := services_mock.NewMockNotificationServiceInterface(ctrl)
	mockNotificationService.EXPECT().
		CreateRecipient(gomock.Any(), gomock.Eq(orgID), gomock.Eq(string(acc_spec_v2.RecipientTypeEmail)), gomock.Eq(services.EmailRecipientConfig{
			Email: email,
			Type:  string(acc_spec_v2.RecipientTypeEmail),
		})).
		Times(1).
		Return(nil)

	controller := &UserController{
		notificationService: mockNotificationService,
	}

	controller.PostRecipients(ctx)
	assert.Equal(t, http.StatusOK, rec.Result().StatusCode)
}

func TestDeleteRecipientsRecipientId(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	rec := httptest.NewRecorder()
	userResourceID := uuid.New()
	recipientID := uuid.New()

	orgID := uuid.New()
	userInfo := &ginauth.ControllerUserInfo{
		OrgID:          orgID,
		UserResourceID: userResourceID,
	}
	ctx := buildTestContext(rec, nil, userInfo.ToAuthInfo())

	mockNotificationService := services_mock.NewMockNotificationServiceInterface(ctrl)
	mockNotificationService.EXPECT().
		DeleteRecipientByID(gomock.Any(), gomock.Eq(recipientID), gomock.Eq(orgID)).
		Times(1).
		Return(nil)

	controller := &UserController{
		notificationService: mockNotificationService,
	}

	controller.DeleteRecipientsRecipientId(ctx, recipientID)
	assert.Equal(t, http.StatusOK, rec.Result().StatusCode)
}

func TestGetRecipients(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	rec := httptest.NewRecorder()
	userResourceID := uuid.New()
	recipientID := uuid.New()
	orgID := uuid.New()
	email := "<EMAIL>"
	recipientConfig := acc_spec_v2.RecipientConfig{}
	err := recipientConfig.FromEmailRecipientConfig(acc_spec_v2.EmailRecipientConfig{
		Email: email,
		Type:  acc_spec_v2.RecipientTypeEmail,
	})
	require.NoError(t, err)
	recipient := &acc_spec_v2.Recipient{
		Id:     recipientID,
		Config: recipientConfig,
		OrgId:  orgID,
	}

	userInfo := &ginauth.ControllerUserInfo{
		OrgID:          orgID,
		UserResourceID: userResourceID,
	}
	ctx := buildTestContext(rec, nil, userInfo.ToAuthInfo())

	mockNotificationService := services_mock.NewMockNotificationServiceInterface(ctrl)
	mockNotificationService.EXPECT().
		GetRecipientsByOrgID(gomock.Any(), gomock.Eq(orgID)).
		Times(1).
		Return([]services.Recipient{
			{
				ID:    recipientID,
				OrgID: orgID,
				Type:  string(acc_spec_v2.RecipientTypeEmail),
				Config: services.EmailRecipientConfig{
					Email: email,
					Type:  string(acc_spec_v2.RecipientTypeEmail),
				},
			},
		}, nil)

	controller := &UserController{
		notificationService: mockNotificationService,
	}

	controller.GetRecipients(ctx)
	assert.Equal(t, http.StatusOK, rec.Result().StatusCode)

	rawRecipientJSON, err := json.Marshal([]*acc_spec_v2.Recipient{recipient})
	require.NoError(t, err)
	assert.Equal(t, string(rawRecipientJSON), rec.Body.String())
}

func TestPutRecipientsRecipientIdSubscriptions(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	rec := httptest.NewRecorder()
	recipientID := uuid.New()

	reqParams := &acc_spec_v2.PutRecipientSubscriptionRequestBody{
		Severities: []acc_spec_v2.AlertSeverity{
			acc_spec_v2.Warning,
		},
	}
	orgID := uuid.New()
	userInfo := &ginauth.ControllerUserInfo{
		OrgID: orgID,
	}
	ctx := buildTestContext(rec, reqParams, userInfo.ToAuthInfo())

	mockNotificationService := services_mock.NewMockNotificationServiceInterface(ctrl)
	mockNotificationService.EXPECT().
		UpdateSubscriptionsByRecipientID(gomock.Any(), gomock.Eq(recipientID), gomock.Eq(orgID), gomock.Eq([]acc_spec_v2.AlertSeverity{acc_spec_v2.Warning})).
		Times(1).
		Return(nil)

	controller := &UserController{
		notificationService: mockNotificationService,
	}

	controller.PutRecipientsRecipientIdSubscriptions(ctx, recipientID)
	assert.Equal(t, http.StatusOK, rec.Result().StatusCode)
}

func TestGetSubscriptions(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	rec := httptest.NewRecorder()
	userResourceID := uuid.New()
	recipientID := uuid.New()
	orgID := uuid.New()
	subscription1ID := uuid.New()
	subscription2ID := uuid.New()

	userInfo := &ginauth.ControllerUserInfo{
		OrgID:          orgID,
		UserResourceID: userResourceID,
	}
	ctx := buildTestContext(rec, nil, userInfo.ToAuthInfo())

	mockNotificationService := services_mock.NewMockNotificationServiceInterface(ctrl)
	mockNotificationService.EXPECT().
		GetSubscriptionsByOrgID(gomock.Any(), gomock.Eq(orgID)).
		Times(1).
		Return([]services.Subscription{
			{
				ID:          subscription1ID,
				RecipientID: recipientID,
				Severity:    string(acc_spec_v2.Warning),
			},
			{
				ID:          subscription2ID,
				RecipientID: recipientID,
				Severity:    string(acc_spec_v2.Critical),
			},
		}, nil)

	controller := &UserController{
		notificationService: mockNotificationService,
	}

	controller.GetSubscriptions(ctx)
	assert.Equal(t, http.StatusOK, rec.Result().StatusCode)

	rawSubscriptionsJSON, err := json.Marshal(acc_spec_v2.SubscriptionArray{
		{
			Id:          subscription1ID,
			RecipientId: recipientID,
			Severity:    acc_spec_v2.Warning,
		},
		{
			Id:          subscription2ID,
			RecipientId: recipientID,
			Severity:    acc_spec_v2.Critical,
		},
	})
	require.NoError(t, err)
	assert.Equal(t, string(rawSubscriptionsJSON), rec.Body.String())
}

func TestGetAlertTypes(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	rec := httptest.NewRecorder()
	orgID := uuid.New()
	userResourceID := uuid.New()
	userInfo := &ginauth.ControllerUserInfo{
		OrgID:          orgID,
		UserResourceID: userResourceID,
	}
	ctx := buildTestContext(rec, nil, userInfo.ToAuthInfo())

	svc := services_mock.NewMockAlertServiceInterface(ctrl)
	svc.
		EXPECT().
		GetAlertTypes().
		Return([]services.AlertType{
			{
				Name:        "test-name",
				Description: "test-desc",
				Severity:    "warning",
				Category:    "test-category",
			},
		}, nil)

	controller := &UserController{
		alertService: svc,
	}

	controller.GetAlertTypes(ctx)
	assert.Equal(t, http.StatusOK, ctx.Writer.Status())

	assert.Equal(t, toJSON(t, acc_spec_v2.AlertTypeArray{
		{
			Name:        "test-name",
			Description: "test-desc",
			Severity:    "warning",
			Category:    "test-category",
		},
	}), rec.Body.String())
}

func TestPostRecipientsRecipientIdTest(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	rec := httptest.NewRecorder()
	userResourceID := uuid.New()
	recipientEmail := "<EMAIL>"

	orgID := uuid.New()
	recipientID := uuid.New()
	userInfo := &ginauth.ControllerUserInfo{
		OrgID:          orgID,
		UserResourceID: userResourceID,
	}
	ctx := buildTestContext(rec, nil, userInfo.ToAuthInfo())

	mockNotificationService := services_mock.NewMockNotificationServiceInterface(ctrl)
	mockNotificationService.EXPECT().
		GetRecipientByID(gomock.Any(), gomock.Eq(recipientID), gomock.Eq(orgID)).
		Times(1).
		Return(services.Recipient{
			ID:   recipientID,
			Type: string(acc_spec_v2.RecipientTypeEmail),
			Config: services.EmailRecipientConfig{
				Email: recipientEmail,
				Type:  string(acc_spec_v2.RecipientTypeEmail),
			},
		}, nil)

	config.Conf.AccountService.AssetPath = "../../assets"
	template, err := email.LoadEmailTemplates()
	require.NoError(t, err)
	client := mock.NewEmailClientWithCache().(*mock.EmailClient)
	emailService := email.NewEmailService(template, client)

	controller := &UserController{
		notificationService: mockNotificationService,
		emailService:        emailService,
	}

	controller.PostRecipientsRecipientIdTest(ctx, recipientID)
	assert.Equal(t, http.StatusOK, rec.Result().StatusCode)
}
