package controllerv2

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"gotest.tools/v3/assert"

	"github.com/risingwavelabs/risingwave-cloud/account/config"
	"github.com/risingwavelabs/risingwave-cloud/account/email"
	"github.com/risingwavelabs/risingwave-cloud/account/email/client/mock"
	"github.com/risingwavelabs/risingwave-cloud/account/model"
	model_mock "github.com/risingwavelabs/risingwave-cloud/account/model/mock"
	"github.com/risingwavelabs/risingwave-cloud/account/model/querier"
	"github.com/risingwavelabs/risingwave-cloud/account/rbac"
	"github.com/risingwavelabs/risingwave-cloud/account/services"
	services_mock "github.com/risingwavelabs/risingwave-cloud/account/services/mock"
	ginauth "github.com/risingwavelabs/risingwave-cloud/shared/ginx/auth"
	"github.com/risingwavelabs/risingwave-cloud/shared/paging"
	"github.com/risingwavelabs/risingwave-cloud/shared/ptr"
	spec "github.com/risingwavelabs/risingwave-cloud/shared/spec/acc_spec"
	"github.com/risingwavelabs/risingwave-cloud/shared/spec/acc_spec_v2"
)

func TestGetInvitations(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	rec := httptest.NewRecorder()
	orgID := uuid.New()
	userResourceID := uuid.New()
	userInfo := &ginauth.ControllerUserInfo{
		OrgID:          orgID,
		UserResourceID: userResourceID,
	}
	ctx := buildTestContext(rec, nil, userInfo.ToAuthInfo())

	q := model_mock.NewMockQuerier(ctrl)
	mockInvitationService := services_mock.NewMockInvitationServiceInterface(ctrl)
	mockInvitationService.EXPECT().
		GetInvitationsByOrgID(gomock.Any(), gomock.Eq(orgID), gomock.Eq(ptr.Ptr(uint64(0))), gomock.Eq(ptr.Ptr(uint64(10)))).
		Times(1).
		Return(services.InvitationPagination{
			Page: paging.Page{
				Offset: uint64(0),
				Limit:  uint64(10),
			},
			Size:           uint64(20),
			InvitationList: []*model.Invitation{},
		}, nil)

	controller := &UserController{
		model:             model.NewModel(q),
		invitationService: mockInvitationService,
	}

	controller.GetInvitations(ctx, acc_spec_v2.GetInvitationsParams{
		Offset: ptr.Ptr(uint64(0)),
		Limit:  ptr.Ptr(uint64(10)),
	})
	assert.Equal(t, http.StatusOK, rec.Result().StatusCode)
}

func TestPostInvitations(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	rec := httptest.NewRecorder()
	inviteeEmail := "<EMAIL>"
	inviterEmail := "<EMAIL>"
	userResourceID := uuid.New()

	mockAuthorizer, err := loadMockAuthorizer(nil, nil)
	require.NoError(t, err)

	roleID, err := mockAuthorizer.GetRoleIDByName(context.Background(), rbac.OrgAdmin)
	require.NoError(t, err)

	reqParams := &spec.PostInvitationsJSONRequestBody{
		Email:  inviteeEmail,
		RoleId: &roleID,
	}
	orgID := uuid.New()
	userInfo := &ginauth.ControllerUserInfo{
		OrgID:          orgID,
		UserResourceID: userResourceID,
	}
	ctx := buildTestContext(rec, reqParams, userInfo.ToAuthInfo())

	q := model_mock.NewMockQuerier(ctrl)
	q.EXPECT().
		IsUserExistByEmail(gomock.Any(), gomock.Eq(inviteeEmail)).Times(1).
		Return(int64(0), nil)

	q.EXPECT().
		GetOrgById(gomock.Any(), gomock.Eq(orgID)).Times(1).
		Return(&querier.Org{
			Name: "test-org",
		}, nil)
	q.EXPECT().
		GetUserByResourceId(gomock.Any(), gomock.Eq(userResourceID)).Times(1).
		Return(&querier.User{
			Email: inviterEmail,
		}, nil)

	mockInvitationService := services_mock.NewMockInvitationServiceInterface(ctrl)
	mockInvitationService.EXPECT().
		CreateInvitation(gomock.Any(), gomock.Eq(inviteeEmail), gomock.Eq(inviterEmail), gomock.Eq(orgID), gomock.Eq(roleID)).
		Times(1).
		Return(&model.Invitation{}, "https://mock.invitation.org", nil)

	config.Conf.AccountService.AssetPath = "../../assets"
	template, err := email.LoadEmailTemplates()
	require.NoError(t, err)
	client := mock.NewEmailClientWithCache().(*mock.EmailClient)
	emailService := email.NewEmailService(template, client)

	controller := &UserController{
		model:             model.NewModel(q),
		emailService:      emailService,
		authorizer:        mockAuthorizer,
		invitationService: mockInvitationService,
	}

	controller.PostInvitations(ctx)
	assert.Equal(t, http.StatusOK, rec.Result().StatusCode)
}

func TestPostInvitationsAlreadyExists(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	rec := httptest.NewRecorder()
	inviteeEmail := "<EMAIL>"
	reqParams := &spec.PostInvitationsJSONRequestBody{
		Email: inviteeEmail,
	}
	orgID := uuid.New()
	userInfo := &ginauth.ControllerUserInfo{
		OrgID: orgID,
	}
	ctx := buildTestContext(rec, reqParams, userInfo.ToAuthInfo())

	q := model_mock.NewMockQuerier(ctrl)

	q.EXPECT().
		IsUserExistByEmail(gomock.Any(), gomock.Eq(inviteeEmail)).Times(1).
		Return(int64(1), nil)

	mockInvitationService := services_mock.NewMockInvitationServiceInterface(ctrl)

	config.Conf.AccountService.AssetPath = "../../assets"
	template, err := email.LoadEmailTemplates()
	require.NoError(t, err)
	client := mock.NewEmailClientWithCache().(*mock.EmailClient)
	service := email.NewEmailService(template, client)
	controller := &UserController{
		model:             model.NewModel(q),
		emailService:      service,
		invitationService: mockInvitationService,
	}

	controller.PostInvitations(ctx)
	assert.Equal(t, http.StatusConflict, rec.Result().StatusCode)
}

func TestPostInvitationsApiKey(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	rec := httptest.NewRecorder()
	inviteeEmail := "<EMAIL>"
	saName := "test-sa"
	serviceAccountID := uuid.New()

	mockAuthorizer, err := loadMockAuthorizer(nil, nil)
	require.NoError(t, err)

	roleID, err := mockAuthorizer.GetRoleIDByName(context.Background(), rbac.OrgAdmin)
	require.NoError(t, err)

	reqParams := &spec.PostInvitationsJSONRequestBody{
		Email:  inviteeEmail,
		RoleId: &roleID,
	}
	orgID := uuid.New()
	serviceAccountInfo := &ginauth.ControllerServiceAccountInfo{
		OrgID:            orgID,
		ServiceAccountID: serviceAccountID,
	}
	ctx := buildTestContext(rec, reqParams, serviceAccountInfo.ToAuthInfo())

	q := model_mock.NewMockQuerier(ctrl)
	q.EXPECT().
		IsUserExistByEmail(gomock.Any(), gomock.Eq(inviteeEmail)).Times(1).
		Return(int64(0), nil)

	q.EXPECT().
		GetOrgById(gomock.Any(), gomock.Eq(orgID)).Times(1).
		Return(&querier.Org{
			Name: "test-org",
		}, nil)
	q.EXPECT().
		GetServiceAccountByIdAndOrg(gomock.Any(), gomock.Eq(querier.GetServiceAccountByIdAndOrgParams{
			OrgID: orgID,
			ID:    serviceAccountID,
		})).Times(1).
		Return(&querier.ServiceAccount{
			Name: saName,
		}, nil)

	mockInvitationService := services_mock.NewMockInvitationServiceInterface(ctrl)
	mockInvitationService.EXPECT().
		CreateInvitation(gomock.Any(), gomock.Eq(inviteeEmail), gomock.Eq(saName), gomock.Eq(orgID), gomock.Eq(roleID)).
		Times(1).
		Return(&model.Invitation{}, "https://mock.invitation.org", nil)

	config.Conf.AccountService.AssetPath = "../../assets"
	template, err := email.LoadEmailTemplates()
	require.NoError(t, err)
	client := mock.NewEmailClientWithCache().(*mock.EmailClient)
	emailService := email.NewEmailService(template, client)

	controller := &UserController{
		model:             model.NewModel(q),
		emailService:      emailService,
		authorizer:        mockAuthorizer,
		invitationService: mockInvitationService,
	}

	controller.PostInvitations(ctx)
	assert.Equal(t, http.StatusOK, rec.Result().StatusCode)
}

func TestDeleteInvitationsId(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	rec := httptest.NewRecorder()
	orgID := uuid.New()
	userInfo := &ginauth.ControllerUserInfo{
		OrgID: orgID,
	}
	ctx := buildTestContext(rec, nil, userInfo.ToAuthInfo())

	q := model_mock.NewMockQuerier(ctrl)
	q.EXPECT().
		DeleteInvitationByIdAndOrg(gomock.Any(), gomock.Eq(querier.DeleteInvitationByIdAndOrgParams{
			OrgID: orgID,
			ID:    0,
		})).Times(1).
		Return(nil)

	controller := &UserController{
		model: model.NewModel(q),
	}

	controller.DeleteInvitationsId(ctx, 0)
	assert.Equal(t, http.StatusOK, rec.Result().StatusCode)
}

func TestGetInvitationsId(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	rec := httptest.NewRecorder()
	orgID := uuid.New()
	email := "<EMAIL>"
	userInfo := &ginauth.ControllerUserInfo{
		OrgID: orgID,
	}
	ctx := buildTestContext(rec, nil, userInfo.ToAuthInfo())

	q := model_mock.NewMockQuerier(ctrl)
	q.EXPECT().
		GetInvitationByIdAndOrg(gomock.Any(), gomock.Eq(querier.GetInvitationByIdAndOrgParams{
			ID:    0,
			OrgID: orgID,
		})).Times(1).
		Return(&querier.GetInvitationByIdAndOrgRow{
			Email: email,
		}, nil)

	controller := &UserController{
		model: model.NewModel(q),
	}

	controller.GetInvitationsId(ctx, 0)
	assert.Equal(t, http.StatusOK, rec.Result().StatusCode)
}
