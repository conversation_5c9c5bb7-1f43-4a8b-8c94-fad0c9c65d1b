package services

import (
	"context"
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"

	"github.com/risingwavelabs/eris"
	"github.com/risingwavelabs/risingwave-cloud/account/model"
	"github.com/risingwavelabs/risingwave-cloud/account/model/modeltx"
	"github.com/risingwavelabs/risingwave-cloud/shared/paging"
	"github.com/risingwavelabs/risingwave-cloud/shared/spec/acc_spec_v2"
)

type NotificationPagination struct {
	paging.Page
	Size              uint64
	UnreadNum         uint64                `json:"unread_num"`
	NotificationsList []*model.Notification `json:"notifications"`
}

type Recipient struct {
	ID        uuid.UUID       `json:"id"`
	Type      string          `json:"type"`
	Config    RecipientConfig `json:"config"`
	OrgID     uuid.UUID       `json:"orgId"`
	CreatedAt time.Time       `json:"createdAt"`
	UpdatedAt time.Time       `json:"updatedAt"`
}

type Subscription struct {
	ID          uuid.UUID `json:"id"`
	Severity    string    `json:"severity"`
	RecipientID uuid.UUID `json:"recipientId"`
	CreatedAt   time.Time `json:"createdAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

type RecipientConfig interface {
	ToRecipientConfigSpec() (acc_spec_v2.RecipientConfig, error)
}

type EmailRecipientConfig struct {
	Email string `json:"email"`
	Type  string `json:"type"`
}

type SlackRecipientConfig struct {
	WebhookURL string `json:"webhookURL"`
	Type       string `json:"type"`
}

type UserRecipientConfig struct {
	UserID uuid.UUID `json:"userId"`
	Type   string    `json:"type"`
}

func (config UserRecipientConfig) ToRecipientConfigSpec() (acc_spec_v2.RecipientConfig, error) {
	userRecipientConfigSpec := acc_spec_v2.UserRecipientConfig{}
	configSpec := acc_spec_v2.RecipientConfig{}
	err := copier.Copy(&userRecipientConfigSpec, &config)
	if err != nil {
		return acc_spec_v2.RecipientConfig{}, err
	}
	err = configSpec.FromUserRecipientConfig(userRecipientConfigSpec)
	if err != nil {
		return acc_spec_v2.RecipientConfig{}, err
	}
	return configSpec, nil
}

func (config EmailRecipientConfig) ToRecipientConfigSpec() (acc_spec_v2.RecipientConfig, error) {
	emailRecipientConfig := acc_spec_v2.EmailRecipientConfig{}
	configSpec := acc_spec_v2.RecipientConfig{}
	err := copier.Copy(&emailRecipientConfig, &config)
	if err != nil {
		return acc_spec_v2.RecipientConfig{}, err
	}
	err = configSpec.FromEmailRecipientConfig(emailRecipientConfig)
	if err != nil {
		return acc_spec_v2.RecipientConfig{}, err
	}
	return configSpec, nil
}

func (config SlackRecipientConfig) ToRecipientConfigSpec() (acc_spec_v2.RecipientConfig, error) {
	slackRecipientConfigSpec := acc_spec_v2.SlackRecipientConfig{}
	configSpec := acc_spec_v2.RecipientConfig{}
	err := copier.Copy(&slackRecipientConfigSpec, &config)
	if err != nil {
		return acc_spec_v2.RecipientConfig{}, err
	}
	err = configSpec.FromSlackRecipientConfig(slackRecipientConfigSpec)
	if err != nil {
		return acc_spec_v2.RecipientConfig{}, err
	}
	return configSpec, nil
}

type NotificationServiceInterface interface {
	GetAllNotificationsByUserID(ctx context.Context, userResourceID UserResourceID, _offset *uint64, _limit *uint64, createdAtAsc bool) (NotificationPagination, error)
	CreateNotification(ctx context.Context, senderEmail, senderName, content, title, messageType string, userIDs []uuid.UUID) error
	GetRecipientsByOrgID(ctx context.Context, orgID OrgID) ([]Recipient, error)
	GetRecipientByID(ctx context.Context, id, orgID OrgID) (Recipient, error)
	DeleteRecipientByID(ctx context.Context, id, orgID OrgID) error
	CreateRecipient(ctx context.Context, orgID OrgID, recipientType string, recipientConfig RecipientConfig) error
	GetSubscriptionsByOrgID(ctx context.Context, orgID OrgID) ([]Subscription, error)
	UpdateSubscriptionsByRecipientID(ctx context.Context, recipientID, orgID uuid.UUID, severities []acc_spec_v2.AlertSeverity) error
}

type NotificationService struct {
	model      *model.Model
	txProvider modeltx.TxProvider
}

func NewNotificationService(model *model.Model, txProvider modeltx.TxProvider) NotificationServiceInterface {
	return &NotificationService{
		model:      model,
		txProvider: txProvider,
	}
}

func (service *NotificationService) GetAllNotificationsByUserID(ctx context.Context, userResourceID UserResourceID, _offset *uint64, _limit *uint64, createdAtAsc bool) (NotificationPagination, error) {
	page := paging.HTTPPager.Paging(_offset, _limit)

	notificationModel := service.model
	size, err := notificationModel.GetNotificationsCount(ctx, userResourceID)
	if err != nil {
		return NotificationPagination{}, err
	}

	unread, err := notificationModel.GetUnreadNotificationsCount(ctx, userResourceID)
	if err != nil {
		return NotificationPagination{}, err
	}

	notificationsList, err := notificationModel.GetLatestNotificationsByUserID(ctx, userResourceID, page.Offset, page.Limit, createdAtAsc)

	if err != nil {
		return NotificationPagination{}, err
	}

	return NotificationPagination{
		Page: paging.Page{
			Limit:  page.Limit,
			Offset: page.Offset,
		},
		Size:              uint64(size),
		UnreadNum:         uint64(unread),
		NotificationsList: notificationsList,
	}, err
}

func (service *NotificationService) CreateNotification(ctx context.Context, senderEmail, senderName, content, title, messageType string, userIDs []uuid.UUID) error {
	return service.model.InsertNotification(ctx, senderName, senderEmail, messageType, userIDs, title, content)
}

func (service *NotificationService) GetRecipientsByOrgID(ctx context.Context, orgID OrgID) ([]Recipient, error) {
	recipients, err := service.model.GetRecipientsByOrgID(ctx, orgID)
	if err != nil {
		return nil, err
	}

	ret := make([]Recipient, len(recipients))

	for i, recipient := range recipients {
		config, err := toRecipientConfig(recipient.Config, recipient.Type)
		if err != nil {
			return nil, err
		}

		ret[i] = Recipient{
			ID:        recipient.ID,
			Type:      recipient.Type,
			Config:    config,
			OrgID:     recipient.OrgID,
			CreatedAt: recipient.CreatedAt,
			UpdatedAt: recipient.UpdatedAt,
		}
	}

	return ret, nil
}

func (service *NotificationService) CreateRecipient(ctx context.Context, orgID OrgID, recipientType string, recipientConfig RecipientConfig) error {
	rawConfigData, err := json.Marshal(recipientConfig)
	if err != nil {
		return eris.Wrap(err, "failed to marshal recipient config")
	}

	return service.model.CreateRecipient(ctx, orgID, recipientType, rawConfigData)
}

func (service *NotificationService) RecipientExists(ctx context.Context, id, orgID OrgID) (bool, error) {
	_, err := service.model.GetRecipientByID(ctx, id, orgID)

	if err != nil {
		if eris.GetCode(err) == eris.CodeNotFound {
			return false, nil
		}
		return false, err
	}

	return true, nil
}

func (service *NotificationService) DeleteRecipientByID(ctx context.Context, id uuid.UUID, orgID OrgID) error {
	ctx, cancel := context.WithTimeout(ctx, model.TransactionTimeout)
	defer cancel()

	return modeltx.Exec(ctx, service.txProvider, func(ctx context.Context, model *model.Model, _ model.Transaction) error {
		_, err := model.GetRecipientByID(ctx, id, orgID)

		if err != nil {
			if eris.GetCode(err) == eris.CodeNotFound {
				return eris.WithCode(err, eris.CodeNotFound)
			}
			return err
		}

		err = model.DeleteSubscriptionsByRecipientID(ctx, id)
		if err != nil {
			return err
		}

		err = model.DeleteRecipientByID(ctx, id, orgID)
		if err != nil {
			return err
		}

		return nil
	})
}

func (service *NotificationService) GetSubscriptionsByOrgID(ctx context.Context, orgID OrgID) ([]Subscription, error) {
	subscriptions, err := service.model.GetSubscriptionsByOrgID(ctx, orgID)
	if err != nil {
		return nil, err
	}

	ret := make([]Subscription, len(subscriptions))

	for i, subscription := range subscriptions {
		ret[i] = Subscription{
			ID:          subscription.ID,
			RecipientID: subscription.RecipientID,
			Severity:    subscription.Severity,
			CreatedAt:   subscription.CreatedAt,
			UpdatedAt:   subscription.UpdatedAt,
		}
	}

	return ret, nil
}

func (service *NotificationService) UpdateSubscriptionsByRecipientID(ctx context.Context, recipientID, orgID uuid.UUID, updatedSeverities []acc_spec_v2.AlertSeverity) error {
	ctx, cancel := context.WithTimeout(ctx, model.TransactionTimeout)
	defer cancel()

	return modeltx.Exec(ctx, service.txProvider, func(ctx context.Context, model *model.Model, _ model.Transaction) error {
		_, err := model.GetRecipientByID(ctx, recipientID, orgID)

		if err != nil {
			if eris.GetCode(err) == eris.CodeNotFound {
				return eris.WithCode(err, eris.CodeNotFound)
			}
			return err
		}
		subscriptions, err := model.GetSubscriptionsByRecipientID(ctx, recipientID)
		if err != nil {
			return err
		}

		var existentSeverities []acc_spec_v2.AlertSeverity
		for _, subscription := range subscriptions {
			existentSeverities = append(existentSeverities, acc_spec_v2.AlertSeverity(subscription.Severity))
		}
		toRemove, toAdd := lo.Difference(existentSeverities, updatedSeverities)

		for _, severity := range toRemove {
			err = model.DeleteSubscription(ctx, recipientID, string(severity))
			if err != nil {
				return err
			}
		}

		for _, severity := range toAdd {
			err = model.CreateSubscription(ctx, recipientID, string(severity))
			if err != nil {
				return err
			}
		}

		return nil
	})
}

func toRecipientConfig(rawConfigData json.RawMessage, recipientType string) (RecipientConfig, error) {
	switch recipientType {
	case string(acc_spec_v2.RecipientTypeEmail):
		emailConfig := EmailRecipientConfig{}
		err := json.Unmarshal(rawConfigData, &emailConfig)
		if err != nil {
			return nil, err
		}
		return emailConfig, nil
	case string(acc_spec_v2.RecipientTypeSlack):
		slackConfig := SlackRecipientConfig{}
		err := json.Unmarshal(rawConfigData, &slackConfig)
		if err != nil {
			return nil, err
		}
		return slackConfig, nil
	case string(acc_spec_v2.RecipientTypeUser):
		userConfig := UserRecipientConfig{}
		err := json.Unmarshal(rawConfigData, &userConfig)
		if err != nil {
			return nil, err
		}
		return userConfig, nil
	}

	return nil, eris.New("recipient type not supported")
}

func ToRecipientsSpec(recipients []Recipient) (acc_spec_v2.RecipientArray, error) {
	recipientsSpec := make(acc_spec_v2.RecipientArray, len(recipients))
	for i, recipient := range recipients {
		configSpec, err := recipient.Config.ToRecipientConfigSpec()
		if err != nil {
			return nil, err
		}
		recipientsSpec[i] = acc_spec_v2.Recipient{
			Id:        recipient.ID,
			Config:    configSpec,
			OrgId:     recipient.OrgID,
			CreatedAt: recipient.CreatedAt,
			UpdatedAt: recipient.UpdatedAt,
		}
	}
	return recipientsSpec, nil
}

func ToSubscriptionsSpec(subscriptions []Subscription) (acc_spec_v2.SubscriptionArray, error) {
	subscriptionsSpec := make(acc_spec_v2.SubscriptionArray, len(subscriptions))
	for i, subscription := range subscriptions {
		subscriptionsSpec[i] = acc_spec_v2.Subscription{
			Id:          subscription.ID,
			RecipientId: subscription.RecipientID,
			Severity:    acc_spec_v2.AlertSeverity(subscription.Severity),
		}
	}
	return subscriptionsSpec, nil
}

func (service *NotificationService) GetRecipientByID(ctx context.Context, id, orgID OrgID) (Recipient, error) {
	recipient, err := service.model.GetRecipientByID(ctx, id, orgID)
	if err != nil {
		if eris.GetCode(err) == eris.CodeNotFound {
			return Recipient{}, eris.New("recipient not found").WithCode(eris.CodeNotFound)
		}
		return Recipient{}, eris.Wrap(err, "failed to get recipient by id")
	}

	config, err := toRecipientConfig(recipient.Config, recipient.Type)
	if err != nil {
		return Recipient{}, eris.Wrap(err, "failed to convert recipient config")
	}

	return Recipient{
		ID:        recipient.ID,
		Type:      recipient.Type,
		Config:    config,
		OrgID:     recipient.OrgID,
		CreatedAt: recipient.CreatedAt,
		UpdatedAt: recipient.UpdatedAt,
	}, nil
}
