// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/risingwave-cloud/account/services (interfaces: NotificationServiceInterface)
//
// Generated by this command:
//
//	mockgen-v0.5.0 -package=mock -destination=services/mock/notifications_gen.go github.com/risingwavelabs/risingwave-cloud/account/services NotificationServiceInterface
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	uuid "github.com/google/uuid"
	services "github.com/risingwavelabs/risingwave-cloud/account/services"
	acc_spec_v2 "github.com/risingwavelabs/risingwave-cloud/shared/spec/acc_spec_v2"
	gomock "go.uber.org/mock/gomock"
)

// MockNotificationServiceInterface is a mock of NotificationServiceInterface interface.
type MockNotificationServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockNotificationServiceInterfaceMockRecorder
	isgomock struct{}
}

// MockNotificationServiceInterfaceMockRecorder is the mock recorder for MockNotificationServiceInterface.
type MockNotificationServiceInterfaceMockRecorder struct {
	mock *MockNotificationServiceInterface
}

// NewMockNotificationServiceInterface creates a new mock instance.
func NewMockNotificationServiceInterface(ctrl *gomock.Controller) *MockNotificationServiceInterface {
	mock := &MockNotificationServiceInterface{ctrl: ctrl}
	mock.recorder = &MockNotificationServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNotificationServiceInterface) EXPECT() *MockNotificationServiceInterfaceMockRecorder {
	return m.recorder
}

// CreateNotification mocks base method.
func (m *MockNotificationServiceInterface) CreateNotification(ctx context.Context, senderEmail, senderName, content, title, messageType string, userIDs []uuid.UUID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateNotification", ctx, senderEmail, senderName, content, title, messageType, userIDs)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateNotification indicates an expected call of CreateNotification.
func (mr *MockNotificationServiceInterfaceMockRecorder) CreateNotification(ctx, senderEmail, senderName, content, title, messageType, userIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNotification", reflect.TypeOf((*MockNotificationServiceInterface)(nil).CreateNotification), ctx, senderEmail, senderName, content, title, messageType, userIDs)
}

// CreateRecipient mocks base method.
func (m *MockNotificationServiceInterface) CreateRecipient(ctx context.Context, orgID uuid.UUID, recipientType string, recipientConfig services.RecipientConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRecipient", ctx, orgID, recipientType, recipientConfig)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateRecipient indicates an expected call of CreateRecipient.
func (mr *MockNotificationServiceInterfaceMockRecorder) CreateRecipient(ctx, orgID, recipientType, recipientConfig any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRecipient", reflect.TypeOf((*MockNotificationServiceInterface)(nil).CreateRecipient), ctx, orgID, recipientType, recipientConfig)
}

// DeleteRecipientByID mocks base method.
func (m *MockNotificationServiceInterface) DeleteRecipientByID(ctx context.Context, id, orgID uuid.UUID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRecipientByID", ctx, id, orgID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteRecipientByID indicates an expected call of DeleteRecipientByID.
func (mr *MockNotificationServiceInterfaceMockRecorder) DeleteRecipientByID(ctx, id, orgID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRecipientByID", reflect.TypeOf((*MockNotificationServiceInterface)(nil).DeleteRecipientByID), ctx, id, orgID)
}

// GetAllNotificationsByUserID mocks base method.
func (m *MockNotificationServiceInterface) GetAllNotificationsByUserID(ctx context.Context, userResourceID uuid.UUID, _offset, _limit *uint64, createdAtAsc bool) (services.NotificationPagination, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllNotificationsByUserID", ctx, userResourceID, _offset, _limit, createdAtAsc)
	ret0, _ := ret[0].(services.NotificationPagination)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllNotificationsByUserID indicates an expected call of GetAllNotificationsByUserID.
func (mr *MockNotificationServiceInterfaceMockRecorder) GetAllNotificationsByUserID(ctx, userResourceID, _offset, _limit, createdAtAsc any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllNotificationsByUserID", reflect.TypeOf((*MockNotificationServiceInterface)(nil).GetAllNotificationsByUserID), ctx, userResourceID, _offset, _limit, createdAtAsc)
}

// GetRecipientByID mocks base method.
func (m *MockNotificationServiceInterface) GetRecipientByID(ctx context.Context, id, orgID uuid.UUID) (services.Recipient, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecipientByID", ctx, id, orgID)
	ret0, _ := ret[0].(services.Recipient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecipientByID indicates an expected call of GetRecipientByID.
func (mr *MockNotificationServiceInterfaceMockRecorder) GetRecipientByID(ctx, id, orgID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecipientByID", reflect.TypeOf((*MockNotificationServiceInterface)(nil).GetRecipientByID), ctx, id, orgID)
}

// GetRecipientsByOrgID mocks base method.
func (m *MockNotificationServiceInterface) GetRecipientsByOrgID(ctx context.Context, orgID uuid.UUID) ([]services.Recipient, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecipientsByOrgID", ctx, orgID)
	ret0, _ := ret[0].([]services.Recipient)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecipientsByOrgID indicates an expected call of GetRecipientsByOrgID.
func (mr *MockNotificationServiceInterfaceMockRecorder) GetRecipientsByOrgID(ctx, orgID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecipientsByOrgID", reflect.TypeOf((*MockNotificationServiceInterface)(nil).GetRecipientsByOrgID), ctx, orgID)
}

// GetSubscriptionsByOrgID mocks base method.
func (m *MockNotificationServiceInterface) GetSubscriptionsByOrgID(ctx context.Context, orgID uuid.UUID) ([]services.Subscription, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSubscriptionsByOrgID", ctx, orgID)
	ret0, _ := ret[0].([]services.Subscription)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSubscriptionsByOrgID indicates an expected call of GetSubscriptionsByOrgID.
func (mr *MockNotificationServiceInterfaceMockRecorder) GetSubscriptionsByOrgID(ctx, orgID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSubscriptionsByOrgID", reflect.TypeOf((*MockNotificationServiceInterface)(nil).GetSubscriptionsByOrgID), ctx, orgID)
}

// UpdateSubscriptionsByRecipientID mocks base method.
func (m *MockNotificationServiceInterface) UpdateSubscriptionsByRecipientID(ctx context.Context, recipientID, orgID uuid.UUID, severities []acc_spec_v2.AlertSeverity) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSubscriptionsByRecipientID", ctx, recipientID, orgID, severities)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSubscriptionsByRecipientID indicates an expected call of UpdateSubscriptionsByRecipientID.
func (mr *MockNotificationServiceInterfaceMockRecorder) UpdateSubscriptionsByRecipientID(ctx, recipientID, orgID, severities any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSubscriptionsByRecipientID", reflect.TypeOf((*MockNotificationServiceInterface)(nil).UpdateSubscriptionsByRecipientID), ctx, recipientID, orgID, severities)
}
