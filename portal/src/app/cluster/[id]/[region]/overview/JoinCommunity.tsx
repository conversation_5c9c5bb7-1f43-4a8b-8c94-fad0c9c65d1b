import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUsers } from "@fortawesome/free-solid-svg-icons";
import Youtube from "@imgs/tenant/Youtube.svg";
import Github from "@imgs/tenant/Github.svg";
import { GITHUB_STARS } from "@/github_star";
import Slack from "@imgs/tenant/slack.svg";
import { Domains } from "@/config/router";
import X from "@imgs/tenant/X.svg";
import Image from "next/image";

const socialList = [
  {
    name: "slack",
    link: Domains.slack,
    value: "2.9k",
    img: Slack,
    tag: "members",
  },
  {
    name: "X",
    link: Domains.twitter,
    value: "2.8k",
    img: X,
  },
  {
    name: "Youtube",
    link: Domains.youtube,
    value: "18k",
    img: Youtube,
    tag: "views",
  },
  {
    name: "Gith<PERSON>",
    link: Domains.github,
    value: "8.1k",
    img: Github,
    tag: "stars",
  },
];

interface JoinCommunityProps {}
const JoinCommunity: React.FC<JoinCommunityProps> = (props) => {
  return (
    <div className="rounded-lg border border-neutral200 px-[15px] py-5 text-sm">
      <p className="border-b border-neutral200 pb-3 text-base">
        <FontAwesomeIcon className="mr-2" icon={faUsers} />
        Community
      </p>
      <div className="grid min-w-[270px] grid-cols-2 gap-3 pt-5">
        {socialList.map((item, index) => {
          return (
            <a
              key={index}
              className="card-hover-transition flex cursor-pointer items-center justify-center gap-3 rounded-lg border border-neutral200 px-12 py-2"
              href={item.link}
              target="_blank">
              <Image className="w-8" src={item.img} alt={item.name} />
              <div>
                <p className="text-xl">{item.value}</p>
                <p className="text-xs text-neutral500">{item.tag || "followers"}</p>
              </div>
            </a>
          );
        })}
      </div>
    </div>
  );
};
export default JoinCommunity;

