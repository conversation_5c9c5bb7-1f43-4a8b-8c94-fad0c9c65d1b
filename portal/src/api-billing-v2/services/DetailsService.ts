/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { BillingDetails } from '../models/BillingDetails';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class DetailsService {

    /**
     * Returns the billing information (the address used on invoices) of the logged in organisation.
     * @returns BillingDetails OK
     * @throws ApiError
     */
    public static getBillingDetails(): CancelablePromise<BillingDetails> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/billing/details',
        });
    }

    /**
     * Updates the billing information (the address used on invoices) of the logged in organisation. Note that this requests updates all properties even if they are omitted. In that case, the request will delete their current values.
     * @param requestBody
     * @returns any Default responses returning msg
     * @throws ApiError
     */
    public static putBillingDetails(
        requestBody: BillingDetails,
    ): CancelablePromise<{
        msg: string;
    }> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/billing/details',
            body: requestBody,
            mediaType: 'application/json',
        });
    }

}
