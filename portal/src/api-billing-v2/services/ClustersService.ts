/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { ClusterUsage } from '../models/ClusterUsage';
import type { Metric } from '../models/Metric';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class ClustersService {

    /**
     * Lists the recorded usage of the given tenant for the given metric in reverse chronological order (i.e. newest first). A result contains at most 1024 entries. The query may return less than the maximum number of entries to ensure that all nodes of the same time-frame are submitted together.
     * @param nsId
     * @param metric
     * @param maxStartTime
     * @returns ClusterUsage OK
     * @throws ApiError
     */
    public static getBillingClustersUsage(
        nsId: string,
        metric: Metric,
        maxStartTime?: string,
    ): CancelablePromise<ClusterUsage> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/billing/clusters/{nsId}/usage',
            path: {
                'nsId': nsId,
            },
            query: {
                'metric': metric,
                'max_start_time': maxStartTime,
            },
        });
    }

}
