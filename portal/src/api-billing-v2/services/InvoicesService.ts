/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { FullInvoice } from '../models/FullInvoice';
import type { GetOrgInvoicesUnpaidResult } from '../models/GetOrgInvoicesUnpaidResult';
import type { InvoiceList } from '../models/InvoiceList';
import type { Url } from '../models/Url';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class InvoicesService {

    /**
     * Lists the customer's invoices in reverse chronological order (i.e. newest first). A result contains at most 1024 entries. It does not contain preliminary invoices. The query may return less than the maximum number of entries to ensure that all invoices of the same billing cycle are submitted together.
     * @param maxCycle
     * @returns InvoiceList OK
     * @throws ApiError
     */
    public static getBillingInvoices(
        maxCycle?: number,
    ): CancelablePromise<InvoiceList> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/billing/invoices',
            query: {
                'max_cycle': maxCycle,
            },
        });
    }

    /**
     * Returns the invoice with the given ID with all its data. If the ID is omitted or empty, it returns a preliminary invoice for the current billing cycle.
     * @param invoiceId
     * @returns FullInvoice OK
     * @throws ApiError
     */
    public static getBillingInvoices1(
        invoiceId: string,
    ): CancelablePromise<FullInvoice> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/billing/invoices/{invoiceID}',
            path: {
                'invoiceID': invoiceId,
            },
        });
    }

    /**
     * Returns the customer's oldest unpaid invoice.
     * @returns GetOrgInvoicesUnpaidResult OK
     * @throws ApiError
     */
    public static getBillingInvoicesGetUnpaid(): CancelablePromise<GetOrgInvoicesUnpaidResult> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/billing/invoices/getUnpaid',
        });
    }

    /**
     * Redirects to the invoice's PDF on Stripe.
     * @param invoiceId
     * @returns Url OK
     * @throws ApiError
     */
    public static getBillingInvoicesPdf(
        invoiceId: string,
    ): CancelablePromise<Url> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/billing/invoices/{invoiceID}/pdf',
            path: {
                'invoiceID': invoiceId,
            },
        });
    }

}
