/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { BillingPeriod } from './BillingPeriod';
import type { InvoiceState } from './InvoiceState';

export type BasicInvoice = {
    stripe_id: string;
    number: string;
    org_id: string;
    billing_period: BillingPeriod;
    total_usd_cent: number;
    status: InvoiceState;
    due_date?: string;
    paid_at?: string;
};

