/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { AlertTypeArray } from '../models/AlertTypeArray';
import type { NotificationPagination } from '../models/NotificationPagination';
import type { PostSubscriptionRequestBody } from '../models/PostSubscriptionRequestBody';
import type { PutNotificationsStatusRequestBody } from '../models/PutNotificationsStatusRequestBody';
import type { PutRecipientSubscriptionRequestBody } from '../models/PutRecipientSubscriptionRequestBody';
import type { RecipientArray } from '../models/RecipientArray';
import type { SubscriptionArray } from '../models/SubscriptionArray';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class NotificationsService {

    /**
     * Get all the notifications
     * @param offset
     * @param limit
     * @param order
     * @returns NotificationPagination OK
     * @throws ApiError
     */
    public static getNotifications(
        offset?: number,
        limit: number = 50,
        order: 'createdAtAsc' | 'createdAtDesc' = 'createdAtDesc',
    ): CancelablePromise<NotificationPagination> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/notifications',
            query: {
                'offset': offset,
                'limit': limit,
                'order': order,
            },
        });
    }

    /**
     * update the status of notification
     * @param id
     * @param requestBody
     * @returns any Default responses returning msg
     * @throws ApiError
     */
    public static putNotifications(
        id: number,
        requestBody: PutNotificationsStatusRequestBody,
    ): CancelablePromise<{
        msg: string;
    }> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/notifications/{id}',
            path: {
                'id': id,
            },
            body: requestBody,
            mediaType: 'application/json',
        });
    }

    /**
     * List alert notification subscriptions in a org
     * @returns SubscriptionArray OK
     * @throws ApiError
     */
    public static getSubscriptions(): CancelablePromise<SubscriptionArray> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/subscriptions',
        });
    }

    /**
     * create new alert subscription in a org
     * @param requestBody
     * @returns any Default responses returning msg
     * @throws ApiError
     */
    public static postSubscriptions(
        requestBody: PostSubscriptionRequestBody,
    ): CancelablePromise<{
        msg: string;
    }> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/subscriptions',
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `400 Bad Request`,
            },
        });
    }

    /**
     * delete notification subscription in a org
     * @param subId
     * @returns any Default responses returning msg
     * @throws ApiError
     */
    public static deleteSubscriptions(
        subId: string,
    ): CancelablePromise<{
        msg: string;
    }> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/subscriptions/{subId}',
            path: {
                'subId': subId,
            },
            errors: {
                400: `400 Bad Request`,
                404: `404 Not Found`,
            },
        });
    }

    /**
     * List alert notification recipients in a org
     * @returns RecipientArray OK
     * @throws ApiError
     */
    public static getRecipients(): CancelablePromise<RecipientArray> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/recipients',
        });
    }

    /**
     * delete notification recipient in a org
     * @param recipientId
     * @returns any Default responses returning msg
     * @throws ApiError
     */
    public static deleteRecipients(
        recipientId: string,
    ): CancelablePromise<{
        msg: string;
    }> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/recipients/{recipientId}',
            path: {
                'recipientId': recipientId,
            },
            errors: {
                400: `400 Bad Request`,
                404: `404 Not Found`,
            },
        });
    }

    /**
     * put notification subscription for a recipient in a org
     * @param recipientId
     * @param requestBody
     * @returns any Default responses returning msg
     * @throws ApiError
     */
    public static putRecipientsSubscriptions(
        recipientId: string,
        requestBody: PutRecipientSubscriptionRequestBody,
    ): CancelablePromise<{
        msg: string;
    }> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/recipients/{recipientId}/subscriptions',
            path: {
                'recipientId': recipientId,
            },
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `400 Bad Request`,
                404: `404 Not Found`,
            },
        });
    }

    /**
     * List all available alert types
     * @returns AlertTypeArray OK
     * @throws ApiError
     */
    public static getAlertTypes(): CancelablePromise<AlertTypeArray> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/alertTypes',
        });
    }

    /**
     * send a test message to recipient
     * @param recipientId
     * @returns any Default responses returning msg
     * @throws ApiError
     */
    public static postRecipientsTest(
        recipientId: string,
    ): CancelablePromise<{
        msg: string;
    }> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/recipients/{recipientId}/test',
            path: {
                'recipientId': recipientId,
            },
            errors: {
                400: `400 Bad Request`,
            },
        });
    }

}
