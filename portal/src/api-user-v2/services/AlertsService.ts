/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { AlertTypeArray } from '../models/AlertTypeArray';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class AlertsService {

    /**
     * List all available alert types
     * @returns AlertTypeArray OK
     * @throws ApiError
     */
    public static getAlertTypes(): CancelablePromise<AlertTypeArray> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/alertTypes',
        });
    }

    /**
     * send a test message to recipient
     * @param recipientId
     * @returns any Default responses returning msg
     * @throws ApiError
     */
    public static postRecipientsTest(
        recipientId: string,
    ): CancelablePromise<{
        msg: string;
    }> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/recipients/{recipientId}/test',
            path: {
                'recipientId': recipientId,
            },
            errors: {
                400: `400 Bad Request`,
            },
        });
    }

}
