/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { TenantExtensions } from './TenantExtensions';
import type { TenantResource } from './TenantResource';
import type { TierId } from './TierId';

export type Tenant = {
    id: number;
    userId: number;
    orgId: string;
    tenantName: string;
    region: string;
    resources: TenantResource;
    status: Tenant.status;
    tier: TierId;
    usageType: Tenant.usageType;
    imageTag: string;
    latestImageTag: string;
    rw_config: string;
    etcd_config: string;
    health_status: Tenant.health_status;
    createdAt: string;
    updatedAt: string;
    nsId: string;
    clusterName?: string;
    upcomingSnapshotTime?: string;
    extensions?: TenantExtensions;
};

export namespace Tenant {

    export enum status {
        CREATING = 'Creating',
        RUNNING = 'Running',
        DELETING = 'Deleting',
        FAILED = 'Failed',
        STOPPED = 'Stopped',
        STOPPING = 'Stopping',
        STARTING = 'Starting',
        EXPIRED = 'Expired',
        CONFIG_UPDATING = 'ConfigUpdating',
        UPGRADING = 'Upgrading',
        UPDATING = 'Updating',
        SNAPSHOTTING = 'Snapshotting',
        EXTENSION_COMPACTION_ENABLING = 'ExtensionCompactionEnabling',
        EXTENSION_COMPACTION_DISABLING = 'ExtensionCompactionDisabling',
        EXTENSION_SERVERLESS_BACKFILL_ENABLING = 'ExtensionServerlessBackfillEnabling',
        EXTENSION_SERVERLESS_BACKFILL_UPDATE = 'ExtensionServerlessBackfillUpdate',
        EXTENSION_SERVERLESS_BACKFILL_DISABLING = 'ExtensionServerlessBackfillDisabling',
        META_MIGRATING = 'MetaMigrating',
        RESTORING = 'Restoring',
    }

    export enum usageType {
        GENERAL = 'general',
        PIPELINE = 'pipeline',
    }

    export enum health_status {
        UNKNOWN = 'Unknown',
        HEALTHY = 'Healthy',
        UNHEALTHY = 'Unhealthy',
    }


}

