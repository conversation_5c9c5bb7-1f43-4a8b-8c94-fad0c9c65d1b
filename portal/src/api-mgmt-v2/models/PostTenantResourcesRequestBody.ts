/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { ComponentResourceRequest } from './ComponentResourceRequest';
import type { TenantExtensionsRequest } from './TenantExtensionsRequest';

export type PostTenantResourcesRequestBody = {
    meta?: ComponentResourceRequest;
    frontend?: ComponentResourceRequest;
    compute?: ComponentResourceRequest;
    compactor?: ComponentResourceRequest;
    standalone?: ComponentResourceRequest;
    extensions?: TenantExtensionsRequest;
};

