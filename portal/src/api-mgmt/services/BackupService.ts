/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { BackupSnapshotsSizePage } from '../models/BackupSnapshotsSizePage';
import type { PostSnapshotResponseBody } from '../models/PostSnapshotResponseBody';

import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';

export class BackupService {

    /**
     * @param tenantId
     * @param offset
     * @param limit
     * @returns BackupSnapshotsSizePage Get all available backup snapshots.
     * @throws ApiError
     */
    public static getTenantBackup(
        tenantId: number,
        offset?: number,
        limit?: number,
    ): CancelablePromise<BackupSnapshotsSizePage> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/tenant/{tenantId}/backup',
            path: {
                'tenantId': tenantId,
            },
            query: {
                'offset': offset,
                'limit': limit,
            },
        });
    }

    /**
     * @param tenantId
     * @returns PostSnapshotResponseBody The backup procedure is started.
     * @throws ApiError
     */
    public static postTenantBackup(
        tenantId: number,
    ): CancelablePromise<PostSnapshotResponseBody> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/tenant/{tenantId}/backup',
            path: {
                'tenantId': tenantId,
            },
            errors: {
                409: `The tenant status should be running before starting the backup procedure.`,
            },
        });
    }

    /**
     * @param tenantId
     * @param snapshotId
     * @returns any The snapshot deletion procedure is started.
     * @throws ApiError
     */
    public static deleteTenantBackup(
        tenantId: number,
        snapshotId: string,
    ): CancelablePromise<any> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/tenant/{tenantId}/backup/{snapshotId}',
            path: {
                'tenantId': tenantId,
                'snapshotId': snapshotId,
            },
        });
    }

    /**
     * @param tenantId
     * @param snapshotId
     * @returns any Starting to restore data from this snapshot.
     * @throws ApiError
     */
    public static postTenantBackupRestore(
        tenantId: number,
        snapshotId: string,
    ): CancelablePromise<any> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/tenant/{tenantId}/backup/{snapshotId}/restore',
            path: {
                'tenantId': tenantId,
                'snapshotId': snapshotId,
            },
        });
    }

}
