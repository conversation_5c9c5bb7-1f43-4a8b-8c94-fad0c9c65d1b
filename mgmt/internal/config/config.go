package config

import (
	"fmt"
	"os"
	"regexp"

	"github.com/risingwavelabs/eris"

	"gopkg.in/yaml.v3"

	"github.com/risingwavelabs/risingwave-cloud/shared/configutil"
	"github.com/risingwavelabs/risingwave-cloud/shared/logger"
	mtlsclient "github.com/risingwavelabs/risingwave-cloud/shared/mtls/client"
	mtlsserver "github.com/risingwavelabs/risingwave-cloud/shared/mtls/server"
)

type h = map[string]any
type arr = []string

type Config struct {
	Region     string
	APIService struct {
		Mode            string
		Port            int
		AdminPort       int
		ZpagesPort      int
		JwtSecret       string
		IdleTimeoutSecs int
		ReadTimeoutSecs int
	}
	WorkflowEngine struct {
		PollRWDBInterval              int // in second
		GenDiagReportIntervalMinutes  int
		DisableDiagReport             bool   `yaml:"disableDiagReport"`
		DisableDiagReportVersionRegex string `yaml:"disableDiagReportVersionRegex"`
		ZpagesPort                    int
		GarbageCollectionConcurrency  *int
		GarbageCollectionRetryLimit   *int
		EngineConfig                  struct {
			WorkflowRetentionDay  int `yaml:"workflowRetentionDay"`
			EventRetentionCount   int `yaml:"eventRetentionCount"`
			CleanupIntervalMinute int `yaml:"cleanupIntervalMinute"`
			EventCleanupCount     int `yaml:"eventCleanupCount"`
		} `yaml:"engineConfig"`
	}
	Mgmt struct {
		Etcd struct {
			Chart         string // must be https://charts.bitnami.com/bitnami/etcd-<version>.tgz
			StorageClass  string
			Values        map[string]interface{}
			DefaultConfig map[string]string
		}
		Log     logger.LogConfig
		Account struct {
			AdminURL        string
			ClientTLSOption mtlsclient.TLSOption
		}
		VMAlert struct {
			APIServerURL string `yaml:"apiserverurl"`
		} `yaml:"vmalert"`
		RisingWave struct {
			Repository string
			Tag        string
			Password   string
			// default labels for the RisingWave namespace
			// all RisingWave related resources are placed
			// in the RisingWave namespace. So we can use
			// labels to filter the namespace we want to
			// do garbage collection.
			DefaultLabels  map[string]string
			JwksURL        string
			SQLTimeoutSecs int

			PgMetaStoreAvailableVersion string `yaml:"PgMetaStoreAvailableVersion"`
		}
		AdminTLSOption               mtlsserver.TLSOption
		ManagedClusterAgentTLSOption mtlsclient.TLSOption
		Infrastructure               struct {
			CloudProvider string
			Aws           struct {
				AccountID           string
				Zones               string // comma-separated list
				BYOCSubnetIDs       string // comma-separated list
				BYOCSecurityGroupID string
				VPCID               string
			}
			Gcp struct {
				ProjectID          string
				Zones              string // comma-separated list
				VPCSelfLink        string
				BYOCSubnetSelfLink string
				BYOCTelemetryGroup string
			}
			Azr struct {
				SubscriptionID                  string
				TenantID                        string
				Zones                           string // comma-separated list
				APIServiceClientID              string
				BYOCSubnetID                    string
				BYOCResourceGroup               string
				BYOCControlPlaneSecurityGroupID string
				BYOCTelemetrySecurityGroupID    string
			}
			RwDiag struct {
				Bucket            string
				AzrStorageAccount string
			}
		}
		Byoc struct {
			ClusterVersion                  string
			UseStagingRepo                  bool
			ApplyGracefulShutdownPeriodMins int
			ApplyLockExpirationDurationMins int
		}
		Backup struct {
			AutoBackupMaximumDeletableSnapshots *int `yaml:"AutoBackupMaximumDeletableSnapshots,omitempty"`
			AutoBackupIntervalMinutes           int  `yaml:"AutoBackupIntervalMinutes"`
			PeriodicalBackupRetentionMins       *int `yaml:"PeriodicalBackupRetentionMins,omitempty"`
			OperationalBackupRetentionMins      *int `yaml:"OperationalBackupRetentionMins,omitempty"`
		}
		PodManagement PodManagement `yaml:"PodManagement"`
		LogAPI        struct {
			ClientTLSOption mtlsclient.TLSOption
		}
		RisingWaveExtensions struct {
			Compaction         RisingWaveExtensionsCompaction
			ServerlessBackfill RisingWaveExtensionsServerlessBackfill
		}
		CloudAgent struct {
			MaxRecvMsgSize int
		}
		AssetPath string
	}
	Msdb struct {
		Host     string
		Port     int
		User     string
		Password string
		Db       string
		SSLMode  string
	}
	MsdbConnection struct {
		MaxConns             int32 `yaml:"maxConns"`
		QueryTimeoutMs       int32 `yaml:"queryTimeoutMs"`
		UpdateTimeoutMs      int32 `yaml:"updateTimeoutMs"`
		DeleteTimeoutMs      int32 `yaml:"deleteTimeoutMs"`
		UpsertTimeoutMs      int32 `yaml:"upsertTimeoutMs"`
		CreateTimeoutMs      int32 `yaml:"createTimeoutMs"`
		TransactionTimeoutMs int32 `yaml:"transactionTimeoutMs"`
	}
	Features struct {
		// if true the serverless backfill extension can be enabled
		// if false requests to enable, update, or disable the extension will be rejected
		ServerlessBackfillExtension  bool
		AuditLog                     bool
		MetaMigration                bool `yaml:"metaMigration"`
		ConfigSet                    bool `yaml:"configSet"`
		TierEnforcement              bool `yaml:"tierEnforcement"`
		SupportVariousCPUMemoryRatio bool `yaml:"supportVariousCPUMemoryRatio"`
	}
	AppTokens struct {
		RisingwaveLicenseKey                  string
		RisingwaveStandardTierLicenseKey      string `yaml:"risingwavestandardtierlicensekey"`
		RisingwaveAdvancedTierLicenseKey      string `yaml:"risingwaveadvancedtierlicensekey"`
		RisingwaveSecretStoreKeyEncryptionKey string
	}
}

var (
	Conf = &Config{}
	// This is separated because the [issue](https://github.com/go-yaml/yaml/issues/125) is still not fixed.
	ResourcesDefinitions = &ResourcesDef{
		Tiers:          map[string]Tier{},
		ComponentTypes: map[string]ComponentType{},
	}
	PodManagementDefinitions = &PodManagement{}
)

func initResourceDefIfExists(resourceDefinitionPath string) error {
	if resourceDefinitionPath != "" {
		resourceDefRaw, err := os.ReadFile(resourceDefinitionPath)
		if err != nil {
			return eris.Wrap(err, "failed to read resource definition file")
		}
		err = yaml.Unmarshal(resourceDefRaw, ResourcesDefinitions)
		if err != nil {
			return err
		}
	} else {
		logger.L().Warn("resource definition is not set")
	}
	return nil
}

func initPodManagementDefIfExists(podManagementDefinitionPath string) error {
	if podManagementDefinitionPath != "" {
		err := configutil.UnmarshalJSONConfigs(podManagementValidator, PodManagementDefinitions, map[string]string{
			podManagementDefinitionPath: "",
		})
		if err != nil {
			return eris.Wrap(err, "failed to read resource definition file")
		}
		Conf.Mgmt.PodManagement = *PodManagementDefinitions
	} else {
		PodManagementDefinitions = nil
		logger.L().Warn("pod management definition is not set")
	}
	return nil
}

func Init(configPath, msdbCredentialPath, resourceDefinitionPath, podManagementDefinitionPath, appTokensPath string) error {
	if err := initResourceDefIfExists(resourceDefinitionPath); err != nil {
		return err
	}

	configPathMap := map[string]string{
		// the file content of msdbCredentialPath will be mounted to Conf["msdb"]
		// the file content of configPath will be merged to Conf
		msdbCredentialPath: "msdb",
		configPath:         "",
	}
	// TODO: CLOUD-3262
	if appTokensPath != "" {
		configPathMap[appTokensPath] = "apptokens"
	} else {
		logger.L().Warn("app tokens is not set")
	}

	if err := configutil.UnmarshalJSONConfigs(validator, Conf, configPathMap); err != nil {
		return err
	}

	if err := initPodManagementDefIfExists(podManagementDefinitionPath); err != nil {
		return err
	}

	return validateParsedConfig(Conf)
}

func validateParsedConfig(cfg *Config) error {
	if cfg.WorkflowEngine.DisableDiagReportVersionRegex != "" {
		_, err := regexp.Compile(cfg.WorkflowEngine.DisableDiagReportVersionRegex)
		if err != nil {
			return err
		}
	}
	return nil
}

type ClusterCredential struct {
	Name     string
	EndPoint string
	Token    string
}

// cluster name to credential.
var ClusterCredentials = map[string]ClusterCredential{}

func InitClusterCredentials(clusterDir string) error {
	clusters, err := configutil.UnmarshalJSONConfigDir[ClusterCredential](clusterDir, "cluster_", clusterValidator)
	if err != nil {
		return eris.Wrapf(err, "failed to unmarshal dir: %s", clusterDir)
	}
	for _, c := range clusters {
		ClusterCredentials[c.Name] = c
	}
	fmt.Printf("Cluster infos intialized: %v\n", ClusterCredentials)
	return nil
}
