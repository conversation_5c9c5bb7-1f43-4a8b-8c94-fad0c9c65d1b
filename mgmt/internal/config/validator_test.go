package config

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/risingwavelabs/risingwave-cloud/shared/configutil"
)

func TestValidator_normal(t *testing.T) {
	data :=
		`region: local
apiservice:
  jwtsecret: local-test-secret
  mode: debug
  port: 8080
  adminport: 8081
  zpagesport: 8090
  idletimeoutsecs: 30
  readtimeoutsecs: 30
workflowengine:
  zpagesport: 8390
  pollrwdbinterval: 45
  garbagecollectionconcurrency: 10
  garbagecollectionretrylimit: 3
  gendiagreportintervalminutes: 30
mgmt:
  etcd:
    chart: "https://charts.bitnami.com/bitnami/etcd-8.5.10.tgz"
    storageclass: "standard"
    values:
      image:
        debug: true
      logLevel: debug
  account:
    adminurl: http://localhost:8181/api/v1
  log:
    level: info
    encoder: json
  risingwave:
    repository: ghcr.io/risingwavelabs/risingwave
    tag: nightly-********
    password: risingwave
    jwksurl: http://localhost:8180/api/v1/jwks
    defaultlabels:
      k1: v1
      k2: v2
    sqltimeoutsecs: 5
  admintlsoption:
    server:
      certpath: randompath
      keypath: randompath
    client:
      cacertpath: randompath
  managedclusteragenttlsoption:
    client:
      certpath: randompath
      keypath: randompath
    server:
      cacertpath: randompath
  infrastructure:
    cloudprovider: local
    aws:
      accountid: randomid
      zones: a,b,c
      byocsubnetids: a,b,c
      byocsecuritygroupid: randomid
      vpcid: randomid
    gcp:
      projectid: randomeid
      zones: a,b,c
      vpcselflink: randomlink
      byocsubnetselflink: randomlink
      byoctelemetrygroup: randomgroup
    azr:
      subscriptionid: randomid
      tenantid: randomid
      zones: a,b,c
      apiserviceclientid: randomid
      byocsubnetid: randomlink
      byocresourcegroup: rg
      byoccontrolplanesecuritygroupid: sg
      byoctelemetrysecuritygroupid: sg
    rwdiag:
      bucket: rw-diag-bucket
      azrstorageaccount: azr-rw-diag-sa
  byoc:
    clusterversion: v999.0.0
    usestagingrepo: true
    applygracefulshutdownperiodmins: 20
    applylockexpirationdurationmins: 30
  backup:
    AutoBackupMaximumDeletableSnapshots: 3
    AutoBackupIntervalMinutes: 30
    PeriodicalBackupRetentionMins: 0
    OperationalBackupRetentionMins: 10080
  PodManagement:
    Frontend:
      PodAffinity:
        MatchExpressions:
          - Key: app
            Operator: In
            Values: ["compute"]
            Effect: NoSchedule
        TopologyKey: kubernetes.io/hostname
      NodeToleration:
        - Key: node-role.kubernetes.io/compute
          Operator: Exists
          Effect: NoSchedule
  logapi:
    clienttlsoption:
      client:
        certpath: randompath
        keypath: randompath
      server:
        cacertpath: randompath
  risingwaveextensions:
    compaction:
      chart:
        url: "https://risingwavelabs.github.io/risingwave-extensions/charts/"
        tag: "risingwave-extensions-0.1.0.tgz"
      image:
        repository: "ghcr.io/risingwavelabs/risingwave-extensions"
      compactor:
        replica: 1
        metaendpoint: "risingwave-meta:5690"
        configmap: "risingwave-default-config"
        cpurequest: "500m"
        cpulimit: "1000m"
        memoryrequest: "512Mi"
        memorylimit: "1Gi"
      scaler:
        pollinginterval: 15
        collectinterval: 1
        scaledowntozerorequiredtimes: 10
        scaledowntonrequiredtimes: 10
        cooldownperiod: 30
        minreplicas: 0
        maxreplicas: 5
        desiredreplicas: 0
        defaultparallelism: 8
      imagemap:
        v1.10.0: "v1.10.1"
        v1.11.0: "v1.11.2"
        v1.12.0: "v1.12.3"
    serverlessbackfill:
      setsbcaddr: true
      chart:
        url: "https://risingwavelabs.github.io/serverless-backfill-controller/charts/serverless-backfill-controller"
        tag: "2.0.0"
      enabledbydefault: false
      resources:
        cpurequest: "500m"
        cpulimit: "1000m"
        memoryrequest: "512Mi"
        memorylimit: "1Gi"
      affinity:
        nodeaffinity:
          requiredduringschedulingignoredduringexecution:
            nodeselectorterms:
            - matchexpressions:
              - key: rw-workload-type
                operator: In
                values:
                  - meta-front-workload
      tolerations:
        - effect: "NoSchedule"
          key: "rw-type"
          operator: "Equal"
          value: "meta-front-workload"
  cloudagent:
    maxrecvmsgsize: 1024
  assetpath: "test-asset-path"
msdb:
  host: localhost
  port: 5432
  user: rwc_root
  password: postgres
  db: msdb
  sslmode: disable
pgxpoolconfig:
  maxConns: 40
features:
  metaMigration: false
apptokens:
  risingwavelicensekey: test-risingwave-license-key
  risingwavestandardtierlicensekey: test-risingwave-standard-tier-license-key
  risingwaveadvancedtierlicensekey: test-risingwave-advanced-tier-license-key
  risingwavesecretstorekeyencryptionkey: test-key-encryption-key
`
	res, err := configutil.ValidateExactly(data, validator)
	require.NoError(t, err)
	require.True(t, res.Valid, fmt.Sprintf("the result should be valid: %v", res))
}

func TestValidator_error(t *testing.T) {
	data :=
		`region: local
apiservice:
  jwtsecret: local-test-secret
  mode: debug
  port: 8080
  adminport: 8081
  zpagesport: 8090
workflowengine:
  pollrwdbinterval: 45
mgmt:
  etcd:
    chart: "https://charts.bitnami.com/bitnami/etcd-8.5.10.tgz"
    storageclass: "standard"
    values:
      image:
        debug: true
      logLevel: debug
  feature: "constant://?val=register%3Dtrue"
  accounturl: http://localhost:8180/api/v1
  additionalfieldshereinmgmt: test
  log:
    level: info
    encoder: json
  risingwave:
    repository: ghcr.io/risingwavelabs/risingwave
    password: risingwave
  tlsoption:
    server:
      certpath: randompath
      keypath: randompath
    client:
      cacertpath: randompath
msdb:
  host: localhost
  port: 5432
  user: rwc_root
  password: postgres
  db: msdb`
	res, err := configutil.ValidateFully(data, validator)
	require.NoError(t, err)
	require.False(t, res.Valid)
	t.Log(res.Message)
}
