package config

import (
	cu "github.com/risingwavelabs/risingwave-cloud/shared/configutil"
	"github.com/risingwavelabs/risingwave-cloud/shared/logger"
	"github.com/risingwavelabs/risingwave-cloud/shared/mtls/client"
	"github.com/risingwavelabs/risingwave-cloud/shared/mtls/server"
)

var matchExpressionsValidator = cu.ObjectPartially(nil, h{
	"Key":      cu.StringType,
	"Operator": cu.StringType,
	"Values":   cu.Array(cu.StringType),
	"Effect":   cu.StringType,
})

var matchNodeSelectorRequirement = cu.ObjectExactly(h{
	"key":      cu.StringType,
	"operator": cu.StringType,
	"values":   cu.Array(cu.StringType),
})

var propertiesValidator = cu.ObjectPartially(nil, h{
	"PodLabel":     cu.Array(matchExpressionsValidator),
	"NodeAffinity": cu.Array(matchExpressionsValidator),
	"PodAffinity": cu.ObjectExactly(h{
		"MatchExpressions": cu.Array(matchExpressionsValidator),
		"TopologyKey":      cu.StringType,
	}),
	"NodeToleration": cu.Array(matchExpressionsValidator),
})

var validator = func() h {
	return h{
		"$id":         "https://github.com/risingwave-cloud/mgmt/internal/config.json",
		"title":       "Mgmt config",
		"description": "configuration file for both API service and workflow engine",
		"type":        "object",
		"anyOf": []h{
			{"required": []string{"msdb", "mgmt", "region", "apiservice"}},
			{"required": []string{"msdb", "mgmt", "region", "workflowengine"}},
		},
		"properties": h{
			"region": cu.StringType,
			"apiservice": cu.ObjectPartially(
				/* required */ h{
					"mode":       cu.StringType,
					"port":       cu.PortType,
					"adminport":  cu.PortType,
					"zpagesport": cu.PortType,
					"jwtsecret":  cu.StringType,
				},
				/* optional */ h{
					"idletimeoutsecs": h{"type": "integer"},
					"readtimeoutsecs": h{"type": "integer"},
				},
			),
			"workflowengine": cu.ObjectPartially(
				/* required */ h{
					"pollrwdbinterval":             cu.IntType,
					"gendiagreportintervalminutes": cu.IntType,
					"zpagesport":                   cu.PortType,
				},
				/* optional */ h{
					"garbagecollectionconcurrency": h{"type": "integer"},
					"garbagecollectionretrylimit":  h{"type": "integer"},
					"engineConfig": cu.ObjectExactly(h{
						"workflowRetentionDay":  cu.IntType,
						"eventRetentionCount":   cu.IntType,
						"cleanupIntervalMinute": cu.IntType,
						"eventCleanupCount":     cu.IntType,
					}),
				},
			),
			"mgmt": cu.ObjectPartially(h{
				"assetpath": cu.StringType,
				"etcd": cu.ObjectPartially(
					/* required */ h{
						"chart":        cu.StringType,
						"storageclass": cu.StringType,
					},
					/* optional */ h{
						"values":        h{"type": "object"},
						"defaultconfig": h{"type": "object"},
					},
				),
				"log": logger.Validator(),
				"risingwave": cu.ObjectPartially(
					/* required */ h{
						"repository": cu.StringType,
						"tag":        cu.StringType,
						"password":   cu.StringType,
					},
					/* optional */ h{
						"jwksurl":                      h{"type": "string"},
						"defaultlabels":                h{"type": "object"},
						"sqltimeoutsecs":               h{"type": "integer"},
						"PgMetaStoreAvailableVersions": h{"type": "string"},
					},
				),
			}, h{
				"admintlsoption": server.TLSOptionValidator(),
				"account": cu.ObjectPartially(nil, h{
					"adminurl":        h{"type": "string"},
					"clienttlsoption": client.TLSOptionValidator(),
				}),
				"vmalert": cu.ObjectExactly(h{
					"apiserverurl": cu.StringType,
				}),
				"managedclusteragenttlsoption": client.TLSOptionValidator(),
				"infrastructure": cu.ObjectPartially(h{
					"cloudprovider": cu.StringType,
				}, h{
					"rwdiag": cu.ObjectPartially(
						/* required */ h{
							"bucket": cu.StringType,
						},
						/* optional */ h{
							"azrstorageaccount": cu.StringType,
						},
					),
					"aws": cu.ObjectExactly(h{
						"accountid":           cu.StringType,
						"zones":               cu.StringType,
						"byocsubnetids":       cu.StringType,
						"byocsecuritygroupid": cu.StringType,
						"vpcid":               cu.StringType,
					}),
					"gcp": cu.ObjectExactly(h{
						"projectid":          cu.StringType,
						"zones":              cu.StringType,
						"vpcselflink":        cu.StringType,
						"byocsubnetselflink": cu.StringType,
						"byoctelemetrygroup": cu.StringType,
					}),
					"azr": cu.ObjectExactly(h{
						"subscriptionid":                  cu.StringType,
						"tenantid":                        cu.StringType,
						"zones":                           cu.StringType,
						"apiserviceclientid":              cu.StringType,
						"byocsubnetid":                    cu.StringType,
						"byocresourcegroup":               cu.StringType,
						"byoccontrolplanesecuritygroupid": cu.StringType,
						"byoctelemetrysecuritygroupid":    cu.StringType,
					}),
				}),
				"byoc": cu.ObjectPartially(h{
					"clusterversion": cu.StringType,
					"usestagingrepo": cu.BooleanType,
				}, h{
					"applygracefulshutdownperiodmins": cu.IntType,
					"applylockexpirationdurationmins": cu.IntType,
				}),
				"backup": cu.ObjectPartially(nil, h{
					"AutoBackupMaximumDeletableSnapshots": cu.IntType,
					"AutoBackupIntervalMinutes":           cu.IntType,
					"PeriodicalBackupRetentionMins":       cu.IntType,
					"OperationalBackupRetentionMins":      cu.IntType,
				}),
				"PodManagement": cu.ObjectPartially(nil, h{
					"Compute":   propertiesValidator,
					"Meta":      propertiesValidator,
					"Frontend":  propertiesValidator,
					"Connector": propertiesValidator,
					"Compactor": propertiesValidator,
					"Etcd":      propertiesValidator,
				}),
				"logapi": cu.ObjectPartially(nil, h{
					"clienttlsoption": client.TLSOptionValidator(),
				}),
				"risingwaveextensions": cu.ObjectPartially(nil, h{
					"serverlessbackfill": cu.ObjectPartially(h{
						/* required */
						"chart": cu.ObjectExactly(h{
							"url": cu.StringType,
							"tag": cu.StringType,
						}),
						/* required */
						"enabledbydefault": cu.BooleanType,
						"setsbcaddr":       cu.BooleanType,
					}, h{
						/* optional */
						"affinity": cu.ObjectPartially(nil, h{
							"nodeaffinity": cu.ObjectExactly(h{
								"requiredduringschedulingignoredduringexecution": cu.ObjectExactly(h{
									"nodeselectorterms": cu.Array(cu.ObjectPartially(nil, h{
										"matchexpressions": cu.Array(matchNodeSelectorRequirement),
									})),
								}),
							}),
						}),
						"resources": cu.ObjectExactly(h{
							"cpurequest":    cu.StringType,
							"cpulimit":      cu.StringType,
							"memoryrequest": cu.StringType,
							"memorylimit":   cu.StringType,
						}),
						"tolerations": cu.Array(cu.ObjectPartially(nil, h{
							"effect":   cu.StringType,
							"key":      cu.StringType,
							"operator": cu.StringType,
							"value":    cu.StringType,
						})),
					}),
					"compaction": cu.ObjectPartially(
						nil, h{
							/* required */
							"chart": cu.ObjectExactly(h{
								"url": cu.StringType,
								"tag": cu.StringType,
							}),
							"image": cu.ObjectExactly(h{
								"repository": cu.StringType,
							}),
							"compactor": cu.ObjectExactly(h{
								"replica":       cu.IntType,
								"metaendpoint":  cu.StringType,
								"configmap":     cu.StringType,
								"cpurequest":    cu.StringType,
								"cpulimit":      cu.StringType,
								"memoryrequest": cu.StringType,
								"memorylimit":   cu.StringType,
							}),
							/* optional */
							"scaler": cu.ObjectPartially(nil, h{
								"pollinginterval":              cu.IntType,
								"collectinterval":              cu.IntType,
								"scaledowntozerorequiredtimes": cu.IntType,
								"scaledowntonrequiredtimes":    cu.IntType,
								"cooldownperiod":               cu.IntType,
								"minreplicas":                  cu.IntType,
								"maxreplicas":                  cu.IntType,
								"desiredreplicas":              cu.IntType,
								"defaultparallelism":           cu.IntType,
							}),
							"imagemap": cu.Map(cu.StringType),
						}),
				}),
				"cloudagent": cu.ObjectPartially(
					/* required */ h{},
					/* optional */ h{
						"maxrecvmsgsize": h{"type": "integer"},
					},
				),
			}),
			"msdb": cu.ObjectExactly(h{
				"host":     cu.StringType,
				"port":     cu.PortType,
				"user":     cu.StringType,
				"password": cu.StringType,
				"db":       cu.StringType,
				"sslmode":  cu.StringEnumType(arr{"disable", "require", "verify-ca", "verify-full"}),
			}),
			"pgxpoolconfig": cu.ObjectExactly(h{
				"maxConns": cu.IntType,
			}),
			"features": cu.ObjectPartially(
				h{},
				h{
					"auditlog":                    cu.BooleanType,
					"metaMigration":               cu.BooleanType,
					"serverlessBackfillExtension": cu.BooleanType,
				},
			),
			"apptokens": cu.ObjectExactly(h{
				"risingwavelicensekey":                  cu.StringType,
				"risingwavestandardtierlicensekey":      cu.StringType,
				"risingwaveadvancedtierlicensekey":      cu.StringType,
				"risingwavesecretstorekeyencryptionkey": cu.StringType,
			}),
		},
	}
}

var clusterValidator = func() h {
	return h{
		"$id":         "https://github.com/risingwave-cloud/mgmt/internal/cluster_credential.json",
		"title":       "Cluster credential",
		"description": "configuration file for cluster crednetial",
		"type":        "object",
		"required":    []string{"endpoint", "name", "token"},
		"properties": h{
			"endpoint": cu.StringType,
			"name":     cu.StringType,
			"token":    cu.StringType,
		},
	}
}

var podManagementValidator = func() h {
	return h{
		"$id":         "https://github.com/risingwave-cloud/mgmt/internal/pod_management.json",
		"title":       "Pod management",
		"description": "configuration file for pod management",
		"type":        "object",
		"properties": h{
			"Compute":   propertiesValidator,
			"Meta":      propertiesValidator,
			"Frontend":  propertiesValidator,
			"Connector": propertiesValidator,
			"Compactor": propertiesValidator,
			"Etcd":      propertiesValidator,
		},
	}
}
