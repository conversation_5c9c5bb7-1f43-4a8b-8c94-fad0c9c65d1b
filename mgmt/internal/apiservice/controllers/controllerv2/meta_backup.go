package controllerv2

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/dto"
	"github.com/risingwavelabs/risingwave-cloud/internal/apiservice/services"
	"github.com/risingwavelabs/risingwave-cloud/internal/config"
	ginauth "github.com/risingwavelabs/risingwave-cloud/shared/ginx/auth"
	"github.com/risingwavelabs/risingwave-cloud/shared/namespace"
	"github.com/risingwavelabs/risingwave-cloud/shared/paging"

	"github.com/risingwavelabs/risingwave-cloud/shared/ginx"

	spec "github.com/risingwavelabs/risingwave-cloud/shared/spec/mgmt_spec_v2"
)

func (controller *UserController) GetTenantsNsIdBackups(c *gin.Context, nsID uuid.UUID, params spec.GetTenantsNsIdBackupsParams) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, eris.Wrap(err, "unable to get auth info: "))
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}

	pager := paging.NewPager(20, 100)
	page := pager.Paging(params.Offset, params.Limit)

	res, err := controller.metaBackupService.GetAllMetaSnapshots(c, tenant.ID, int32(page.Offset), int32(page.Limit))
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	cnt, err := controller.metaBackupService.GetAllMetaSnapshotsCount(c, tenant.ID)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	resSpec, err := toBackupSnapshotsSpec(res)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	rtn := spec.BackupSnapshotsPagination{
		Items: resSpec,
		Pagination: &spec.Pagination{
			Limit:  page.Limit,
			Offset: page.Offset,
			Size:   cnt,
		},
	}

	c.JSON(http.StatusOK, rtn)
}

func (controller *UserController) PostTenantsNsIdBackups(c *gin.Context, nsID uuid.UUID) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, eris.Wrap(err, "unable to get auth info: "))
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}

	workflowID, snapshotID, err := controller.metaBackupService.StartMetaBackup(c, tenant.ID)
	if eris.GetCode(err) == eris.CodeFailedPrecondition {
		ginx.FailedPreconditionError(c, err)
		return
	}
	if err != nil {
		ginx.InternalError(c, err)
		return
	}
	ginx.Accepted(c, spec.PostSnapshotResponseBody{WorkflowId: *workflowID, SnapshotId: *snapshotID}, "snapshot creation is in progress")
}

func (controller *UserController) DeleteTenantsNsIdBackupsSnapshotId(c *gin.Context, nsID uuid.UUID, snapshotID uuid.UUID) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, eris.Wrap(err, "unable to get auth info: "))
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsID)
	if err != nil {
		TenantNotFoundOrInternalErrorResponse(c, err)
		return
	}

	_, err = controller.metaBackupService.StartDeleteSnapshot(c, tenant.ID, snapshotID)
	if err != nil {
		if eris.Is(err, services.ErrSnapshotCreating) {
			ginx.FailedPreconditionf(c, "snapshot is creating")
			return
		}
		if eris.Is(err, services.ErrSnapshotDeleting) {
			ginx.Accepted(c, nil, "snapshot deletion is already in progress")
			return
		}
		ginx.InternalError(c, err)
		return
	}
	ginx.Accepted(c, nil, "snapshot deletion is in progress")
}

func (controller *UserController) PostTenantsNsIdBackupsSnapshotIdRestore(c *gin.Context, nsID uuid.UUID, snapshotId uuid.UUID) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	var req spec.PostTenantRestoreRequestBody
	if err = c.BindJSON(&req); err != nil {
		ginx.InvalidArgumentError(c, err)
		return
	}

	oldTenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsID)
	if err != nil {
		if eris.GetCode(err) == eris.CodeNotFound {
			ginx.NotFoundError(c, err)
			return
		}
		ginx.InternalError(c, err)
		return
	}
	newTenantName := req.NewTenantName
	if !namespace.IsValidResourceName(newTenantName) {
		ginx.InvalidArgument(c, "The cluster name must only contain letters, numbers, and hyphens, with a length of no more than 32 characters.")
		return
	}
	check, err := controller.accountAgent.CheckTenantAvailability(c, authInfo.OrgID, 0, oldTenant.TierID, newTenantName, config.Conf.Region)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}
	if !check.Available {
		ginx.FailedPrecondition(c, check.Msg)
		return
	}

	extSpecs, err := controller.tenantExtensionService.GetSpecTenantExtensionsByTenantID(c, oldTenant.ID)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	rtn, err := controller.tenantService.RestoreTenant(c, oldTenant, newTenantName, snapshotId, extSpecs)
	if err != nil {
		//nolint:exhaustive
		switch eris.GetCode(err) {
		case eris.CodeInvalidArgument:
			ginx.InvalidArgumentError(c, err)
		case eris.CodeNotFound:
			ginx.NotFoundError(c, err)
		case eris.CodeAlreadyExists:
			ginx.AlreadyExistsError(c, err)
		case eris.CodeFailedPrecondition:
			ginx.FailedPreconditionError(c, err)
		default:
			ginx.InternalError(c, err)
		}
		return
	}

	c.JSON(202, rtn)
}

func (controller *UserController) PostTenantsNsIdBackupsSnapshotIdInPlaceRestore(c *gin.Context, nsID uuid.UUID, snapshotId uuid.UUID) {
	authInfo, err := ginauth.GetAuthInfo(c)
	if err != nil {
		ginx.InternalError(c, err)
		return
	}

	tenant, err := controller.tenantService.GetOrgTenantByNsID(c, authInfo.OrgID, nsID)
	if err != nil {
		if eris.GetCode(err) == eris.CodeNotFound {
			ginx.NotFoundError(c, err)
			return
		}
		ginx.InternalError(c, err)
		return
	}

	rtn, err := controller.metaBackupService.InPlaceRestore(c, tenant.ID, snapshotId)
	if err != nil {
		//nolint:exhaustive
		switch eris.GetCode(err) {
		case eris.CodeNotFound:
			ginx.NotFoundError(c, err)
		case eris.CodeInvalidArgument:
			ginx.FailedPreconditionError(c, err)
		default:
			ginx.InternalError(c, err)
		}
		return
	}

	c.JSON(202, rtn)
}

func toBackupSnapshotsSpec(backupSnapshots []dto.BackupSnapshotItem) ([]spec.BackupSnapshotItem, error) {
	if backupSnapshots == nil {
		return nil, nil
	}
	backupSnapshotsSpec := make([]spec.BackupSnapshotItem, len(backupSnapshots))
	err := copier.Copy(&backupSnapshotsSpec, &backupSnapshots)
	if err != nil {
		return nil, err
	}
	return backupSnapshotsSpec, nil
}
