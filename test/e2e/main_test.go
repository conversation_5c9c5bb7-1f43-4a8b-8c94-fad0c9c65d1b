/*
 *	Please write test functions in other files and add them in main_test.go
 *
 *  Due to limited CI capacity, please try to control the number of tenants created at the same time
 *	You can use groups to separate subtests and limit tenant creations.
 *
 *  For example:
 *
 *  TestSharedTenant and TestConfigs are in one group
 *  So the group can create and run two tenants simultaneously.
 *
 *	If you need to test locally, please use:
 *
 *  onebox test --parallel e2e -t 30 -v TestMainCI
 *
 */

package e2e

import (
	"bytes"
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/risingwavelabs/eris"

	accountService "github.com/risingwavelabs/risingwave-cloud/account/cmd/server"
	billingApi "github.com/risingwavelabs/risingwave-cloud/billing/api/server"
	billingService "github.com/risingwavelabs/risingwave-cloud/billing/core"
	apiService "github.com/risingwavelabs/risingwave-cloud/cmd/api-service/server"
	"github.com/risingwavelabs/risingwave-cloud/shared/spec/acc_admin"
	"github.com/risingwavelabs/risingwave-cloud/shared/spec/acc_spec"
	"github.com/risingwavelabs/risingwave-cloud/test/config"
	"github.com/risingwavelabs/risingwave-cloud/test/utils"
)

func TestMain(m *testing.M) {
	// determine the test environment based on the go test flag
	flag.StringVar(&config.CloudCfg.Stack, "stack", "LOCAL", "Run E2E Test in Which Stack")
	flag.StringVar(&config.CloudCfg.Region, "region", "us-east-1", "Run E2E Test in Which Region")
	flag.StringVar(&config.CloudCfg.LeastSupportedKernelVersion, "least_supported_kernel_version", "v2.0.3", "Least supported kernel version on cloud")
	flag.StringVar(&config.CloudCfg.KernelVersion, "kernel_version", "", "Kernel version to be used in the test")
	flag.BoolVar(&config.SkipTenantTest, "skip_tenant_test", false, "Skip tenant test")
	flag.BoolVar(&config.UseDistributedRateLimiter, "use_distributed_rate_limiter", false, "If true, use distributed rate limiter for http client trottler")
	flag.StringVar(&config.DistributedRateLimiterBucket, "distributed_rate_limiter_bucket", "", "GCS bucket for distributed rate limiter")
	flag.StringVar(&config.DistributedRateLimiterKey, "distributed_rate_limiter_key", "", "GCS object for distributed rate limiter")
	flag.BoolVar(&config.UseCloudPG, "use_cloud_pg", false, "If true, use vender provided pg(RDS/CloudSQL/FlexibleServer) as metastore in e2e")
	flag.Parse()

	if config.CloudCfg.Stack == config.StackLOCAL {
		fmt.Println("Running in Local Environment")
		ServiceSetup()
		createGlobalAccountForLocal()
	} else {
		fmt.Println("Running in Cloud Environment, Stack: " + config.CloudCfg.Stack + ", Region: " + config.CloudCfg.Region)
		CloudEnvSetup()
	}

	code := m.Run()

	os.Exit(code)
}

func CloudEnvSetup() {
	// AccountService URL + CA URL
	endpoints, exists := config.RegionalEndpoints[config.CloudCfg.Stack]
	if !exists {
		log.Fatal("Unknown Stack:", config.CloudCfg.Stack)
	}
	config.CloudCfg.AccountServiceURL = endpoints.AccountServiceURL
	config.CloudCfg.BillingServiceURL = endpoints.BillingServiceURL
	config.CloudCfg.CAEndpoint = endpoints.CAEndpoint
	fmt.Println("AccountServiceURL: " + config.CloudCfg.AccountServiceURL + ", caURL: " + config.CloudCfg.CAEndpoint)

	// ApiService URL
	url, err := utils.GetRegionUrl(config.CloudCfg.Region)
	if err != nil {
		log.Fatal(err)
	}
	config.CloudCfg.ApiServiceURL = utils.RemoveAPIV1FromURL(url)
	fmt.Println("ApiServiceURL: " + config.CloudCfg.ApiServiceURL)
}

func ServiceSetup() {
	//
	// Management Service

	err := apiService.Init("../../onebox-assets/mgmt/svc_config.json", "../../onebox-assets/mgmt/db_credential.json", "../../onebox-assets/mgmt/resource_def.yaml", "../../onebox-assets/mgmt/app_tokens.json")
	if err != nil {
		panic(err)
	}
	allComponents, err := apiService.InitializeApiService()
	if err != nil {
		panic(err)
	}

	config.APIServiceEngine = allComponents.ApiServer.Handler()
	config.APIServiceAdminServiceEngine = allComponents.AdminServer.Handler()

	//
	// Account Service

	err = accountService.Init("../../onebox-assets/account/db_credential.json", "../../onebox-assets/account/svc_config.json", "../../onebox-assets/account/app_tokens.json", "../../onebox-assets/account/regions")
	if err != nil {
		panic(err)
	}
	_accountServiceEngine, err := accountService.InitializeAccountServiceServer()
	if err != nil {
		panic(err)
	}
	_accountAdminService, err := accountService.InitializeAdminServiceServer()
	if err != nil {
		panic(err)
	}

	config.AccountServiceEngine = _accountServiceEngine.Handler()
	config.AccountAdminServiceEngine = _accountAdminService.Handler()

	//
	// Billing Service

	billingService.InitBilling(
		context.TODO(),
		"../../onebox-assets/billing/svc_config.json",
		"../../onebox-assets/billing/db_credential.json",
		"../../onebox-assets/billing/billing_tokens.json",
	)

	_billingApiEngine, err := billingApi.InitializeBillingAPIServer()
	if err != nil {
		panic(err)
	}
	_billingAdminEngine, err := billingApi.InitializeBillingAdminServer()
	if err != nil {
		panic(err)
	}

	config.BillingApiEngine = _billingApiEngine.Handler()
	config.BillingAdminEngine = _billingAdminEngine.Handler()

	config.CloudCfg.AccountServiceURL = "http://localhost:8180"
	config.CloudCfg.ApiServiceURL = "http://localhost:8080"
}

func createGlobalAccountForLocal() {
	account := config.GlobalAccount

	//
	// Create account.

	response, err := performRequest(
		config.AccountServiceEngine,
		http.MethodPost,
		"/api/v1/auth/register",
		"",
		config.JSON{
			"username": account.Username,
			"email":    account.Email,
			"password": account.Password,
		},
	)

	if err != nil {
		log.Fatal(err)
	} else if response.Code != http.StatusOK {
		log.Fatal("failed to create test account")
	}

	//
	// Log in account.

	response, err = performRequest(
		config.AccountServiceEngine,
		http.MethodPost,
		"/api/v1/auth/login",
		"",
		config.JSON{
			"email":    account.Email,
			"password": account.Password,
		},
	)

	if err != nil {
		log.Fatal(err)
	} else if response.Code != http.StatusOK {
		log.Fatal("failed to log in test account")
	}

	var userAuth acc_spec.UserAuth
	err = json.Unmarshal(response.Body.Bytes(), &userAuth)
	if err != nil {
		log.Fatal("failed to parse response")
	}

	account.UserResourceId = userAuth.ResourceId.String()
	account.OrgId = userAuth.Org.String()
	account.Token = *userAuth.Tokens.Jwt

	//
	// Create and redeem invitation code.

	response, err = performRequest(
		config.AccountAdminServiceEngine,
		http.MethodPost,
		"/api/v1/invitation-codes",
		"",
		config.JSON{
			"number": 1,
		},
	)
	if err != nil {
		log.Fatal(err)
	} else if response.Code != http.StatusOK {
		log.Fatal("failed to create invitation code")
	}

	var invCodes acc_admin.PlainInvitationCodes
	err = json.Unmarshal(response.Body.Bytes(), &invCodes)
	if err != nil {
		log.Fatal("failed to parse invitation codes")
	} else if len(invCodes.InvitationCodes) == 0 {
		log.Fatal("no invitation code produced")
	}

	response, err = performRequest(
		config.AccountServiceEngine,
		http.MethodPost,
		"/api/v1/invitation-codes/redemption",
		account.Token,
		config.JSON{
			"code": invCodes.InvitationCodes[0],
		},
	)
	if err != nil {
		log.Fatal(err)
	} else if response.Code != http.StatusOK {
		log.Fatal("failed to redeem invitation code")
	}
}

func performRequest(service http.Handler, method, path, authToken string, body config.JSON) (*httptest.ResponseRecorder, error) {
	var reqBod io.Reader
	if body != nil {
		rawBody, err := json.Marshal(body)
		if err != nil {
			return nil, eris.Wrap(err, "failed to marshal request body into")
		}

		reqBod = bytes.NewReader(rawBody)
	}

	recorder := httptest.NewRecorder()
	request, err := http.NewRequest(method, "http://localhost"+path, reqBod)
	if err != nil {
		return nil, eris.Wrap(err, "failed to create HTTP request")
	}

	if len(authToken) > 0 {
		request.Header.Add("Authorization", "Bearer "+authToken)
	}
	if reqBod != nil {
		request.Header["Content-Type"] = []string{"application/json"}
	}

	service.ServeHTTP(recorder, request)

	return recorder, nil
}
