package config

import (
	"fmt"
	"net/http"
	"sync"
	"time"
)

const (
	SKUStandardStandaloneTest  = "SD-SA-Test"
	SKUStandardDistributedTest = "SD-Dist-Test"
	SKUInvitedDistributedTest  = "IV-Dist-Test"
	SKUInvitedStandaloneTest   = "IV-SA-Test"
	SKUTest                    = "Test"
	SKUTestStandalone          = "TestStandalone"
	SKUMultiNodeTest           = "MultiNodeTest"
)

var (
	globalAccountName = "__e2e_user__invited__off_trial__"
	GlobalAccount     = &Account{
		Username: globalAccountName,
		Email:    "ci+" + globalAccountName + "@risingwave-labs.com",
		Password: "P@ssw0rd",
	}

	APIServiceEngine             http.Handler
	APIServiceAdminServiceEngine http.Handler
	AccountServiceEngine         http.Handler
	AccountAdminServiceEngine    http.Handler
	BillingApiEngine             http.Handler
	BillingAdminEngine           http.Handler
	GlobalMutex                  sync.Mutex
	CloudCfg                     CloudConfiguration
	SkipTenantTest               = false
	UseDistributedRateLimiter    = false
	DistributedRateLimiterBucket = ""
	DistributedRateLimiterKey    = ""
	UseCloudPG                   = false

	RegionalEndpoints = map[string]CloudConfiguration{
		"TEST": {
			BillingServiceURL: "https://test-useast2-acc-billing.risingwave-cloud.xyz",
			AccountServiceURL: "https://test-useast2-acc.risingwave-cloud.xyz",
			CAEndpoint:        "https://test.risingwave-cloud.com/rootca/root.crt",
		},
		"PROD": {
			BillingServiceURL: "https://canary-useast2-acc-billing.risingwave-cloud.cloud",
			AccountServiceURL: "https://canary-useast2-acc.risingwave.cloud",
			CAEndpoint:        "https://risingwave-cloud.com/rootca/root.crt",
		},
		"RLS": {
			BillingServiceURL: "https://rls-apse1-acc-billing.risingwave-cloud.xyz",
			AccountServiceURL: "https://rls-apse1-acc.risingwave-cloud.xyz",
			CAEndpoint:        "https://rls.risingwave-cloud.com/rootca/root.crt",
		},
		"DEV": {
			BillingServiceURL: "https://dev-apse1-acc-billing.risingwave-cloud.xyz",
			AccountServiceURL: "https://dev-apse1-acc.risingwave-cloud.xyz",
			CAEndpoint:        "https://dev.risingwave-cloud.com/rootca/root.crt",
		},
	}
	// maps stack to map of available regions and their respective privatelink service.
	RegionalDemoSourcePrivateLinkServices = map[string]map[string]string{
		// retrieved from https://ap-southeast-1.console.aws.amazon.com/vpcconsole/home?region=ap-southeast-1#EndpointServices:.
		// retrieved from https://console.cloud.google.com/net-services/psc/list/producers?project=rwcdev
		// retrieved from https://portal.azure.com/#@risingwave-labs.com/resource/subscriptions/3f59ac6a-d708-49ec-9490-a3c786d63455/resourceGroups/dev-azr-inc-mgmt-rg/providers/Microsoft.Network/privateLinkServices/dev-azr-inc-mgmt-aks-demosrc/overview
		"DEV": {
			"ap-southeast-1":  "com.amazonaws.vpce.ap-southeast-1.vpce-svc-029135e4650cb393a",
			"asia-southeast1": "https://www.googleapis.com/compute/v1/projects/rwcdev/regions/asia-southeast1/serviceAttachments/k8s1-sa-54arwlr7-demosrc-demosrc-86orgahs",
			"centralindia":    "/subscriptions/3f59ac6a-d708-49ec-9490-a3c786d63455/resourceGroups/dev-azr-inc-mgmt-rg/providers/Microsoft.Network/privateLinkServices/dev-azr-inc-mgmt-aks-demosrc",
		},
		// retrieved from https://us-east-1.console.aws.amazon.com/vpcconsole/home?region=us-east-1#EndpointServices:.
		// retrieved from https://console.cloud.google.com/net-services/psc/list/producers?project=rwctest
		// retrieved from https://portal.azure.com/#@risingwave-labs.com/resource/subscriptions/a1e2228d-b60b-4015-b3b3-49d862e6430c/resourceGroups/test-azr-use2-mgmt-rg/providers/Microsoft.Network/privateLinkServices/test-azr-use2-mgmt-aks-demosrc/overview
		"TEST": {
			"us-east-1":   "com.amazonaws.vpce.us-east-1.vpce-svc-09f9d5594a26aa7d0",
			"us-central1": "https://www.googleapis.com/compute/v1/projects/rwctest/regions/us-central1/serviceAttachments/k8s1-sa-q0jojsxr-demosrc-demosrc-2ygxp2rs",
			"eastus2":     "/subscriptions/a1e2228d-b60b-4015-b3b3-49d862e6430c/resourceGroups/test-azr-use2-mgmt-rg/providers/Microsoft.Network/privateLinkServices/test-azr-use2-mgmt-aks-demosrc",
		},
		// retrieved from https://us-east-1.console.aws.amazon.com/vpcconsole/home?region=us-east-1#EndpointServices:
		// retrieved from https://console.cloud.google.com/net-services/psc/list/producers?project=rwcrls
		// retrieved from https://portal.azure.com/#@risingwave-labs.com/resource/subscriptions/36118c9b-28ec-4f8a-8456-8f0d5db293dc/resourceGroups/rls-azr-use2-mgmt-rg/providers/Microsoft.Network/privateLinkServices/rls-azr-use2-mgmt-aks-demosrc/overview
		"RLS": {
			"us-east-1":   "com.amazonaws.vpce.us-east-1.vpce-svc-0d3f3b02ad18cc6cc",
			"us-central1": "https://www.googleapis.com/compute/v1/projects/rwcrls/regions/us-central1/serviceAttachments/k8s1-sa-al9dzbj6-demosrc-demosrc-l8hg5oil",
			"eastus2":     "/subscriptions/36118c9b-28ec-4f8a-8456-8f0d5db293dc/resourceGroups/rls-azr-use2-mgmt-rg/providers/Microsoft.Network/privateLinkServices/rls-azr-use2-mgmt-aks-demosrc",
		},
	}
)

type JSON = map[string]interface{}

type Account struct {
	Username       string
	Email          string
	Password       string
	Token          string
	UserId         float64
	UserResourceId string
	OrgId          string
}

type CloudConfiguration struct {
	Stack                       string
	Region                      string
	AccountServiceURL           string
	ApiServiceURL               string
	BillingServiceURL           string
	CAEndpoint                  string
	LeastSupportedKernelVersion string
	KernelVersion               string
}

const (
	PollTenantTimeout      = 45 * time.Minute
	PollTenantInterval     = 60 * time.Second
	PollDemoSourceTimeout  = 15 * time.Minute
	PollDemoSourceInterval = 30 * time.Second
	StackLOCAL             = "LOCAL"
)

func GetFreeDuringTrialAccount() *Account { return getTierBasedAccount(false, true) }
func GetFreePastTrialAccount() *Account   { return getTierBasedAccount(false, false) }
func GetPaidDuringTrialAccount() *Account { return getTierBasedAccount(true, true) }
func GetPaidPastTrialAccount() *Account   { return getTierBasedAccount(true, false) }

func getTierBasedAccount(isPaid, duringTrial bool) *Account {
	// Pattern: __e2e_user__<region>__<paid|free>__<during|past>_trial__

	var paidString string
	if isPaid {
		paidString = "paid"
	} else {
		paidString = "free"
	}

	var trialString string
	if duringTrial {
		trialString = "during"
	} else {
		trialString = "past"
	}

	accountName := fmt.Sprintf(
		"__e2e_user__%s__%s__%s_trial__",
		CloudCfg.Region, paidString, trialString,
	)
	return &Account{
		Username: accountName,
		Email:    "ci+" + accountName + "@risingwave-labs.com",
		Password: "P@ssw0rd",
	}
}

func GetInvitedAccount() *Account {
	// Pattern: __e2e_user__<region>__invited__off_trial__

	accountName := fmt.Sprintf(
		"__e2e_user__%s__%s__%s_trial__",
		CloudCfg.Region, "invited", "off",
	)
	return &Account{
		Username: accountName,
		Email:    "ci+" + accountName + "@risingwave-labs.com",
		Password: "P@ssw0rd",
	}
}
