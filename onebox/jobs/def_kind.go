package jobs

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"runtime"
	"strings"

	"github.com/docker/docker/api/types/image"
	docker "github.com/docker/docker/client"

	"github.com/singularity-data/risingwave-cloud/onebox/configs"

	"github.com/risingwavelabs/risingwave-cloud/shared/namespace"
)

// OPERATOR_TAG will be updated continually
var (
	OPERATOR_TAG   = "v0.10.0"
	RISINGWAVE_TAG = Optional(readRisingwaveTag(), "v2.3.2")
)

const (
	CLUSTER_NAME         = "onebox"
	KIND_CONTEXT         = "kind-" + CLUSTER_NAME
	RWPROXY_PORT         = 30010
	PROMETHUS_ADMIN_PORT = 30900
	GRAFANA_PORT         = 30800
	CLUSTER_ADMIN        = "rwadmin"
	CLUSTER_ADMIN_TOKEN  = "rwadmin-token"
	RWPROXY_TAG          = "v1.1.0"
	RWPROXY_REGISTRY     = "639303875316.dkr.ecr.ap-southeast-1.amazonaws.com"
	CLOUDAGENT_NODE_PORT = 30110
	CLOUDAGENT_TAG       = "v1.1.0"
	CLOUDAGENT_REGISTRY  = "639303875316.dkr.ecr.ap-southeast-1.amazonaws.com"

	DOCKER_HOST_DOMAIN = "dockerhost.default.svc"
)

var l = NewLogger("setup")

var preInstallImages = []string{
	fmt.Sprintf("ghcr.io/risingwavelabs/risingwave:%s", RISINGWAVE_TAG),
	fmt.Sprintf("ghcr.io/risingwavelabs/risingwave-operator:%s", OPERATOR_TAG),
	"quay.io/jetstack/cert-manager-cainjector:v1.8.0",
	"quay.io/jetstack/cert-manager-controller:v1.8.0",
	"quay.io/jetstack/cert-manager-webhook:v1.8.0",
	"quay.io/brancz/kube-rbac-proxy:v0.14.1", // depend by operator
	"ghcr.io/zalando/postgres-operator:v1.12.0",
	"ghcr.io/zalando/spilo-16:3.2-p3", // postgres
}

func readRisingwaveTag() *string {
	bytes, err := os.ReadFile(configs.UserConfig.ProjectPath + "/onebox-assets/mgmt/svc_config.json")
	if err != nil {
		fmt.Printf("%sWARNING: Failed to read Risingwave Tag from %s\n", "\u001B[33m", "\u001B[0m")
		fmt.Printf("%s         <ProjectPath>/onebox-assets/mgmt/svc_config.json  %s\n", "\u001B[33m", "\u001B[0m")
		return nil
	}
	var svcConfig = struct {
		Mgmt struct {
			Risingwave struct {
				Tag string `json:"tag"`
			} `json:"risingwave"`
		} `json:"mgmt"`
	}{}
	err = json.Unmarshal(bytes, &svcConfig)
	if err != nil {
		fmt.Printf("%sWARNING: Failed to read Risingwave Tag from %s\n", "\u001B[33m", "\u001B[0m")
		fmt.Printf("%s         <ProjectPath>/onebox-assets/mgmt/svc_config.json  %s\n", "\u001B[33m", "\u001B[0m")
		return nil
	}
	return Ptr(svcConfig.Mgmt.Risingwave.Tag)
}

func GetLoadAllImageJob(scope *JobScope) *Job {
	loadImageJob := DummyJob("load image", scope)
	for _, _image := range preInstallImages {
		image := _image

		loadJob := NewLoadJob(scope, image)
		pullJob := NewPullJob(scope, image)
		loadJob.Depend(pullJob)

		loadJob.Depend(scope.EnsureJob(initKindDef))
		loadImageJob.Depend(loadJob)
	}
	return loadImageJob
}

func GetDeployRwproxy(scope *JobScope) *Job {
	var (
		loadLocalRwproxy     = GetLoadLocalRwproxy(scope)
		prepareLocalhostCert = scope.EnsureJob(prepareLocalhostCertDef)
	)
	return scope.EnsureJob(deployRwProxyDef).Depend(
		loadLocalRwproxy,
		prepareLocalhostCert,
	)
}

func GetDeployCloudAgent(scope *JobScope) *Job {
	var (
		loadLocalCloudAgent           = GetLoadLocalCloudAgent(scope)
		loadLocalCloudAgentTaskRunner = GetLoadLocalCloudAgentTaskRuner(scope)
	)
	return scope.EnsureJob(deployCloudAgentDef).Depend(
		loadLocalCloudAgent,
		loadLocalCloudAgentTaskRunner,
	)
}

func NewPullJob(scope *JobScope, imageTag string) *Job {
	pullJob := NewJob(fmt.Sprintf("pull %s", imageTag), scope, func(ctx context.Context, j *Job) error {
		client, err := docker.NewClientWithOpts()
		if err != nil {
			return err
		}
		client.NegotiateAPIVersion(ctx)
		reader, err := client.ImagePull(ctx, imageTag, image.PullOptions{})
		if err != nil {
			return err
		}
		defer reader.Close()
		if *configs.Verbose {
			io.Copy(os.Stdout, reader)
		} else {
			io.Copy(io.Discard, reader)
		}
		return nil
	})

	return pullJob
}

func NewLoadJob(scope *JobScope, image string) *Job {

	loadJob := NewJob(fmt.Sprintf("load %s", image), scope, func(ctx context.Context, j *Job) error {
		return RunCommand(ctx, NewCommand("kind", "load", "docker-image", image, "--name", CLUSTER_NAME))
	})
	return loadJob
}

var deleteKindDef = &JobDef{
	Name: "delete kind",
	Task: func(ctx context.Context, job *Job) error {
		return RunCommand(ctx, NewCommand("kind", "delete", "cluster", "--name", CLUSTER_NAME))
	},
}

var initKindDef = &JobDef{
	Name: "init kind",
	Task: func(ctx context.Context, job *Job) error {
		out, err := OutputCommand(ctx, NewCommand("kind", "get", "clusters"))
		if err != nil {
			return err
		}
		// kind cluster already exists
		if strings.Contains(string(out), CLUSTER_NAME) {
			return nil
		}

		pathname, err := getKindConfig(CLOUDAGENT_NODE_PORT, RWPROXY_PORT, PROMETHUS_ADMIN_PORT, GRAFANA_PORT, *configs.WorkerNodes)
		if err != nil {
			l.Logln("failed to write kind-config.yaml")
			return err
		}
		return RunCommand(ctx, NewCommand("kind", "create", "cluster", "--name", CLUSTER_NAME, "--config", pathname))
	},
}

var existKindDef = &JobDef{
	Name: "ensure kind exist",
	Task: func(ctx context.Context, job *Job) error {
		out, err := OutputCommand(ctx, NewCommand("kind", "get", "clusters"))
		if err != nil {
			return err
		}
		if !strings.Contains(string(out), CLUSTER_NAME) {
			return fmt.Errorf("not found kind cluster %s", CLUSTER_NAME)
		}
		return nil
	},
}

var setupKubectlContextDef = &JobDef{
	Name: "Setup kubectl context",
	Task: func(ctx context.Context, j *Job) error {
		err := RunCommand(ctx, NewCommand("kubectl", "config", "use-context", KIND_CONTEXT))
		return err
	},
}

var setupKindAdminServiceAccountDef = &JobDef{
	Name: "setup admin service account in kind",
	Task: func(ctx context.Context, j *Job) error {
		if err := RunCommand(ctx, NewCommand("kubectl", "create", "serviceaccount", CLUSTER_ADMIN, "-n", "kube-system", "--context", KIND_CONTEXT)); err != nil {
			return err
		}
		if err := RunCommand(ctx, NewCommand("kubectl", "create", "clusterrolebinding", CLUSTER_ADMIN, "--clusterrole=cluster-admin", fmt.Sprintf("--serviceaccount=kube-system:%s", CLUSTER_ADMIN), "--context", KIND_CONTEXT)); err != nil {
			return err
		}
		pathname, err := getKindAdminServiceAccount(CLUSTER_ADMIN_TOKEN, CLUSTER_ADMIN)
		if err != nil {
			return err
		}

		if err := RunCommand(ctx, NewCommand("kubectl", "apply", "-f", pathname, "--context", KIND_CONTEXT)); err != nil {
			return err
		}

		return nil
	},
}

var createDockerHostServiceDef = &JobDef{
	Name: "create docker host service",
	Task: func(ctx context.Context, j *Job) error {
		// Get docker host IP based on OS
		serviceYAML := getDockerHostService()
		pathname, err := writeToTemp(serviceYAML)
		if err != nil {
			return fmt.Errorf("failed to write service definition: %w", err)
		}

		if err := RunCommand(ctx, NewCommand("kubectl", "apply", "-f", pathname, "--context", KIND_CONTEXT)); err != nil {
			return fmt.Errorf("failed to apply docker host service: %w", err)
		}

		return nil
	},
}

type outputKindVariableOutput struct {
	masterUtl string
	token     string
}

func GetOutputKindVariable(scope *JobScope) *Job {
	var setupKubectlContext = scope.EnsureJob(setupKubectlContextDef)
	return scope.EnsureJob(outputKindVariableDef).Depend(setupKubectlContext)
}

var outputKindVariableDef = &JobDef{
	Name: "output kind variables",
	Task: func(ctx context.Context, job *Job) error {
		masterUrl, err := OutputCommand(ctx, NewCommand("kubectl", "config", "view", "-o", fmt.Sprintf("jsonpath=\"{.clusters[?(@.name==\"kind-%s\")].cluster.server}\"", CLUSTER_NAME), "--context", KIND_CONTEXT))
		if err != nil {
			return err
		}
		token, err := OutputCommand(ctx, NewCommand("kubectl", "-n", "kube-system", "get", "secret", CLUSTER_ADMIN_TOKEN, "-o", "jsonpath='{.data.token}'", "--context", KIND_CONTEXT))
		if err != nil {
			return err
		}
		decodedTokenBytes, err := base64.StdEncoding.DecodeString(strings.Trim(string(token), "\"'\n "))
		if err != nil {
			return err
		}
		job.Output = &outputKindVariableOutput{
			masterUtl: strings.Trim(string(masterUrl), "\"'\n "),
			token:     string(decodedTokenBytes),
		}
		return nil
	},
}

var deployCertManagerDef = &JobDef{
	Name: "deploy cert manager",
	Task: func(ctx context.Context, job *Job) error {
		err := RunCommand(ctx, NewCommand("kubectl", "apply", "-f", "https://github.com/cert-manager/cert-manager/releases/download/v1.8.0/cert-manager.yaml", "--context", KIND_CONTEXT))
		if err != nil {
			return err
		}
		err = RunCommand(ctx, NewCommand("kubectl", "wait", "--for=condition=Ready", "Pods", "--all", "--namespace", "cert-manager", "--timeout=5m", "--context", KIND_CONTEXT))
		if err != nil {
			return err
		}
		return nil
	},
}

var deployRisingWaveOperatorDef = &JobDef{
	Name: "deploy RisingWave operator",
	Task: func(ctx context.Context, j *Job) error {
		err := RunCommand(ctx, NewCommand("kubectl", "apply", "--server-side", "-f",
			fmt.Sprintf("https://github.com/risingwavelabs/risingwave-operator/releases/download/%s/risingwave-operator.yaml", OPERATOR_TAG),
			"--context", KIND_CONTEXT,
		))
		if err != nil {
			return err
		}
		err = RunCommand(ctx, NewCommand("kubectl", "wait", "--for=condition=Ready", "Pods", "--all", "--namespace", "risingwave-operator-system", "--timeout=5m", "--context", KIND_CONTEXT))
		if err != nil {
			return err
		}
		return nil
	},
}

var deployRwProxyDef = &JobDef{
	Name: "deploy rwproxy",
	Task: func(ctx context.Context, j *Job) error {
		// load rwproxy from source
		image, ok := GetOutput(j.Scope, loadLocalRwproxyDef).(string)
		if !ok {
			return fmt.Errorf("failed to get output from loadLocalRwproxyDef")
		}

		// deploy rwproxy
		output, ok := GetOutput(j.Scope, prepareLocalhostCertDef).(map[string]string)
		if !ok {
			return fmt.Errorf("failed to get output from prepareLocalhostCertDef")
		}
		deploymentPath, err := getRwproxyDeployment(image, output["cert"], output["key"])
		if err != nil {
			return err
		}
		if err := RunCommand(ctx, NewCommand("kubectl", "apply", "-f", deploymentPath, "--context", KIND_CONTEXT)); err != nil {
			return err
		}
		return nil
	},
}

var deployCloudAgentDef = &JobDef{
	Name: "deploy cloudagent",
	Task: func(ctx context.Context, j *Job) error {
		serviceImage, ok := GetOutput(j.Scope, loadLocalCloudAgentDef).(string)
		if !ok {
			return fmt.Errorf("failed to get output from loadLocalCloudAgentDef")
		}
		taskRunnerImage, ok := GetOutput(j.Scope, loadLocalCloudAgentTaskRunnerDef).(string)
		if !ok {
			return fmt.Errorf("failed to get output from loadLocalCloudAgentTaskRunnerDef")
		}

		tls, err := loadCloudAgentTLS()
		if err != nil {
			return fmt.Errorf("failed to load CloudAgent TLS, %v", err)
		}

		// deploy cloudagent
		deploymentPath, err := getCloudAgentDeployment(CloudAgentConfig{
			ServiceImage:    serviceImage,
			TaskRunnerImage: taskRunnerImage,
			TLS:             tls,
		})
		if err != nil {
			return err
		}
		if err := RunCommand(ctx, NewCommand("kubectl", "apply", "-f", deploymentPath, "--context", KIND_CONTEXT)); err != nil {
			return err
		}
		return nil
	},
}

var deleteRwcNamespacesDef = &JobDef{
	Name: "delete rwc namespaces",
	Task: func(ctx context.Context, j *Job) error {
		scope := j.Scope

		output, err := OutputCommand(ctx, NewCommand("kubectl", "get", "namespaces", "--context", KIND_CONTEXT))
		if err != nil {
			return err
		}
		listRaw := strings.Split(string(output), "\n")
		for i, line := range listRaw {
			if strings.TrimSpace(line) == "" {
				continue
			}
			fields := strings.Fields(line)
			name := fields[0]
			listRaw[i] = name
		}
		var deleteRwcNamespacesJob []*Job
		for _, n := range listRaw {
			if !strings.Contains(n, namespace.Prefix) {
				continue
			}
			name := n
			deleteJob := NewJob(fmt.Sprintf("delete namespace %s", name), scope, func(ctx context.Context, j *Job) error {
				return RunCommand(ctx, NewCommand("kubectl", "delete", "namespace", name, "--context", KIND_CONTEXT))
			})
			deleteRwcNamespacesJob = append(deleteRwcNamespacesJob, deleteJob)
		}
		job := DummyJob("delete namespaces root", scope)
		job.Depend(deleteRwcNamespacesJob...)
		return RunJobWithContext(job, ctx)
	},
}

func getDockerHostService() string {
	goos := runtime.GOOS
	if goos == "linux" && isWSL() {
		goos = "windows"
	}

	// reference: https://github.com/kubernetes-sigs/kind/issues/1200#issuecomment-1304855791
	switch goos {
	case "linux":
		// On Linux, docker host is accessible via the docker0 bridge interface
		return `
apiVersion: v1
kind: Endpoints
metadata:
  name: dockerhost
subsets:
- addresses:
  - ip: ********** # this is the gateway IP in the "bridge" docker network
---
apiVersion: v1
kind: Service
metadata:
  name: dockerhost
spec:
  clusterIP: None
`

	case "darwin", "windows":
		// On macOS/windows, docker host is accessible via host.docker.internal
		return `
apiVersion: v1
kind: Service
metadata:
  name: dockerhost
spec:
  type: ExternalName
  externalName: host.docker.internal
`
	default:
		return ""
	}
}

func isWSL() bool {
	if version, err := os.ReadFile("/proc/version"); err == nil {
		return strings.Contains(strings.ToLower(string(version)), "microsoft")
	}
	return false
}
