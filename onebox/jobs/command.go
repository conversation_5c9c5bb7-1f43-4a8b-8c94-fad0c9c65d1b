package jobs

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"strings"
	"sync"
	"syscall"

	"github.com/singularity-data/risingwave-cloud/onebox/configs"
	"github.com/singularity-data/risingwave-cloud/onebox/pkg/oneboxd/client"
)

var commandLogger = NewLogger("Command")

func EnvCheck() error {
	tools := []string{"kind", "kubectl", "docker", "oneboxd-server", "psql", "helm"}
	notFound := ""
	for _, tool := range tools {
		if !CommandExists(tool) {
			notFound += tool + " "
		}
	}
	if notFound != "" {
		return fmt.Errorf("missing %s", notFound)
	}
	return nil
}

func CommandExists(cmd string) bool {
	_, err := exec.LookPath(cmd)
	return err == nil
}

func NewCommandWithDir(dir, command string, args ...string) *exec.Cmd {
	cmd := exec.Command(command, args...)
	if filepath.IsAbs(dir) {
		cmd.Dir = dir
	} else {
		cmd.Dir = path.Join(*configs.ProjectPath, dir)
	}
	coreLogger.Verbosef("run command %s at %s\n", command, dir)
	if *configs.Verbose {
		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr
	}
	return cmd
}

func NewCommand(command string, args ...string) *exec.Cmd {
	return NewCommandWithDir(*configs.ProjectPath, command, args...)
}

func NewLongCommand(longCommand string) *exec.Cmd {
	segments := strings.Split(longCommand, " ")
	return NewCommandWithDir(*configs.ProjectPath, segments[0], segments[1:]...)
}

func releaseOnContext(ctx context.Context, cmd *exec.Cmd) {
	<-ctx.Done()
	if cmd.Process != nil {
		if cmd.ProcessState != nil && !cmd.ProcessState.Exited() {
			commandLogger.Errorf("cancelling %s: %s\n", cmd.Args, cmd.ProcessState)
			cmd.Process.Signal(syscall.SIGTERM)
		}
	}
}

func RunCommand(ctx context.Context, cmd *exec.Cmd) error {
	go releaseOnContext(ctx, cmd)
	commandLogger.Verbosef("running normal command %s\n", cmd.Args)
	err := cmd.Run()
	if err != nil {
		commandLogger.Errorf("failed to run command %s: %s\n", cmd.Args, err)
	} else {
		commandLogger.Verbosef("finished command %s\n", cmd.Args)
	}
	return err
}

func RunCommandsParallel(ctx context.Context, description string, cmds ...*exec.Cmd) error {
	var waitGroup sync.WaitGroup
	waitGroup.Add(len(cmds))

	wgErrors := []error{}
	for _, cmd := range cmds {
		go func(c *exec.Cmd, outErrors []error) {
			defer waitGroup.Done()
			err := c.Run()
			if err != nil {
				commandLogger.Errorf("failed to run command %s: %s\n", c.Args, err)
				wgErrors = append(wgErrors, err)
			} else {
				commandLogger.Verbosef("finished command %s\n", c.Args)
			}
		}(cmd, wgErrors)
	}

	wgDone := make(chan bool, 1)
	go func(wg *sync.WaitGroup, c chan bool) {
		wg.Wait()
		c <- true
	}(&waitGroup, wgDone)

	select {
	case <-ctx.Done():
		commandLogger.Errorf("failed to run command %s in parallel", description)
	case <-wgDone:
		commandLogger.Verbosef("finished to run command %s in parallel", description)
	}
	if len(wgErrors) == 0 {
		return nil
	}
	return fmt.Errorf("%v", wgErrors)
}

func OutputCommand(ctx context.Context, cmd *exec.Cmd) ([]byte, error) {
	cmd.Stdout = nil
	cmd.Stderr = nil
	go releaseOnContext(ctx, cmd)
	commandLogger.Verbosef("running output command %s\n", cmd.Args)
	output, err := cmd.Output()

	if err != nil {
		commandLogger.Errorf("failed to run command %s: %s\n", cmd.Args, err)
	} else {
		commandLogger.Verbosef("finished command %s: %s\n", cmd.Args, string(output))
	}
	return output, err
}

func TryCommand(ctx context.Context, cmd *exec.Cmd) bool {
	go releaseOnContext(ctx, cmd)
	commandLogger.Verbosef("trying command %s\n", cmd.Args)
	err := cmd.Run()
	commandLogger.Verbosef("try command %s: %s\n", cmd.Args, err)
	return err == nil
}

func NewScript(script string) *exec.Cmd {
	pathname, err := writeToTemp(script)
	must(err)
	cmd := exec.Command("bash", pathname)
	cmd.Dir = ""
	if *configs.Verbose {
		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr
	}
	return cmd
}

func BackgroundRun(ctx context.Context, name, command string, args []string, env []string) error {
	return OneboxdExec(func(oc *client.OneboxdClient) error {
		id, err := oc.StartDaemon(ctx, name, "", command, args, env)
		coreLogger.Verbosef("start daemon %s, %s\n", name, id)
		if err != nil {
			return err
		}
		return nil
	})
}
