# Onebox

Onebox is a CLI tool for local RisingWave Cloud development. It provisions infrastructure, manages services, and runs tests in a unified local environment.

## Quick Start

### Installation

```bash
cd onebox
make install
onebox configure path <risingwave-cloud-root-directory>
```

## Common Commands

```bash
# Infrastructure setup
onebox setup -v -t 60           # Set up Kind cluster + databases

# Service management
onebox dev                      # Start API + workflow engine

# Process management
onebox proc list                # List running processes
onebox proc log <name>          # View process logs

# Testing
onebox test -v -t 60 e2e                      # Run E2E tests,
onebox test -v -t 60 e2e <TestFuncationName>  # Run single E2E test function

# Database access
onebox attach msdb / asdb       # Connect to MSDB or ASDB

# Cleanup
onebox clean                    # Clean processes and database. Use case: before new `onebox test` or `onebox dev`
onebox shutdown                 # Shutdown all services
```

**Useful flags**: `-v` (verbose), `-t <minutes>` (timeout), `--lightweight` (skip Kind)

## Shell Completion

```bash
# Bash
source <(onebox completion bash -s) # set up autocomplete in bash into the current shell
echo "source <(onebox completion bash -s)" >> ~/.bashrc # add autocomplete permanently to your bash shell

# Zsh
source <(onebox completion zsh -s)  # set up autocomplete in zsh into the current shell
echo '[[ $commands[onebox] ]] && source <(onebox completion zsh -s)' >> ~/.zshrc # add autocomplete permanently to your zsh shell

```

## Development

1. Define jobs in `./jobs/def_*`
2. Define command line entrypoint in `./jobs/jobs.go`
