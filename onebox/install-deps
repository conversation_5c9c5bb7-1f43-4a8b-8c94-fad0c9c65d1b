#!/bin/bash

BIN_PATH=./bin
INSTALL_DIR=$HOME/.local/bin

if ! [ -x "$(command -v kubectl 2>&-)" ]; then
	echo 'Kubectl not found. Installing...'
	OS=$(go env GOOS)
	ARCH=$(go env GOARCH)
	curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/$OS/$ARCH/kubectl"
	curl -LO "https://dl.k8s.io/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/$OS/$ARCH/kubectl.sha256" >/dev/null
	if ! echo "$(cat kubectl.sha256)  kubectl" | shasum -a 256 -eq 0; then
		echo "Checksum could not be verified"
		exit 1
	fi

	if ! install kubectl ${BIN_PATH}/kubectl -eq 0; then
		echo "Installation failed"
		exit 1
	fi
	rm kubectl kubectl.sha256
fi

if ! [ -x "$(command -v kind 2>&-)" ]; then
	echo 'Kind not found. Installing...'
	OS=$(go env GOOS)
	ARCH=$(go env GOARCH)
	curl -Lo ./kind https://kind.sigs.k8s.io/dl/v0.15.0/kind-"$OS"-"$ARCH"
	chmod +x ./kind
	mv ./kind ${BIN_PATH}/kind
	rm ./LICENSES
fi

if [ ! -d "${INSTALL_DIR}" ]; then
	echo "${INSTALL_DIR} does not exist. Creating..."
	mkdir -p "${INSTALL_DIR}"
fi

cp -rip "${BIN_PATH}"/* "${INSTALL_DIR}"

if ! [[ ":$PATH:" == *":${INSTALL_DIR}:"* ]]; then
	echo -e "\x1B[31mDependencies installed to ${INSTALL_DIR}, which is not on PATH.\x1B[0m"
fi
