// Package acc_spec_v2 provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package acc_spec_v2

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

const (
	ApiKeyAuthScopes = "ApiKeyAuth.Scopes"
	BearerAuthScopes = "BearerAuth.Scopes"
)

// Defines values for AlertSeverity.
const (
	Critical AlertSeverity = "critical"
	Warning  AlertSeverity = "warning"
)

// Defines values for AuthType.
const (
	Github       AuthType = "github"
	GoogleOauth2 AuthType = "google-oauth2"
	Local        AuthType = "local"
	Sso          AuthType = "sso"
	Windowslive  AuthType = "windowslive"
)

// Defines values for NotificationStatus.
const (
	NotificationStatusDeleted NotificationStatus = "Deleted"
	NotificationStatusRead    NotificationStatus = "Read"
	NotificationStatusUnread  NotificationStatus = "Unread"
)

// Defines values for NotificationType.
const (
	NotificationTypeAlert        NotificationType = "Alert"
	NotificationTypeMessage      NotificationType = "Message"
	NotificationTypeNotification NotificationType = "Notification"
)

// Defines values for PutNotificationsStatusRequestBodyStatus.
const (
	PutNotificationsStatusRequestBodyStatusDeleted PutNotificationsStatusRequestBodyStatus = "Deleted"
	PutNotificationsStatusRequestBodyStatusRead    PutNotificationsStatusRequestBodyStatus = "Read"
	PutNotificationsStatusRequestBodyStatusUnread  PutNotificationsStatusRequestBodyStatus = "Unread"
)

// Defines values for RecipientType.
const (
	RecipientTypeEmail RecipientType = "email"
	RecipientTypeSlack RecipientType = "slack"
	RecipientTypeUser  RecipientType = "user"
)

// Defines values for GetNotificationsParamsOrder.
const (
	CreatedAtAsc  GetNotificationsParamsOrder = "createdAtAsc"
	CreatedAtDesc GetNotificationsParamsOrder = "createdAtDesc"
)

// AlertSeverity defines model for AlertSeverity.
type AlertSeverity string

// AlertType defines model for AlertType.
type AlertType struct {
	Category    string        `json:"category"`
	Description string        `json:"description"`
	Name        string        `json:"name"`
	Severity    AlertSeverity `json:"severity"`
}

// AlertTypeArray defines model for AlertTypeArray.
type AlertTypeArray = []AlertType

// ApiKey defines model for ApiKey.
type ApiKey struct {
	CreatedAt   time.Time          `json:"createdAt"`
	Description string             `json:"description"`
	Id          uint64             `json:"id"`
	Key         string             `json:"key"`
	Principal   openapi_types.UUID `json:"principal"`
	Secret      string             `json:"secret"`
	UpdatedAt   time.Time          `json:"updatedAt"`
}

// ApiKeyArray defines model for ApiKeyArray.
type ApiKeyArray = []ApiKey

// ApiKeyPagination defines model for ApiKeyPagination.
type ApiKeyPagination struct {
	ApiKeys    ApiKeyArray `json:"apiKeys"`
	Pagination *Pagination `json:"pagination,omitempty"`
}

// AuthType defines model for AuthType.
type AuthType string

// CreateInvitationRequestBody defines model for CreateInvitationRequestBody.
type CreateInvitationRequestBody struct {
	Email  string             `json:"email"`
	RoleId openapi_types.UUID `json:"roleId"`
}

// EmailRecipientConfig defines model for EmailRecipientConfig.
type EmailRecipientConfig struct {
	Email string        `json:"email"`
	Type  RecipientType `json:"type"`
}

// Invitation defines model for Invitation.
type Invitation struct {
	CreatedAt time.Time          `json:"createdAt"`
	Email     string             `json:"email"`
	ExpiresAt time.Time          `json:"expiresAt"`
	Id        uint64             `json:"id"`
	Name      string             `json:"name"`
	OrgId     string             `json:"orgId"`
	RoleId    openapi_types.UUID `json:"roleId"`
	UpdatedAt time.Time          `json:"updatedAt"`
}

// InvitationArray defines model for InvitationArray.
type InvitationArray = []Invitation

// InvitationPagination defines model for InvitationPagination.
type InvitationPagination struct {
	Invitations InvitationArray `json:"invitations"`
	Pagination  *Pagination     `json:"pagination,omitempty"`
}

// Notification defines model for Notification.
type Notification struct {
	Content string             `json:"content"`
	Id      uint64             `json:"id"`
	Sender  NotificationSender `json:"sender"`
	Status  NotificationStatus `json:"status"`
	Time    time.Time          `json:"time"`
	Title   string             `json:"title"`
	Type    NotificationType   `json:"type"`
}

// NotificationStatus defines model for Notification.Status.
type NotificationStatus string

// NotificationType defines model for Notification.Type.
type NotificationType string

// NotificationArray defines model for NotificationArray.
type NotificationArray = []Notification

// NotificationPagination defines model for NotificationPagination.
type NotificationPagination struct {
	Notifications NotificationArray `json:"notifications"`
	Pagination    *Pagination       `json:"pagination,omitempty"`
	UnreadNum     uint64            `json:"unreadNum"`
}

// NotificationSender defines model for NotificationSender.
type NotificationSender struct {
	Email string `json:"email"`
	Name  string `json:"name"`
}

// Org defines model for Org.
type Org struct {
	CreatedAt time.Time          `json:"createdAt"`
	Name      string             `json:"name"`
	OrgId     openapi_types.UUID `json:"orgId"`
	UpdatedAt time.Time          `json:"updatedAt"`
}

// Page defines model for Page.
type Page struct {
	Limit  uint64 `json:"limit"`
	Offset uint64 `json:"offset"`
}

// Pagination defines model for Pagination.
type Pagination struct {
	Limit  uint64 `json:"limit"`
	Offset uint64 `json:"offset"`
	Size   uint64 `json:"size"`
}

// PostApiKeyRequestBody defines model for PostApiKeyRequestBody.
type PostApiKeyRequestBody struct {
	Description string             `json:"description"`
	Principal   openapi_types.UUID `json:"principal"`
}

// PostRecipientRequestBody defines model for PostRecipientRequestBody.
type PostRecipientRequestBody struct {
	Config RecipientConfig `json:"config"`
}

// PostServiceAccountRequestBody defines model for PostServiceAccountRequestBody.
type PostServiceAccountRequestBody struct {
	Description string             `json:"description"`
	Name        string             `json:"name"`
	RoleId      openapi_types.UUID `json:"roleId"`
}

// PostSubscriptionRequestBody defines model for PostSubscriptionRequestBody.
type PostSubscriptionRequestBody struct {
	AlertLevel  string             `json:"alertLevel"`
	RecipientId openapi_types.UUID `json:"recipientId"`
}

// PutApiKeyRequestBody defines model for PutApiKeyRequestBody.
type PutApiKeyRequestBody struct {
	Description string `json:"description"`
}

// PutNotificationsStatusRequestBody defines model for PutNotificationsStatusRequestBody.
type PutNotificationsStatusRequestBody struct {
	Status PutNotificationsStatusRequestBodyStatus `json:"status"`
}

// PutNotificationsStatusRequestBodyStatus defines model for PutNotificationsStatusRequestBody.Status.
type PutNotificationsStatusRequestBodyStatus string

// PutOrgRequestBody defines model for PutOrgRequestBody.
type PutOrgRequestBody struct {
	Name string `json:"name"`
}

// PutRecipientSubscriptionRequestBody defines model for PutRecipientSubscriptionRequestBody.
type PutRecipientSubscriptionRequestBody struct {
	Severities []AlertSeverity `json:"severities"`
}

// PutRoleBindingRequestBody defines model for PutRoleBindingRequestBody.
type PutRoleBindingRequestBody struct {
	Principal     openapi_types.UUID   `json:"principal"`
	PrincipalType string               `json:"principalType"`
	RoleIds       []openapi_types.UUID `json:"roleIds"`
}

// PutServiceAccountRequestBody defines model for PutServiceAccountRequestBody.
type PutServiceAccountRequestBody struct {
	Description string `json:"description"`
}

// Recipient defines model for Recipient.
type Recipient struct {
	Config    RecipientConfig    `json:"config"`
	CreatedAt time.Time          `json:"createdAt"`
	Id        openapi_types.UUID `json:"id"`
	OrgId     openapi_types.UUID `json:"orgId"`
	UpdatedAt time.Time          `json:"updatedAt"`
}

// RecipientArray defines model for RecipientArray.
type RecipientArray = []Recipient

// RecipientConfig defines model for RecipientConfig.
type RecipientConfig struct {
	union json.RawMessage
}

// RecipientType defines model for RecipientType.
type RecipientType string

// Role defines model for Role.
type Role struct {
	Description string             `json:"description"`
	Id          openapi_types.UUID `json:"id"`
	Name        string             `json:"name"`
}

// RoleArray defines model for RoleArray.
type RoleArray = []Role

// RoleBinding defines model for RoleBinding.
type RoleBinding struct {
	Principal     openapi_types.UUID `json:"principal"`
	PrincipalType *string            `json:"principalType,omitempty"`
	RoleId        openapi_types.UUID `json:"roleId"`
	RoleName      string             `json:"roleName"`
}

// RoleBindingArray defines model for RoleBindingArray.
type RoleBindingArray = []RoleBinding

// RoleBindingPagination defines model for RoleBindingPagination.
type RoleBindingPagination struct {
	Pagination   *Pagination      `json:"pagination,omitempty"`
	RoleBindings RoleBindingArray `json:"roleBindings"`
}

// RolePagination defines model for RolePagination.
type RolePagination struct {
	Pagination *Pagination `json:"pagination,omitempty"`
	Roles      RoleArray   `json:"roles"`
}

// ServiceAccount defines model for ServiceAccount.
type ServiceAccount struct {
	ApiKeyCount uint64             `json:"apiKeyCount"`
	CreatedAt   time.Time          `json:"createdAt"`
	Description string             `json:"description"`
	Id          openapi_types.UUID `json:"id"`
	Name        string             `json:"name"`
	OrgId       openapi_types.UUID `json:"orgId"`
	Roles       []string           `json:"roles"`
	UpdatedAt   time.Time          `json:"updatedAt"`
}

// ServiceAccountArray defines model for ServiceAccountArray.
type ServiceAccountArray = []ServiceAccount

// ServiceAccountPagination defines model for ServiceAccountPagination.
type ServiceAccountPagination struct {
	Pagination      *Pagination         `json:"pagination,omitempty"`
	ServiceAccounts ServiceAccountArray `json:"serviceAccounts"`
}

// Size defines model for Size.
type Size struct {
	Size uint64 `json:"size"`
}

// SlackRecipientConfig defines model for SlackRecipientConfig.
type SlackRecipientConfig struct {
	Type       RecipientType `json:"type"`
	WebhookURL string        `json:"webhookURL"`
}

// Subscription defines model for Subscription.
type Subscription struct {
	Id          openapi_types.UUID `json:"id"`
	RecipientId openapi_types.UUID `json:"recipientId"`
	Severity    AlertSeverity      `json:"severity"`
}

// SubscriptionArray defines model for SubscriptionArray.
type SubscriptionArray = []Subscription

// User defines model for User.
type User struct {
	AuthType    AuthType           `json:"authType"`
	CreatedAt   time.Time          `json:"createdAt"`
	Email       string             `json:"email"`
	Id          uint64             `json:"id"`
	LastLoginAt *time.Time         `json:"lastLoginAt,omitempty"`
	ResourceId  openapi_types.UUID `json:"resourceId"`
	Roles       []string           `json:"roles"`
	Username    string             `json:"username"`
}

// UserArray defines model for UserArray.
type UserArray = []User

// UserPagination defines model for UserPagination.
type UserPagination struct {
	Pagination *Pagination `json:"pagination,omitempty"`
	Users      UserArray   `json:"users"`
}

// UserRecipientConfig defines model for UserRecipientConfig.
type UserRecipientConfig struct {
	Type   RecipientType      `json:"type"`
	UserId openapi_types.UUID `json:"userId"`
}

// AlreadyExistsResponse defines model for AlreadyExistsResponse.
type AlreadyExistsResponse struct {
	Msg string `json:"msg"`
}

// BadRequestResponse defines model for BadRequestResponse.
type BadRequestResponse struct {
	Msg string `json:"msg"`
}

// DefaultResponse defines model for DefaultResponse.
type DefaultResponse struct {
	Msg string `json:"msg"`
}

// FailedPreconditionResponse defines model for FailedPreconditionResponse.
type FailedPreconditionResponse struct {
	Code *string `json:"code,omitempty"`
	Msg  string  `json:"msg"`
}

// NotFoundResponse defines model for NotFoundResponse.
type NotFoundResponse struct {
	Msg string `json:"msg"`
}

// GetApiKeysParams defines parameters for GetApiKeys.
type GetApiKeysParams struct {
	Offset    *uint64            `form:"offset,omitempty" json:"offset,omitempty"`
	Limit     *uint64            `form:"limit,omitempty" json:"limit,omitempty"`
	Principal openapi_types.UUID `form:"principal" json:"principal"`
}

// GetInvitationsParams defines parameters for GetInvitations.
type GetInvitationsParams struct {
	Offset *uint64 `form:"offset,omitempty" json:"offset,omitempty"`
	Limit  *uint64 `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetNotificationsParams defines parameters for GetNotifications.
type GetNotificationsParams struct {
	Offset *uint64                      `form:"offset,omitempty" json:"offset,omitempty"`
	Limit  *uint64                      `form:"limit,omitempty" json:"limit,omitempty"`
	Order  *GetNotificationsParamsOrder `form:"order,omitempty" json:"order,omitempty"`
}

// GetNotificationsParamsOrder defines parameters for GetNotifications.
type GetNotificationsParamsOrder string

// GetRoleBindingsParams defines parameters for GetRoleBindings.
type GetRoleBindingsParams struct {
	Offset    *uint64             `form:"offset,omitempty" json:"offset,omitempty"`
	Limit     *uint64             `form:"limit,omitempty" json:"limit,omitempty"`
	Principal *openapi_types.UUID `form:"principal,omitempty" json:"principal,omitempty"`
}

// GetRolesParams defines parameters for GetRoles.
type GetRolesParams struct {
	Offset *uint64 `form:"offset,omitempty" json:"offset,omitempty"`
	Limit  *uint64 `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetServiceAccountsParams defines parameters for GetServiceAccounts.
type GetServiceAccountsParams struct {
	Offset *uint64 `form:"offset,omitempty" json:"offset,omitempty"`
	Limit  *uint64 `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetUsersParams defines parameters for GetUsers.
type GetUsersParams struct {
	Offset *uint64 `form:"offset,omitempty" json:"offset,omitempty"`
	Limit  *uint64 `form:"limit,omitempty" json:"limit,omitempty"`
}

// PostApiKeysJSONRequestBody defines body for PostApiKeys for application/json ContentType.
type PostApiKeysJSONRequestBody = PostApiKeyRequestBody

// PutApiKeysIdJSONRequestBody defines body for PutApiKeysId for application/json ContentType.
type PutApiKeysIdJSONRequestBody = PutApiKeyRequestBody

// PostInvitationsJSONRequestBody defines body for PostInvitations for application/json ContentType.
type PostInvitationsJSONRequestBody = CreateInvitationRequestBody

// PutNotificationsIdJSONRequestBody defines body for PutNotificationsId for application/json ContentType.
type PutNotificationsIdJSONRequestBody = PutNotificationsStatusRequestBody

// PutOrgsOrgIdJSONRequestBody defines body for PutOrgsOrgId for application/json ContentType.
type PutOrgsOrgIdJSONRequestBody = PutOrgRequestBody

// PostRecipientsJSONRequestBody defines body for PostRecipients for application/json ContentType.
type PostRecipientsJSONRequestBody = PostRecipientRequestBody

// PutRecipientsRecipientIdSubscriptionsJSONRequestBody defines body for PutRecipientsRecipientIdSubscriptions for application/json ContentType.
type PutRecipientsRecipientIdSubscriptionsJSONRequestBody = PutRecipientSubscriptionRequestBody

// PutRoleBindingsJSONRequestBody defines body for PutRoleBindings for application/json ContentType.
type PutRoleBindingsJSONRequestBody = PutRoleBindingRequestBody

// PostServiceAccountsJSONRequestBody defines body for PostServiceAccounts for application/json ContentType.
type PostServiceAccountsJSONRequestBody = PostServiceAccountRequestBody

// PutServiceAccountsIdJSONRequestBody defines body for PutServiceAccountsId for application/json ContentType.
type PutServiceAccountsIdJSONRequestBody = PutServiceAccountRequestBody

// PostSubscriptionsJSONRequestBody defines body for PostSubscriptions for application/json ContentType.
type PostSubscriptionsJSONRequestBody = PostSubscriptionRequestBody

// AsEmailRecipientConfig returns the union data inside the RecipientConfig as a EmailRecipientConfig
func (t RecipientConfig) AsEmailRecipientConfig() (EmailRecipientConfig, error) {
	var body EmailRecipientConfig
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromEmailRecipientConfig overwrites any union data inside the RecipientConfig as the provided EmailRecipientConfig
func (t *RecipientConfig) FromEmailRecipientConfig(v EmailRecipientConfig) error {
	v.Type = "email"
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeEmailRecipientConfig performs a merge with any union data inside the RecipientConfig, using the provided EmailRecipientConfig
func (t *RecipientConfig) MergeEmailRecipientConfig(v EmailRecipientConfig) error {
	v.Type = "email"
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsUserRecipientConfig returns the union data inside the RecipientConfig as a UserRecipientConfig
func (t RecipientConfig) AsUserRecipientConfig() (UserRecipientConfig, error) {
	var body UserRecipientConfig
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromUserRecipientConfig overwrites any union data inside the RecipientConfig as the provided UserRecipientConfig
func (t *RecipientConfig) FromUserRecipientConfig(v UserRecipientConfig) error {
	v.Type = "user"
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeUserRecipientConfig performs a merge with any union data inside the RecipientConfig, using the provided UserRecipientConfig
func (t *RecipientConfig) MergeUserRecipientConfig(v UserRecipientConfig) error {
	v.Type = "user"
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsSlackRecipientConfig returns the union data inside the RecipientConfig as a SlackRecipientConfig
func (t RecipientConfig) AsSlackRecipientConfig() (SlackRecipientConfig, error) {
	var body SlackRecipientConfig
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromSlackRecipientConfig overwrites any union data inside the RecipientConfig as the provided SlackRecipientConfig
func (t *RecipientConfig) FromSlackRecipientConfig(v SlackRecipientConfig) error {
	v.Type = "slack"
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeSlackRecipientConfig performs a merge with any union data inside the RecipientConfig, using the provided SlackRecipientConfig
func (t *RecipientConfig) MergeSlackRecipientConfig(v SlackRecipientConfig) error {
	v.Type = "slack"
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t RecipientConfig) Discriminator() (string, error) {
	var discriminator struct {
		Discriminator string `json:"type"`
	}
	err := json.Unmarshal(t.union, &discriminator)
	return discriminator.Discriminator, err
}

func (t RecipientConfig) ValueByDiscriminator() (interface{}, error) {
	discriminator, err := t.Discriminator()
	if err != nil {
		return nil, err
	}
	switch discriminator {
	case "email":
		return t.AsEmailRecipientConfig()
	case "slack":
		return t.AsSlackRecipientConfig()
	case "user":
		return t.AsUserRecipientConfig()
	default:
		return nil, errors.New("unknown discriminator value: " + discriminator)
	}
}

func (t RecipientConfig) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *RecipientConfig) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}

// RequestEditorFn  is the function signature for the RequestEditor callback function
type RequestEditorFn func(ctx context.Context, req *http.Request) error

// Doer performs HTTP requests.
//
// The standard http.Client implements this interface.
type HttpRequestDoer interface {
	Do(req *http.Request) (*http.Response, error)
}

// Client which conforms to the OpenAPI3 specification for this service.
type Client struct {
	// The endpoint of the server conforming to this interface, with scheme,
	// https://api.deepmap.com for example. This can contain a path relative
	// to the server, such as https://api.deepmap.com/dev-test, and all the
	// paths in the swagger spec will be appended to the server.
	Server string

	// Doer for performing requests, typically a *http.Client with any
	// customized settings, such as certificate chains.
	Client HttpRequestDoer

	// A list of callbacks for modifying requests which are generated before sending over
	// the network.
	RequestEditors []RequestEditorFn
}

// ClientOption allows setting custom parameters during construction
type ClientOption func(*Client) error

// Creates a new Client, with reasonable defaults
func NewClient(server string, opts ...ClientOption) (*Client, error) {
	// create a client with sane default values
	client := Client{
		Server: server,
	}
	// mutate client and add all optional params
	for _, o := range opts {
		if err := o(&client); err != nil {
			return nil, err
		}
	}
	// ensure the server URL always has a trailing slash
	if !strings.HasSuffix(client.Server, "/") {
		client.Server += "/"
	}
	// create httpClient, if not already present
	if client.Client == nil {
		client.Client = &http.Client{}
	}
	return &client, nil
}

// WithHTTPClient allows overriding the default Doer, which is
// automatically created using http.Client. This is useful for tests.
func WithHTTPClient(doer HttpRequestDoer) ClientOption {
	return func(c *Client) error {
		c.Client = doer
		return nil
	}
}

// WithRequestEditorFn allows setting up a callback function, which will be
// called right before sending the request. This can be used to mutate the request.
func WithRequestEditorFn(fn RequestEditorFn) ClientOption {
	return func(c *Client) error {
		c.RequestEditors = append(c.RequestEditors, fn)
		return nil
	}
}

// The interface specification for the client above.
type ClientInterface interface {
	// GetAlertTypes request
	GetAlertTypes(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApiKeys request
	GetApiKeys(ctx context.Context, params *GetApiKeysParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiKeysWithBody request with any body
	PostApiKeysWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiKeys(ctx context.Context, body PostApiKeysJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteApiKeysId request
	DeleteApiKeysId(ctx context.Context, id uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApiKeysId request
	GetApiKeysId(ctx context.Context, id uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutApiKeysIdWithBody request with any body
	PutApiKeysIdWithBody(ctx context.Context, id uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutApiKeysId(ctx context.Context, id uint64, body PutApiKeysIdJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetInvitations request
	GetInvitations(ctx context.Context, params *GetInvitationsParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostInvitationsWithBody request with any body
	PostInvitationsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostInvitations(ctx context.Context, body PostInvitationsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteInvitationsId request
	DeleteInvitationsId(ctx context.Context, id uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetInvitationsId request
	GetInvitationsId(ctx context.Context, id uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetNotifications request
	GetNotifications(ctx context.Context, params *GetNotificationsParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutNotificationsIdWithBody request with any body
	PutNotificationsIdWithBody(ctx context.Context, id uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutNotificationsId(ctx context.Context, id uint64, body PutNotificationsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteOrgsOrgId request
	DeleteOrgsOrgId(ctx context.Context, orgId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetOrgsOrgId request
	GetOrgsOrgId(ctx context.Context, orgId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutOrgsOrgIdWithBody request with any body
	PutOrgsOrgIdWithBody(ctx context.Context, orgId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutOrgsOrgId(ctx context.Context, orgId openapi_types.UUID, body PutOrgsOrgIdJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetRecipients request
	GetRecipients(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostRecipientsWithBody request with any body
	PostRecipientsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostRecipients(ctx context.Context, body PostRecipientsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteRecipientsRecipientId request
	DeleteRecipientsRecipientId(ctx context.Context, recipientId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutRecipientsRecipientIdSubscriptionsWithBody request with any body
	PutRecipientsRecipientIdSubscriptionsWithBody(ctx context.Context, recipientId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutRecipientsRecipientIdSubscriptions(ctx context.Context, recipientId openapi_types.UUID, body PutRecipientsRecipientIdSubscriptionsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostRecipientsRecipientIdTest request
	PostRecipientsRecipientIdTest(ctx context.Context, recipientId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetRoleBindings request
	GetRoleBindings(ctx context.Context, params *GetRoleBindingsParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutRoleBindingsWithBody request with any body
	PutRoleBindingsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutRoleBindings(ctx context.Context, body PutRoleBindingsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetRoles request
	GetRoles(ctx context.Context, params *GetRolesParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetServiceAccounts request
	GetServiceAccounts(ctx context.Context, params *GetServiceAccountsParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostServiceAccountsWithBody request with any body
	PostServiceAccountsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostServiceAccounts(ctx context.Context, body PostServiceAccountsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteServiceAccountsId request
	DeleteServiceAccountsId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetServiceAccountsId request
	GetServiceAccountsId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutServiceAccountsIdWithBody request with any body
	PutServiceAccountsIdWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutServiceAccountsId(ctx context.Context, id openapi_types.UUID, body PutServiceAccountsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetSubscriptions request
	GetSubscriptions(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostSubscriptionsWithBody request with any body
	PostSubscriptionsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostSubscriptions(ctx context.Context, body PostSubscriptionsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteSubscriptionsSubId request
	DeleteSubscriptionsSubId(ctx context.Context, subId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetUsers request
	GetUsers(ctx context.Context, params *GetUsersParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteUsersId request
	DeleteUsersId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetUsersId request
	GetUsersId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)
}

func (c *Client) GetAlertTypes(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetAlertTypesRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApiKeys(ctx context.Context, params *GetApiKeysParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApiKeysRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiKeysWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiKeysRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiKeys(ctx context.Context, body PostApiKeysJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiKeysRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteApiKeysId(ctx context.Context, id uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteApiKeysIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApiKeysId(ctx context.Context, id uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApiKeysIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutApiKeysIdWithBody(ctx context.Context, id uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutApiKeysIdRequestWithBody(c.Server, id, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutApiKeysId(ctx context.Context, id uint64, body PutApiKeysIdJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutApiKeysIdRequest(c.Server, id, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetInvitations(ctx context.Context, params *GetInvitationsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetInvitationsRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostInvitationsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostInvitationsRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostInvitations(ctx context.Context, body PostInvitationsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostInvitationsRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteInvitationsId(ctx context.Context, id uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteInvitationsIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetInvitationsId(ctx context.Context, id uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetInvitationsIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetNotifications(ctx context.Context, params *GetNotificationsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetNotificationsRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutNotificationsIdWithBody(ctx context.Context, id uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutNotificationsIdRequestWithBody(c.Server, id, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutNotificationsId(ctx context.Context, id uint64, body PutNotificationsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutNotificationsIdRequest(c.Server, id, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteOrgsOrgId(ctx context.Context, orgId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteOrgsOrgIdRequest(c.Server, orgId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetOrgsOrgId(ctx context.Context, orgId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetOrgsOrgIdRequest(c.Server, orgId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutOrgsOrgIdWithBody(ctx context.Context, orgId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutOrgsOrgIdRequestWithBody(c.Server, orgId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutOrgsOrgId(ctx context.Context, orgId openapi_types.UUID, body PutOrgsOrgIdJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutOrgsOrgIdRequest(c.Server, orgId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetRecipients(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetRecipientsRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostRecipientsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostRecipientsRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostRecipients(ctx context.Context, body PostRecipientsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostRecipientsRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteRecipientsRecipientId(ctx context.Context, recipientId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteRecipientsRecipientIdRequest(c.Server, recipientId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutRecipientsRecipientIdSubscriptionsWithBody(ctx context.Context, recipientId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutRecipientsRecipientIdSubscriptionsRequestWithBody(c.Server, recipientId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutRecipientsRecipientIdSubscriptions(ctx context.Context, recipientId openapi_types.UUID, body PutRecipientsRecipientIdSubscriptionsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutRecipientsRecipientIdSubscriptionsRequest(c.Server, recipientId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostRecipientsRecipientIdTest(ctx context.Context, recipientId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostRecipientsRecipientIdTestRequest(c.Server, recipientId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetRoleBindings(ctx context.Context, params *GetRoleBindingsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetRoleBindingsRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutRoleBindingsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutRoleBindingsRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutRoleBindings(ctx context.Context, body PutRoleBindingsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutRoleBindingsRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetRoles(ctx context.Context, params *GetRolesParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetRolesRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetServiceAccounts(ctx context.Context, params *GetServiceAccountsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetServiceAccountsRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostServiceAccountsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostServiceAccountsRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostServiceAccounts(ctx context.Context, body PostServiceAccountsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostServiceAccountsRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteServiceAccountsId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteServiceAccountsIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetServiceAccountsId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetServiceAccountsIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutServiceAccountsIdWithBody(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutServiceAccountsIdRequestWithBody(c.Server, id, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutServiceAccountsId(ctx context.Context, id openapi_types.UUID, body PutServiceAccountsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutServiceAccountsIdRequest(c.Server, id, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetSubscriptions(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetSubscriptionsRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostSubscriptionsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostSubscriptionsRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostSubscriptions(ctx context.Context, body PostSubscriptionsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostSubscriptionsRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteSubscriptionsSubId(ctx context.Context, subId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteSubscriptionsSubIdRequest(c.Server, subId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetUsers(ctx context.Context, params *GetUsersParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetUsersRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteUsersId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteUsersIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetUsersId(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetUsersIdRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

// NewGetAlertTypesRequest generates requests for GetAlertTypes
func NewGetAlertTypesRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/alertTypes")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetApiKeysRequest generates requests for GetApiKeys
func NewGetApiKeysRequest(server string, params *GetApiKeysParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/apiKeys")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "principal", runtime.ParamLocationQuery, params.Principal); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostApiKeysRequest calls the generic PostApiKeys builder with application/json body
func NewPostApiKeysRequest(server string, body PostApiKeysJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiKeysRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiKeysRequestWithBody generates requests for PostApiKeys with any type of body
func NewPostApiKeysRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/apiKeys")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteApiKeysIdRequest generates requests for DeleteApiKeysId
func NewDeleteApiKeysIdRequest(server string, id uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/apiKeys/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetApiKeysIdRequest generates requests for GetApiKeysId
func NewGetApiKeysIdRequest(server string, id uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/apiKeys/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutApiKeysIdRequest calls the generic PutApiKeysId builder with application/json body
func NewPutApiKeysIdRequest(server string, id uint64, body PutApiKeysIdJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPutApiKeysIdRequestWithBody(server, id, "application/json", bodyReader)
}

// NewPutApiKeysIdRequestWithBody generates requests for PutApiKeysId with any type of body
func NewPutApiKeysIdRequestWithBody(server string, id uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/apiKeys/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetInvitationsRequest generates requests for GetInvitations
func NewGetInvitationsRequest(server string, params *GetInvitationsParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/invitations")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostInvitationsRequest calls the generic PostInvitations builder with application/json body
func NewPostInvitationsRequest(server string, body PostInvitationsJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostInvitationsRequestWithBody(server, "application/json", bodyReader)
}

// NewPostInvitationsRequestWithBody generates requests for PostInvitations with any type of body
func NewPostInvitationsRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/invitations")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteInvitationsIdRequest generates requests for DeleteInvitationsId
func NewDeleteInvitationsIdRequest(server string, id uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/invitations/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetInvitationsIdRequest generates requests for GetInvitationsId
func NewGetInvitationsIdRequest(server string, id uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/invitations/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetNotificationsRequest generates requests for GetNotifications
func NewGetNotificationsRequest(server string, params *GetNotificationsParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/notifications")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Order != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "order", runtime.ParamLocationQuery, *params.Order); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutNotificationsIdRequest calls the generic PutNotificationsId builder with application/json body
func NewPutNotificationsIdRequest(server string, id uint64, body PutNotificationsIdJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPutNotificationsIdRequestWithBody(server, id, "application/json", bodyReader)
}

// NewPutNotificationsIdRequestWithBody generates requests for PutNotificationsId with any type of body
func NewPutNotificationsIdRequestWithBody(server string, id uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/notifications/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteOrgsOrgIdRequest generates requests for DeleteOrgsOrgId
func NewDeleteOrgsOrgIdRequest(server string, orgId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "orgId", runtime.ParamLocationPath, orgId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/orgs/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetOrgsOrgIdRequest generates requests for GetOrgsOrgId
func NewGetOrgsOrgIdRequest(server string, orgId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "orgId", runtime.ParamLocationPath, orgId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/orgs/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutOrgsOrgIdRequest calls the generic PutOrgsOrgId builder with application/json body
func NewPutOrgsOrgIdRequest(server string, orgId openapi_types.UUID, body PutOrgsOrgIdJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPutOrgsOrgIdRequestWithBody(server, orgId, "application/json", bodyReader)
}

// NewPutOrgsOrgIdRequestWithBody generates requests for PutOrgsOrgId with any type of body
func NewPutOrgsOrgIdRequestWithBody(server string, orgId openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "orgId", runtime.ParamLocationPath, orgId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/orgs/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetRecipientsRequest generates requests for GetRecipients
func NewGetRecipientsRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/recipients")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostRecipientsRequest calls the generic PostRecipients builder with application/json body
func NewPostRecipientsRequest(server string, body PostRecipientsJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostRecipientsRequestWithBody(server, "application/json", bodyReader)
}

// NewPostRecipientsRequestWithBody generates requests for PostRecipients with any type of body
func NewPostRecipientsRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/recipients")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteRecipientsRecipientIdRequest generates requests for DeleteRecipientsRecipientId
func NewDeleteRecipientsRecipientIdRequest(server string, recipientId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "recipientId", runtime.ParamLocationPath, recipientId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/recipients/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutRecipientsRecipientIdSubscriptionsRequest calls the generic PutRecipientsRecipientIdSubscriptions builder with application/json body
func NewPutRecipientsRecipientIdSubscriptionsRequest(server string, recipientId openapi_types.UUID, body PutRecipientsRecipientIdSubscriptionsJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPutRecipientsRecipientIdSubscriptionsRequestWithBody(server, recipientId, "application/json", bodyReader)
}

// NewPutRecipientsRecipientIdSubscriptionsRequestWithBody generates requests for PutRecipientsRecipientIdSubscriptions with any type of body
func NewPutRecipientsRecipientIdSubscriptionsRequestWithBody(server string, recipientId openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "recipientId", runtime.ParamLocationPath, recipientId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/recipients/%s/subscriptions", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostRecipientsRecipientIdTestRequest generates requests for PostRecipientsRecipientIdTest
func NewPostRecipientsRecipientIdTestRequest(server string, recipientId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "recipientId", runtime.ParamLocationPath, recipientId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/recipients/%s/test", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetRoleBindingsRequest generates requests for GetRoleBindings
func NewGetRoleBindingsRequest(server string, params *GetRoleBindingsParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/roleBindings")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Principal != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "principal", runtime.ParamLocationQuery, *params.Principal); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutRoleBindingsRequest calls the generic PutRoleBindings builder with application/json body
func NewPutRoleBindingsRequest(server string, body PutRoleBindingsJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPutRoleBindingsRequestWithBody(server, "application/json", bodyReader)
}

// NewPutRoleBindingsRequestWithBody generates requests for PutRoleBindings with any type of body
func NewPutRoleBindingsRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/roleBindings")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetRolesRequest generates requests for GetRoles
func NewGetRolesRequest(server string, params *GetRolesParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/roles")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetServiceAccountsRequest generates requests for GetServiceAccounts
func NewGetServiceAccountsRequest(server string, params *GetServiceAccountsParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/serviceAccounts")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostServiceAccountsRequest calls the generic PostServiceAccounts builder with application/json body
func NewPostServiceAccountsRequest(server string, body PostServiceAccountsJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostServiceAccountsRequestWithBody(server, "application/json", bodyReader)
}

// NewPostServiceAccountsRequestWithBody generates requests for PostServiceAccounts with any type of body
func NewPostServiceAccountsRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/serviceAccounts")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteServiceAccountsIdRequest generates requests for DeleteServiceAccountsId
func NewDeleteServiceAccountsIdRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/serviceAccounts/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetServiceAccountsIdRequest generates requests for GetServiceAccountsId
func NewGetServiceAccountsIdRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/serviceAccounts/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutServiceAccountsIdRequest calls the generic PutServiceAccountsId builder with application/json body
func NewPutServiceAccountsIdRequest(server string, id openapi_types.UUID, body PutServiceAccountsIdJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPutServiceAccountsIdRequestWithBody(server, id, "application/json", bodyReader)
}

// NewPutServiceAccountsIdRequestWithBody generates requests for PutServiceAccountsId with any type of body
func NewPutServiceAccountsIdRequestWithBody(server string, id openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/serviceAccounts/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetSubscriptionsRequest generates requests for GetSubscriptions
func NewGetSubscriptionsRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/subscriptions")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostSubscriptionsRequest calls the generic PostSubscriptions builder with application/json body
func NewPostSubscriptionsRequest(server string, body PostSubscriptionsJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostSubscriptionsRequestWithBody(server, "application/json", bodyReader)
}

// NewPostSubscriptionsRequestWithBody generates requests for PostSubscriptions with any type of body
func NewPostSubscriptionsRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/subscriptions")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteSubscriptionsSubIdRequest generates requests for DeleteSubscriptionsSubId
func NewDeleteSubscriptionsSubIdRequest(server string, subId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "subId", runtime.ParamLocationPath, subId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/subscriptions/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetUsersRequest generates requests for GetUsers
func NewGetUsersRequest(server string, params *GetUsersParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/users")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewDeleteUsersIdRequest generates requests for DeleteUsersId
func NewDeleteUsersIdRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/users/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetUsersIdRequest generates requests for GetUsersId
func NewGetUsersIdRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/users/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

func (c *Client) applyEditors(ctx context.Context, req *http.Request, additionalEditors []RequestEditorFn) error {
	for _, r := range c.RequestEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	for _, r := range additionalEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	return nil
}

// ClientWithResponses builds on ClientInterface to offer response payloads
type ClientWithResponses struct {
	ClientInterface
}

// NewClientWithResponses creates a new ClientWithResponses, which wraps
// Client with return type handling
func NewClientWithResponses(server string, opts ...ClientOption) (*ClientWithResponses, error) {
	client, err := NewClient(server, opts...)
	if err != nil {
		return nil, err
	}
	return &ClientWithResponses{client}, nil
}

// WithBaseURL overrides the baseURL.
func WithBaseURL(baseURL string) ClientOption {
	return func(c *Client) error {
		newBaseURL, err := url.Parse(baseURL)
		if err != nil {
			return err
		}
		c.Server = newBaseURL.String()
		return nil
	}
}

// ClientWithResponsesInterface is the interface specification for the client with responses above.
type ClientWithResponsesInterface interface {
	// GetAlertTypesWithResponse request
	GetAlertTypesWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetAlertTypesResponse, error)

	// GetApiKeysWithResponse request
	GetApiKeysWithResponse(ctx context.Context, params *GetApiKeysParams, reqEditors ...RequestEditorFn) (*GetApiKeysResponse, error)

	// PostApiKeysWithBodyWithResponse request with any body
	PostApiKeysWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiKeysResponse, error)

	PostApiKeysWithResponse(ctx context.Context, body PostApiKeysJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiKeysResponse, error)

	// DeleteApiKeysIdWithResponse request
	DeleteApiKeysIdWithResponse(ctx context.Context, id uint64, reqEditors ...RequestEditorFn) (*DeleteApiKeysIdResponse, error)

	// GetApiKeysIdWithResponse request
	GetApiKeysIdWithResponse(ctx context.Context, id uint64, reqEditors ...RequestEditorFn) (*GetApiKeysIdResponse, error)

	// PutApiKeysIdWithBodyWithResponse request with any body
	PutApiKeysIdWithBodyWithResponse(ctx context.Context, id uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutApiKeysIdResponse, error)

	PutApiKeysIdWithResponse(ctx context.Context, id uint64, body PutApiKeysIdJSONRequestBody, reqEditors ...RequestEditorFn) (*PutApiKeysIdResponse, error)

	// GetInvitationsWithResponse request
	GetInvitationsWithResponse(ctx context.Context, params *GetInvitationsParams, reqEditors ...RequestEditorFn) (*GetInvitationsResponse, error)

	// PostInvitationsWithBodyWithResponse request with any body
	PostInvitationsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostInvitationsResponse, error)

	PostInvitationsWithResponse(ctx context.Context, body PostInvitationsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostInvitationsResponse, error)

	// DeleteInvitationsIdWithResponse request
	DeleteInvitationsIdWithResponse(ctx context.Context, id uint64, reqEditors ...RequestEditorFn) (*DeleteInvitationsIdResponse, error)

	// GetInvitationsIdWithResponse request
	GetInvitationsIdWithResponse(ctx context.Context, id uint64, reqEditors ...RequestEditorFn) (*GetInvitationsIdResponse, error)

	// GetNotificationsWithResponse request
	GetNotificationsWithResponse(ctx context.Context, params *GetNotificationsParams, reqEditors ...RequestEditorFn) (*GetNotificationsResponse, error)

	// PutNotificationsIdWithBodyWithResponse request with any body
	PutNotificationsIdWithBodyWithResponse(ctx context.Context, id uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutNotificationsIdResponse, error)

	PutNotificationsIdWithResponse(ctx context.Context, id uint64, body PutNotificationsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*PutNotificationsIdResponse, error)

	// DeleteOrgsOrgIdWithResponse request
	DeleteOrgsOrgIdWithResponse(ctx context.Context, orgId openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteOrgsOrgIdResponse, error)

	// GetOrgsOrgIdWithResponse request
	GetOrgsOrgIdWithResponse(ctx context.Context, orgId openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetOrgsOrgIdResponse, error)

	// PutOrgsOrgIdWithBodyWithResponse request with any body
	PutOrgsOrgIdWithBodyWithResponse(ctx context.Context, orgId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutOrgsOrgIdResponse, error)

	PutOrgsOrgIdWithResponse(ctx context.Context, orgId openapi_types.UUID, body PutOrgsOrgIdJSONRequestBody, reqEditors ...RequestEditorFn) (*PutOrgsOrgIdResponse, error)

	// GetRecipientsWithResponse request
	GetRecipientsWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetRecipientsResponse, error)

	// PostRecipientsWithBodyWithResponse request with any body
	PostRecipientsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostRecipientsResponse, error)

	PostRecipientsWithResponse(ctx context.Context, body PostRecipientsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostRecipientsResponse, error)

	// DeleteRecipientsRecipientIdWithResponse request
	DeleteRecipientsRecipientIdWithResponse(ctx context.Context, recipientId openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteRecipientsRecipientIdResponse, error)

	// PutRecipientsRecipientIdSubscriptionsWithBodyWithResponse request with any body
	PutRecipientsRecipientIdSubscriptionsWithBodyWithResponse(ctx context.Context, recipientId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutRecipientsRecipientIdSubscriptionsResponse, error)

	PutRecipientsRecipientIdSubscriptionsWithResponse(ctx context.Context, recipientId openapi_types.UUID, body PutRecipientsRecipientIdSubscriptionsJSONRequestBody, reqEditors ...RequestEditorFn) (*PutRecipientsRecipientIdSubscriptionsResponse, error)

	// PostRecipientsRecipientIdTestWithResponse request
	PostRecipientsRecipientIdTestWithResponse(ctx context.Context, recipientId openapi_types.UUID, reqEditors ...RequestEditorFn) (*PostRecipientsRecipientIdTestResponse, error)

	// GetRoleBindingsWithResponse request
	GetRoleBindingsWithResponse(ctx context.Context, params *GetRoleBindingsParams, reqEditors ...RequestEditorFn) (*GetRoleBindingsResponse, error)

	// PutRoleBindingsWithBodyWithResponse request with any body
	PutRoleBindingsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutRoleBindingsResponse, error)

	PutRoleBindingsWithResponse(ctx context.Context, body PutRoleBindingsJSONRequestBody, reqEditors ...RequestEditorFn) (*PutRoleBindingsResponse, error)

	// GetRolesWithResponse request
	GetRolesWithResponse(ctx context.Context, params *GetRolesParams, reqEditors ...RequestEditorFn) (*GetRolesResponse, error)

	// GetServiceAccountsWithResponse request
	GetServiceAccountsWithResponse(ctx context.Context, params *GetServiceAccountsParams, reqEditors ...RequestEditorFn) (*GetServiceAccountsResponse, error)

	// PostServiceAccountsWithBodyWithResponse request with any body
	PostServiceAccountsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostServiceAccountsResponse, error)

	PostServiceAccountsWithResponse(ctx context.Context, body PostServiceAccountsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostServiceAccountsResponse, error)

	// DeleteServiceAccountsIdWithResponse request
	DeleteServiceAccountsIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteServiceAccountsIdResponse, error)

	// GetServiceAccountsIdWithResponse request
	GetServiceAccountsIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetServiceAccountsIdResponse, error)

	// PutServiceAccountsIdWithBodyWithResponse request with any body
	PutServiceAccountsIdWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutServiceAccountsIdResponse, error)

	PutServiceAccountsIdWithResponse(ctx context.Context, id openapi_types.UUID, body PutServiceAccountsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*PutServiceAccountsIdResponse, error)

	// GetSubscriptionsWithResponse request
	GetSubscriptionsWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetSubscriptionsResponse, error)

	// PostSubscriptionsWithBodyWithResponse request with any body
	PostSubscriptionsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostSubscriptionsResponse, error)

	PostSubscriptionsWithResponse(ctx context.Context, body PostSubscriptionsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostSubscriptionsResponse, error)

	// DeleteSubscriptionsSubIdWithResponse request
	DeleteSubscriptionsSubIdWithResponse(ctx context.Context, subId openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteSubscriptionsSubIdResponse, error)

	// GetUsersWithResponse request
	GetUsersWithResponse(ctx context.Context, params *GetUsersParams, reqEditors ...RequestEditorFn) (*GetUsersResponse, error)

	// DeleteUsersIdWithResponse request
	DeleteUsersIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteUsersIdResponse, error)

	// GetUsersIdWithResponse request
	GetUsersIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetUsersIdResponse, error)
}

type GetAlertTypesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *AlertTypeArray
}

// Status returns HTTPResponse.Status
func (r GetAlertTypesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetAlertTypesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApiKeysResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ApiKeyPagination
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r GetApiKeysResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApiKeysResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiKeysResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ApiKey
	JSON400      *BadRequestResponse
}

// Status returns HTTPResponse.Status
func (r PostApiKeysResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiKeysResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteApiKeysIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r DeleteApiKeysIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteApiKeysIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApiKeysIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ApiKey
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r GetApiKeysIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApiKeysIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutApiKeysIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ApiKey
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PutApiKeysIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutApiKeysIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetInvitationsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *InvitationPagination
}

// Status returns HTTPResponse.Status
func (r GetInvitationsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetInvitationsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostInvitationsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Invitation
	JSON400      *BadRequestResponse
	JSON409      *AlreadyExistsResponse
}

// Status returns HTTPResponse.Status
func (r PostInvitationsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostInvitationsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteInvitationsIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r DeleteInvitationsIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteInvitationsIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetInvitationsIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Invitation
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r GetInvitationsIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetInvitationsIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetNotificationsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *NotificationPagination
}

// Status returns HTTPResponse.Status
func (r GetNotificationsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetNotificationsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutNotificationsIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PutNotificationsIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutNotificationsIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteOrgsOrgIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r DeleteOrgsOrgIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteOrgsOrgIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetOrgsOrgIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Org
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r GetOrgsOrgIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetOrgsOrgIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutOrgsOrgIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PutOrgsOrgIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutOrgsOrgIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetRecipientsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *RecipientArray
}

// Status returns HTTPResponse.Status
func (r GetRecipientsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetRecipientsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostRecipientsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *BadRequestResponse
}

// Status returns HTTPResponse.Status
func (r PostRecipientsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostRecipientsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteRecipientsRecipientIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r DeleteRecipientsRecipientIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteRecipientsRecipientIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutRecipientsRecipientIdSubscriptionsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PutRecipientsRecipientIdSubscriptionsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutRecipientsRecipientIdSubscriptionsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostRecipientsRecipientIdTestResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *BadRequestResponse
}

// Status returns HTTPResponse.Status
func (r PostRecipientsRecipientIdTestResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostRecipientsRecipientIdTestResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetRoleBindingsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *RoleBindingPagination
}

// Status returns HTTPResponse.Status
func (r GetRoleBindingsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetRoleBindingsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutRoleBindingsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *BadRequestResponse
}

// Status returns HTTPResponse.Status
func (r PutRoleBindingsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutRoleBindingsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetRolesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *RolePagination
}

// Status returns HTTPResponse.Status
func (r GetRolesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetRolesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetServiceAccountsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ServiceAccountPagination
}

// Status returns HTTPResponse.Status
func (r GetServiceAccountsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetServiceAccountsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostServiceAccountsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ServiceAccount
	JSON400      *BadRequestResponse
	JSON409      *AlreadyExistsResponse
}

// Status returns HTTPResponse.Status
func (r PostServiceAccountsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostServiceAccountsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteServiceAccountsIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r DeleteServiceAccountsIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteServiceAccountsIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetServiceAccountsIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ServiceAccount
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r GetServiceAccountsIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetServiceAccountsIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutServiceAccountsIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PutServiceAccountsIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutServiceAccountsIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetSubscriptionsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *SubscriptionArray
}

// Status returns HTTPResponse.Status
func (r GetSubscriptionsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetSubscriptionsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostSubscriptionsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *BadRequestResponse
}

// Status returns HTTPResponse.Status
func (r PostSubscriptionsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostSubscriptionsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteSubscriptionsSubIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *BadRequestResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r DeleteSubscriptionsSubIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteSubscriptionsSubIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetUsersResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *UserPagination
}

// Status returns HTTPResponse.Status
func (r GetUsersResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetUsersResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteUsersIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r DeleteUsersIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteUsersIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetUsersIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *User
}

// Status returns HTTPResponse.Status
func (r GetUsersIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetUsersIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

// GetAlertTypesWithResponse request returning *GetAlertTypesResponse
func (c *ClientWithResponses) GetAlertTypesWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetAlertTypesResponse, error) {
	rsp, err := c.GetAlertTypes(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetAlertTypesResponse(rsp)
}

// GetApiKeysWithResponse request returning *GetApiKeysResponse
func (c *ClientWithResponses) GetApiKeysWithResponse(ctx context.Context, params *GetApiKeysParams, reqEditors ...RequestEditorFn) (*GetApiKeysResponse, error) {
	rsp, err := c.GetApiKeys(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApiKeysResponse(rsp)
}

// PostApiKeysWithBodyWithResponse request with arbitrary body returning *PostApiKeysResponse
func (c *ClientWithResponses) PostApiKeysWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiKeysResponse, error) {
	rsp, err := c.PostApiKeysWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiKeysResponse(rsp)
}

func (c *ClientWithResponses) PostApiKeysWithResponse(ctx context.Context, body PostApiKeysJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiKeysResponse, error) {
	rsp, err := c.PostApiKeys(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiKeysResponse(rsp)
}

// DeleteApiKeysIdWithResponse request returning *DeleteApiKeysIdResponse
func (c *ClientWithResponses) DeleteApiKeysIdWithResponse(ctx context.Context, id uint64, reqEditors ...RequestEditorFn) (*DeleteApiKeysIdResponse, error) {
	rsp, err := c.DeleteApiKeysId(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteApiKeysIdResponse(rsp)
}

// GetApiKeysIdWithResponse request returning *GetApiKeysIdResponse
func (c *ClientWithResponses) GetApiKeysIdWithResponse(ctx context.Context, id uint64, reqEditors ...RequestEditorFn) (*GetApiKeysIdResponse, error) {
	rsp, err := c.GetApiKeysId(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApiKeysIdResponse(rsp)
}

// PutApiKeysIdWithBodyWithResponse request with arbitrary body returning *PutApiKeysIdResponse
func (c *ClientWithResponses) PutApiKeysIdWithBodyWithResponse(ctx context.Context, id uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutApiKeysIdResponse, error) {
	rsp, err := c.PutApiKeysIdWithBody(ctx, id, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutApiKeysIdResponse(rsp)
}

func (c *ClientWithResponses) PutApiKeysIdWithResponse(ctx context.Context, id uint64, body PutApiKeysIdJSONRequestBody, reqEditors ...RequestEditorFn) (*PutApiKeysIdResponse, error) {
	rsp, err := c.PutApiKeysId(ctx, id, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutApiKeysIdResponse(rsp)
}

// GetInvitationsWithResponse request returning *GetInvitationsResponse
func (c *ClientWithResponses) GetInvitationsWithResponse(ctx context.Context, params *GetInvitationsParams, reqEditors ...RequestEditorFn) (*GetInvitationsResponse, error) {
	rsp, err := c.GetInvitations(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetInvitationsResponse(rsp)
}

// PostInvitationsWithBodyWithResponse request with arbitrary body returning *PostInvitationsResponse
func (c *ClientWithResponses) PostInvitationsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostInvitationsResponse, error) {
	rsp, err := c.PostInvitationsWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostInvitationsResponse(rsp)
}

func (c *ClientWithResponses) PostInvitationsWithResponse(ctx context.Context, body PostInvitationsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostInvitationsResponse, error) {
	rsp, err := c.PostInvitations(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostInvitationsResponse(rsp)
}

// DeleteInvitationsIdWithResponse request returning *DeleteInvitationsIdResponse
func (c *ClientWithResponses) DeleteInvitationsIdWithResponse(ctx context.Context, id uint64, reqEditors ...RequestEditorFn) (*DeleteInvitationsIdResponse, error) {
	rsp, err := c.DeleteInvitationsId(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteInvitationsIdResponse(rsp)
}

// GetInvitationsIdWithResponse request returning *GetInvitationsIdResponse
func (c *ClientWithResponses) GetInvitationsIdWithResponse(ctx context.Context, id uint64, reqEditors ...RequestEditorFn) (*GetInvitationsIdResponse, error) {
	rsp, err := c.GetInvitationsId(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetInvitationsIdResponse(rsp)
}

// GetNotificationsWithResponse request returning *GetNotificationsResponse
func (c *ClientWithResponses) GetNotificationsWithResponse(ctx context.Context, params *GetNotificationsParams, reqEditors ...RequestEditorFn) (*GetNotificationsResponse, error) {
	rsp, err := c.GetNotifications(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetNotificationsResponse(rsp)
}

// PutNotificationsIdWithBodyWithResponse request with arbitrary body returning *PutNotificationsIdResponse
func (c *ClientWithResponses) PutNotificationsIdWithBodyWithResponse(ctx context.Context, id uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutNotificationsIdResponse, error) {
	rsp, err := c.PutNotificationsIdWithBody(ctx, id, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutNotificationsIdResponse(rsp)
}

func (c *ClientWithResponses) PutNotificationsIdWithResponse(ctx context.Context, id uint64, body PutNotificationsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*PutNotificationsIdResponse, error) {
	rsp, err := c.PutNotificationsId(ctx, id, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutNotificationsIdResponse(rsp)
}

// DeleteOrgsOrgIdWithResponse request returning *DeleteOrgsOrgIdResponse
func (c *ClientWithResponses) DeleteOrgsOrgIdWithResponse(ctx context.Context, orgId openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteOrgsOrgIdResponse, error) {
	rsp, err := c.DeleteOrgsOrgId(ctx, orgId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteOrgsOrgIdResponse(rsp)
}

// GetOrgsOrgIdWithResponse request returning *GetOrgsOrgIdResponse
func (c *ClientWithResponses) GetOrgsOrgIdWithResponse(ctx context.Context, orgId openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetOrgsOrgIdResponse, error) {
	rsp, err := c.GetOrgsOrgId(ctx, orgId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetOrgsOrgIdResponse(rsp)
}

// PutOrgsOrgIdWithBodyWithResponse request with arbitrary body returning *PutOrgsOrgIdResponse
func (c *ClientWithResponses) PutOrgsOrgIdWithBodyWithResponse(ctx context.Context, orgId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutOrgsOrgIdResponse, error) {
	rsp, err := c.PutOrgsOrgIdWithBody(ctx, orgId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutOrgsOrgIdResponse(rsp)
}

func (c *ClientWithResponses) PutOrgsOrgIdWithResponse(ctx context.Context, orgId openapi_types.UUID, body PutOrgsOrgIdJSONRequestBody, reqEditors ...RequestEditorFn) (*PutOrgsOrgIdResponse, error) {
	rsp, err := c.PutOrgsOrgId(ctx, orgId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutOrgsOrgIdResponse(rsp)
}

// GetRecipientsWithResponse request returning *GetRecipientsResponse
func (c *ClientWithResponses) GetRecipientsWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetRecipientsResponse, error) {
	rsp, err := c.GetRecipients(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetRecipientsResponse(rsp)
}

// PostRecipientsWithBodyWithResponse request with arbitrary body returning *PostRecipientsResponse
func (c *ClientWithResponses) PostRecipientsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostRecipientsResponse, error) {
	rsp, err := c.PostRecipientsWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostRecipientsResponse(rsp)
}

func (c *ClientWithResponses) PostRecipientsWithResponse(ctx context.Context, body PostRecipientsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostRecipientsResponse, error) {
	rsp, err := c.PostRecipients(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostRecipientsResponse(rsp)
}

// DeleteRecipientsRecipientIdWithResponse request returning *DeleteRecipientsRecipientIdResponse
func (c *ClientWithResponses) DeleteRecipientsRecipientIdWithResponse(ctx context.Context, recipientId openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteRecipientsRecipientIdResponse, error) {
	rsp, err := c.DeleteRecipientsRecipientId(ctx, recipientId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteRecipientsRecipientIdResponse(rsp)
}

// PutRecipientsRecipientIdSubscriptionsWithBodyWithResponse request with arbitrary body returning *PutRecipientsRecipientIdSubscriptionsResponse
func (c *ClientWithResponses) PutRecipientsRecipientIdSubscriptionsWithBodyWithResponse(ctx context.Context, recipientId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutRecipientsRecipientIdSubscriptionsResponse, error) {
	rsp, err := c.PutRecipientsRecipientIdSubscriptionsWithBody(ctx, recipientId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutRecipientsRecipientIdSubscriptionsResponse(rsp)
}

func (c *ClientWithResponses) PutRecipientsRecipientIdSubscriptionsWithResponse(ctx context.Context, recipientId openapi_types.UUID, body PutRecipientsRecipientIdSubscriptionsJSONRequestBody, reqEditors ...RequestEditorFn) (*PutRecipientsRecipientIdSubscriptionsResponse, error) {
	rsp, err := c.PutRecipientsRecipientIdSubscriptions(ctx, recipientId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutRecipientsRecipientIdSubscriptionsResponse(rsp)
}

// PostRecipientsRecipientIdTestWithResponse request returning *PostRecipientsRecipientIdTestResponse
func (c *ClientWithResponses) PostRecipientsRecipientIdTestWithResponse(ctx context.Context, recipientId openapi_types.UUID, reqEditors ...RequestEditorFn) (*PostRecipientsRecipientIdTestResponse, error) {
	rsp, err := c.PostRecipientsRecipientIdTest(ctx, recipientId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostRecipientsRecipientIdTestResponse(rsp)
}

// GetRoleBindingsWithResponse request returning *GetRoleBindingsResponse
func (c *ClientWithResponses) GetRoleBindingsWithResponse(ctx context.Context, params *GetRoleBindingsParams, reqEditors ...RequestEditorFn) (*GetRoleBindingsResponse, error) {
	rsp, err := c.GetRoleBindings(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetRoleBindingsResponse(rsp)
}

// PutRoleBindingsWithBodyWithResponse request with arbitrary body returning *PutRoleBindingsResponse
func (c *ClientWithResponses) PutRoleBindingsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutRoleBindingsResponse, error) {
	rsp, err := c.PutRoleBindingsWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutRoleBindingsResponse(rsp)
}

func (c *ClientWithResponses) PutRoleBindingsWithResponse(ctx context.Context, body PutRoleBindingsJSONRequestBody, reqEditors ...RequestEditorFn) (*PutRoleBindingsResponse, error) {
	rsp, err := c.PutRoleBindings(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutRoleBindingsResponse(rsp)
}

// GetRolesWithResponse request returning *GetRolesResponse
func (c *ClientWithResponses) GetRolesWithResponse(ctx context.Context, params *GetRolesParams, reqEditors ...RequestEditorFn) (*GetRolesResponse, error) {
	rsp, err := c.GetRoles(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetRolesResponse(rsp)
}

// GetServiceAccountsWithResponse request returning *GetServiceAccountsResponse
func (c *ClientWithResponses) GetServiceAccountsWithResponse(ctx context.Context, params *GetServiceAccountsParams, reqEditors ...RequestEditorFn) (*GetServiceAccountsResponse, error) {
	rsp, err := c.GetServiceAccounts(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetServiceAccountsResponse(rsp)
}

// PostServiceAccountsWithBodyWithResponse request with arbitrary body returning *PostServiceAccountsResponse
func (c *ClientWithResponses) PostServiceAccountsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostServiceAccountsResponse, error) {
	rsp, err := c.PostServiceAccountsWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostServiceAccountsResponse(rsp)
}

func (c *ClientWithResponses) PostServiceAccountsWithResponse(ctx context.Context, body PostServiceAccountsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostServiceAccountsResponse, error) {
	rsp, err := c.PostServiceAccounts(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostServiceAccountsResponse(rsp)
}

// DeleteServiceAccountsIdWithResponse request returning *DeleteServiceAccountsIdResponse
func (c *ClientWithResponses) DeleteServiceAccountsIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteServiceAccountsIdResponse, error) {
	rsp, err := c.DeleteServiceAccountsId(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteServiceAccountsIdResponse(rsp)
}

// GetServiceAccountsIdWithResponse request returning *GetServiceAccountsIdResponse
func (c *ClientWithResponses) GetServiceAccountsIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetServiceAccountsIdResponse, error) {
	rsp, err := c.GetServiceAccountsId(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetServiceAccountsIdResponse(rsp)
}

// PutServiceAccountsIdWithBodyWithResponse request with arbitrary body returning *PutServiceAccountsIdResponse
func (c *ClientWithResponses) PutServiceAccountsIdWithBodyWithResponse(ctx context.Context, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutServiceAccountsIdResponse, error) {
	rsp, err := c.PutServiceAccountsIdWithBody(ctx, id, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutServiceAccountsIdResponse(rsp)
}

func (c *ClientWithResponses) PutServiceAccountsIdWithResponse(ctx context.Context, id openapi_types.UUID, body PutServiceAccountsIdJSONRequestBody, reqEditors ...RequestEditorFn) (*PutServiceAccountsIdResponse, error) {
	rsp, err := c.PutServiceAccountsId(ctx, id, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutServiceAccountsIdResponse(rsp)
}

// GetSubscriptionsWithResponse request returning *GetSubscriptionsResponse
func (c *ClientWithResponses) GetSubscriptionsWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetSubscriptionsResponse, error) {
	rsp, err := c.GetSubscriptions(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetSubscriptionsResponse(rsp)
}

// PostSubscriptionsWithBodyWithResponse request with arbitrary body returning *PostSubscriptionsResponse
func (c *ClientWithResponses) PostSubscriptionsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostSubscriptionsResponse, error) {
	rsp, err := c.PostSubscriptionsWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostSubscriptionsResponse(rsp)
}

func (c *ClientWithResponses) PostSubscriptionsWithResponse(ctx context.Context, body PostSubscriptionsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostSubscriptionsResponse, error) {
	rsp, err := c.PostSubscriptions(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostSubscriptionsResponse(rsp)
}

// DeleteSubscriptionsSubIdWithResponse request returning *DeleteSubscriptionsSubIdResponse
func (c *ClientWithResponses) DeleteSubscriptionsSubIdWithResponse(ctx context.Context, subId openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteSubscriptionsSubIdResponse, error) {
	rsp, err := c.DeleteSubscriptionsSubId(ctx, subId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteSubscriptionsSubIdResponse(rsp)
}

// GetUsersWithResponse request returning *GetUsersResponse
func (c *ClientWithResponses) GetUsersWithResponse(ctx context.Context, params *GetUsersParams, reqEditors ...RequestEditorFn) (*GetUsersResponse, error) {
	rsp, err := c.GetUsers(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetUsersResponse(rsp)
}

// DeleteUsersIdWithResponse request returning *DeleteUsersIdResponse
func (c *ClientWithResponses) DeleteUsersIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteUsersIdResponse, error) {
	rsp, err := c.DeleteUsersId(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteUsersIdResponse(rsp)
}

// GetUsersIdWithResponse request returning *GetUsersIdResponse
func (c *ClientWithResponses) GetUsersIdWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetUsersIdResponse, error) {
	rsp, err := c.GetUsersId(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetUsersIdResponse(rsp)
}

// ParseGetAlertTypesResponse parses an HTTP response from a GetAlertTypesWithResponse call
func ParseGetAlertTypesResponse(rsp *http.Response) (*GetAlertTypesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetAlertTypesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest AlertTypeArray
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetApiKeysResponse parses an HTTP response from a GetApiKeysWithResponse call
func ParseGetApiKeysResponse(rsp *http.Response) (*GetApiKeysResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApiKeysResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ApiKeyPagination
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostApiKeysResponse parses an HTTP response from a PostApiKeysWithResponse call
func ParsePostApiKeysResponse(rsp *http.Response) (*PostApiKeysResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiKeysResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ApiKey
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParseDeleteApiKeysIdResponse parses an HTTP response from a DeleteApiKeysIdWithResponse call
func ParseDeleteApiKeysIdResponse(rsp *http.Response) (*DeleteApiKeysIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteApiKeysIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetApiKeysIdResponse parses an HTTP response from a GetApiKeysIdWithResponse call
func ParseGetApiKeysIdResponse(rsp *http.Response) (*GetApiKeysIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApiKeysIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ApiKey
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePutApiKeysIdResponse parses an HTTP response from a PutApiKeysIdWithResponse call
func ParsePutApiKeysIdResponse(rsp *http.Response) (*PutApiKeysIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutApiKeysIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ApiKey
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetInvitationsResponse parses an HTTP response from a GetInvitationsWithResponse call
func ParseGetInvitationsResponse(rsp *http.Response) (*GetInvitationsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetInvitationsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest InvitationPagination
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostInvitationsResponse parses an HTTP response from a PostInvitationsWithResponse call
func ParsePostInvitationsResponse(rsp *http.Response) (*PostInvitationsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostInvitationsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Invitation
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 409:
		var dest AlreadyExistsResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON409 = &dest

	}

	return response, nil
}

// ParseDeleteInvitationsIdResponse parses an HTTP response from a DeleteInvitationsIdWithResponse call
func ParseDeleteInvitationsIdResponse(rsp *http.Response) (*DeleteInvitationsIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteInvitationsIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetInvitationsIdResponse parses an HTTP response from a GetInvitationsIdWithResponse call
func ParseGetInvitationsIdResponse(rsp *http.Response) (*GetInvitationsIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetInvitationsIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Invitation
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetNotificationsResponse parses an HTTP response from a GetNotificationsWithResponse call
func ParseGetNotificationsResponse(rsp *http.Response) (*GetNotificationsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetNotificationsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest NotificationPagination
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePutNotificationsIdResponse parses an HTTP response from a PutNotificationsIdWithResponse call
func ParsePutNotificationsIdResponse(rsp *http.Response) (*PutNotificationsIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutNotificationsIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseDeleteOrgsOrgIdResponse parses an HTTP response from a DeleteOrgsOrgIdWithResponse call
func ParseDeleteOrgsOrgIdResponse(rsp *http.Response) (*DeleteOrgsOrgIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteOrgsOrgIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetOrgsOrgIdResponse parses an HTTP response from a GetOrgsOrgIdWithResponse call
func ParseGetOrgsOrgIdResponse(rsp *http.Response) (*GetOrgsOrgIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetOrgsOrgIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Org
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePutOrgsOrgIdResponse parses an HTTP response from a PutOrgsOrgIdWithResponse call
func ParsePutOrgsOrgIdResponse(rsp *http.Response) (*PutOrgsOrgIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutOrgsOrgIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetRecipientsResponse parses an HTTP response from a GetRecipientsWithResponse call
func ParseGetRecipientsResponse(rsp *http.Response) (*GetRecipientsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetRecipientsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest RecipientArray
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostRecipientsResponse parses an HTTP response from a PostRecipientsWithResponse call
func ParsePostRecipientsResponse(rsp *http.Response) (*PostRecipientsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostRecipientsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParseDeleteRecipientsRecipientIdResponse parses an HTTP response from a DeleteRecipientsRecipientIdWithResponse call
func ParseDeleteRecipientsRecipientIdResponse(rsp *http.Response) (*DeleteRecipientsRecipientIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteRecipientsRecipientIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePutRecipientsRecipientIdSubscriptionsResponse parses an HTTP response from a PutRecipientsRecipientIdSubscriptionsWithResponse call
func ParsePutRecipientsRecipientIdSubscriptionsResponse(rsp *http.Response) (*PutRecipientsRecipientIdSubscriptionsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutRecipientsRecipientIdSubscriptionsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostRecipientsRecipientIdTestResponse parses an HTTP response from a PostRecipientsRecipientIdTestWithResponse call
func ParsePostRecipientsRecipientIdTestResponse(rsp *http.Response) (*PostRecipientsRecipientIdTestResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostRecipientsRecipientIdTestResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParseGetRoleBindingsResponse parses an HTTP response from a GetRoleBindingsWithResponse call
func ParseGetRoleBindingsResponse(rsp *http.Response) (*GetRoleBindingsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetRoleBindingsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest RoleBindingPagination
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePutRoleBindingsResponse parses an HTTP response from a PutRoleBindingsWithResponse call
func ParsePutRoleBindingsResponse(rsp *http.Response) (*PutRoleBindingsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutRoleBindingsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParseGetRolesResponse parses an HTTP response from a GetRolesWithResponse call
func ParseGetRolesResponse(rsp *http.Response) (*GetRolesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetRolesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest RolePagination
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetServiceAccountsResponse parses an HTTP response from a GetServiceAccountsWithResponse call
func ParseGetServiceAccountsResponse(rsp *http.Response) (*GetServiceAccountsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetServiceAccountsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ServiceAccountPagination
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostServiceAccountsResponse parses an HTTP response from a PostServiceAccountsWithResponse call
func ParsePostServiceAccountsResponse(rsp *http.Response) (*PostServiceAccountsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostServiceAccountsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ServiceAccount
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 409:
		var dest AlreadyExistsResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON409 = &dest

	}

	return response, nil
}

// ParseDeleteServiceAccountsIdResponse parses an HTTP response from a DeleteServiceAccountsIdWithResponse call
func ParseDeleteServiceAccountsIdResponse(rsp *http.Response) (*DeleteServiceAccountsIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteServiceAccountsIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetServiceAccountsIdResponse parses an HTTP response from a GetServiceAccountsIdWithResponse call
func ParseGetServiceAccountsIdResponse(rsp *http.Response) (*GetServiceAccountsIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetServiceAccountsIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ServiceAccount
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePutServiceAccountsIdResponse parses an HTTP response from a PutServiceAccountsIdWithResponse call
func ParsePutServiceAccountsIdResponse(rsp *http.Response) (*PutServiceAccountsIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutServiceAccountsIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetSubscriptionsResponse parses an HTTP response from a GetSubscriptionsWithResponse call
func ParseGetSubscriptionsResponse(rsp *http.Response) (*GetSubscriptionsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetSubscriptionsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest SubscriptionArray
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostSubscriptionsResponse parses an HTTP response from a PostSubscriptionsWithResponse call
func ParsePostSubscriptionsResponse(rsp *http.Response) (*PostSubscriptionsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostSubscriptionsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParseDeleteSubscriptionsSubIdResponse parses an HTTP response from a DeleteSubscriptionsSubIdWithResponse call
func ParseDeleteSubscriptionsSubIdResponse(rsp *http.Response) (*DeleteSubscriptionsSubIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteSubscriptionsSubIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetUsersResponse parses an HTTP response from a GetUsersWithResponse call
func ParseGetUsersResponse(rsp *http.Response) (*GetUsersResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetUsersResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest UserPagination
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseDeleteUsersIdResponse parses an HTTP response from a DeleteUsersIdWithResponse call
func ParseDeleteUsersIdResponse(rsp *http.Response) (*DeleteUsersIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteUsersIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetUsersIdResponse parses an HTTP response from a GetUsersIdWithResponse call
func ParseGetUsersIdResponse(rsp *http.Response) (*GetUsersIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetUsersIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest User
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ServerInterface represents all server handlers.
type ServerInterface interface {

	// (GET /alertTypes)
	GetAlertTypes(c *gin.Context)

	// (GET /apiKeys)
	GetApiKeys(c *gin.Context, params GetApiKeysParams)

	// (POST /apiKeys)
	PostApiKeys(c *gin.Context)

	// (DELETE /apiKeys/{id})
	DeleteApiKeysId(c *gin.Context, id uint64)

	// (GET /apiKeys/{id})
	GetApiKeysId(c *gin.Context, id uint64)

	// (PUT /apiKeys/{id})
	PutApiKeysId(c *gin.Context, id uint64)

	// (GET /invitations)
	GetInvitations(c *gin.Context, params GetInvitationsParams)

	// (POST /invitations)
	PostInvitations(c *gin.Context)

	// (DELETE /invitations/{id})
	DeleteInvitationsId(c *gin.Context, id uint64)

	// (GET /invitations/{id})
	GetInvitationsId(c *gin.Context, id uint64)

	// (GET /notifications)
	GetNotifications(c *gin.Context, params GetNotificationsParams)

	// (PUT /notifications/{id})
	PutNotificationsId(c *gin.Context, id uint64)

	// (DELETE /orgs/{orgId})
	DeleteOrgsOrgId(c *gin.Context, orgId openapi_types.UUID)

	// (GET /orgs/{orgId})
	GetOrgsOrgId(c *gin.Context, orgId openapi_types.UUID)

	// (PUT /orgs/{orgId})
	PutOrgsOrgId(c *gin.Context, orgId openapi_types.UUID)

	// (GET /recipients)
	GetRecipients(c *gin.Context)

	// (POST /recipients)
	PostRecipients(c *gin.Context)

	// (DELETE /recipients/{recipientId})
	DeleteRecipientsRecipientId(c *gin.Context, recipientId openapi_types.UUID)

	// (PUT /recipients/{recipientId}/subscriptions)
	PutRecipientsRecipientIdSubscriptions(c *gin.Context, recipientId openapi_types.UUID)

	// (POST /recipients/{recipientId}/test)
	PostRecipientsRecipientIdTest(c *gin.Context, recipientId openapi_types.UUID)

	// (GET /roleBindings)
	GetRoleBindings(c *gin.Context, params GetRoleBindingsParams)

	// (PUT /roleBindings)
	PutRoleBindings(c *gin.Context)

	// (GET /roles)
	GetRoles(c *gin.Context, params GetRolesParams)

	// (GET /serviceAccounts)
	GetServiceAccounts(c *gin.Context, params GetServiceAccountsParams)

	// (POST /serviceAccounts)
	PostServiceAccounts(c *gin.Context)

	// (DELETE /serviceAccounts/{id})
	DeleteServiceAccountsId(c *gin.Context, id openapi_types.UUID)

	// (GET /serviceAccounts/{id})
	GetServiceAccountsId(c *gin.Context, id openapi_types.UUID)

	// (PUT /serviceAccounts/{id})
	PutServiceAccountsId(c *gin.Context, id openapi_types.UUID)

	// (GET /subscriptions)
	GetSubscriptions(c *gin.Context)

	// (POST /subscriptions)
	PostSubscriptions(c *gin.Context)

	// (DELETE /subscriptions/{subId})
	DeleteSubscriptionsSubId(c *gin.Context, subId openapi_types.UUID)

	// (GET /users)
	GetUsers(c *gin.Context, params GetUsersParams)

	// (DELETE /users/{id})
	DeleteUsersId(c *gin.Context, id openapi_types.UUID)

	// (GET /users/{id})
	GetUsersId(c *gin.Context, id openapi_types.UUID)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetAlertTypes operation middleware
func (siw *ServerInterfaceWrapper) GetAlertTypes(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAlertTypes(c)
}

// GetApiKeys operation middleware
func (siw *ServerInterfaceWrapper) GetApiKeys(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetApiKeysParams

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Required query parameter "principal" -------------

	if paramValue := c.Query("principal"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument principal is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "principal", c.Request.URL.Query(), &params.Principal)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter principal: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetApiKeys(c, params)
}

// PostApiKeys operation middleware
func (siw *ServerInterfaceWrapper) PostApiKeys(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostApiKeys(c)
}

// DeleteApiKeysId operation middleware
func (siw *ServerInterfaceWrapper) DeleteApiKeysId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id uint64

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteApiKeysId(c, id)
}

// GetApiKeysId operation middleware
func (siw *ServerInterfaceWrapper) GetApiKeysId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id uint64

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetApiKeysId(c, id)
}

// PutApiKeysId operation middleware
func (siw *ServerInterfaceWrapper) PutApiKeysId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id uint64

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutApiKeysId(c, id)
}

// GetInvitations operation middleware
func (siw *ServerInterfaceWrapper) GetInvitations(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetInvitationsParams

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetInvitations(c, params)
}

// PostInvitations operation middleware
func (siw *ServerInterfaceWrapper) PostInvitations(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostInvitations(c)
}

// DeleteInvitationsId operation middleware
func (siw *ServerInterfaceWrapper) DeleteInvitationsId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id uint64

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteInvitationsId(c, id)
}

// GetInvitationsId operation middleware
func (siw *ServerInterfaceWrapper) GetInvitationsId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id uint64

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetInvitationsId(c, id)
}

// GetNotifications operation middleware
func (siw *ServerInterfaceWrapper) GetNotifications(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetNotificationsParams

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "order" -------------

	err = runtime.BindQueryParameter("form", true, false, "order", c.Request.URL.Query(), &params.Order)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter order: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetNotifications(c, params)
}

// PutNotificationsId operation middleware
func (siw *ServerInterfaceWrapper) PutNotificationsId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id uint64

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutNotificationsId(c, id)
}

// DeleteOrgsOrgId operation middleware
func (siw *ServerInterfaceWrapper) DeleteOrgsOrgId(c *gin.Context) {

	var err error

	// ------------- Path parameter "orgId" -------------
	var orgId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "orgId", c.Param("orgId"), &orgId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter orgId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteOrgsOrgId(c, orgId)
}

// GetOrgsOrgId operation middleware
func (siw *ServerInterfaceWrapper) GetOrgsOrgId(c *gin.Context) {

	var err error

	// ------------- Path parameter "orgId" -------------
	var orgId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "orgId", c.Param("orgId"), &orgId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter orgId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetOrgsOrgId(c, orgId)
}

// PutOrgsOrgId operation middleware
func (siw *ServerInterfaceWrapper) PutOrgsOrgId(c *gin.Context) {

	var err error

	// ------------- Path parameter "orgId" -------------
	var orgId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "orgId", c.Param("orgId"), &orgId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter orgId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutOrgsOrgId(c, orgId)
}

// GetRecipients operation middleware
func (siw *ServerInterfaceWrapper) GetRecipients(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetRecipients(c)
}

// PostRecipients operation middleware
func (siw *ServerInterfaceWrapper) PostRecipients(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostRecipients(c)
}

// DeleteRecipientsRecipientId operation middleware
func (siw *ServerInterfaceWrapper) DeleteRecipientsRecipientId(c *gin.Context) {

	var err error

	// ------------- Path parameter "recipientId" -------------
	var recipientId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "recipientId", c.Param("recipientId"), &recipientId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter recipientId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteRecipientsRecipientId(c, recipientId)
}

// PutRecipientsRecipientIdSubscriptions operation middleware
func (siw *ServerInterfaceWrapper) PutRecipientsRecipientIdSubscriptions(c *gin.Context) {

	var err error

	// ------------- Path parameter "recipientId" -------------
	var recipientId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "recipientId", c.Param("recipientId"), &recipientId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter recipientId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutRecipientsRecipientIdSubscriptions(c, recipientId)
}

// PostRecipientsRecipientIdTest operation middleware
func (siw *ServerInterfaceWrapper) PostRecipientsRecipientIdTest(c *gin.Context) {

	var err error

	// ------------- Path parameter "recipientId" -------------
	var recipientId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "recipientId", c.Param("recipientId"), &recipientId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter recipientId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostRecipientsRecipientIdTest(c, recipientId)
}

// GetRoleBindings operation middleware
func (siw *ServerInterfaceWrapper) GetRoleBindings(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetRoleBindingsParams

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "principal" -------------

	err = runtime.BindQueryParameter("form", true, false, "principal", c.Request.URL.Query(), &params.Principal)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter principal: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetRoleBindings(c, params)
}

// PutRoleBindings operation middleware
func (siw *ServerInterfaceWrapper) PutRoleBindings(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutRoleBindings(c)
}

// GetRoles operation middleware
func (siw *ServerInterfaceWrapper) GetRoles(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetRolesParams

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetRoles(c, params)
}

// GetServiceAccounts operation middleware
func (siw *ServerInterfaceWrapper) GetServiceAccounts(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetServiceAccountsParams

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetServiceAccounts(c, params)
}

// PostServiceAccounts operation middleware
func (siw *ServerInterfaceWrapper) PostServiceAccounts(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostServiceAccounts(c)
}

// DeleteServiceAccountsId operation middleware
func (siw *ServerInterfaceWrapper) DeleteServiceAccountsId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteServiceAccountsId(c, id)
}

// GetServiceAccountsId operation middleware
func (siw *ServerInterfaceWrapper) GetServiceAccountsId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetServiceAccountsId(c, id)
}

// PutServiceAccountsId operation middleware
func (siw *ServerInterfaceWrapper) PutServiceAccountsId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutServiceAccountsId(c, id)
}

// GetSubscriptions operation middleware
func (siw *ServerInterfaceWrapper) GetSubscriptions(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetSubscriptions(c)
}

// PostSubscriptions operation middleware
func (siw *ServerInterfaceWrapper) PostSubscriptions(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostSubscriptions(c)
}

// DeleteSubscriptionsSubId operation middleware
func (siw *ServerInterfaceWrapper) DeleteSubscriptionsSubId(c *gin.Context) {

	var err error

	// ------------- Path parameter "subId" -------------
	var subId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "subId", c.Param("subId"), &subId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter subId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteSubscriptionsSubId(c, subId)
}

// GetUsers operation middleware
func (siw *ServerInterfaceWrapper) GetUsers(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetUsersParams

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetUsers(c, params)
}

// DeleteUsersId operation middleware
func (siw *ServerInterfaceWrapper) DeleteUsersId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteUsersId(c, id)
}

// GetUsersId operation middleware
func (siw *ServerInterfaceWrapper) GetUsersId(c *gin.Context) {

	var err error

	// ------------- Path parameter "id" -------------
	var id openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "id", c.Param("id"), &id, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter id: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	c.Set(ApiKeyAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetUsersId(c, id)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/alertTypes", wrapper.GetAlertTypes)
	router.GET(options.BaseURL+"/apiKeys", wrapper.GetApiKeys)
	router.POST(options.BaseURL+"/apiKeys", wrapper.PostApiKeys)
	router.DELETE(options.BaseURL+"/apiKeys/:id", wrapper.DeleteApiKeysId)
	router.GET(options.BaseURL+"/apiKeys/:id", wrapper.GetApiKeysId)
	router.PUT(options.BaseURL+"/apiKeys/:id", wrapper.PutApiKeysId)
	router.GET(options.BaseURL+"/invitations", wrapper.GetInvitations)
	router.POST(options.BaseURL+"/invitations", wrapper.PostInvitations)
	router.DELETE(options.BaseURL+"/invitations/:id", wrapper.DeleteInvitationsId)
	router.GET(options.BaseURL+"/invitations/:id", wrapper.GetInvitationsId)
	router.GET(options.BaseURL+"/notifications", wrapper.GetNotifications)
	router.PUT(options.BaseURL+"/notifications/:id", wrapper.PutNotificationsId)
	router.DELETE(options.BaseURL+"/orgs/:orgId", wrapper.DeleteOrgsOrgId)
	router.GET(options.BaseURL+"/orgs/:orgId", wrapper.GetOrgsOrgId)
	router.PUT(options.BaseURL+"/orgs/:orgId", wrapper.PutOrgsOrgId)
	router.GET(options.BaseURL+"/recipients", wrapper.GetRecipients)
	router.POST(options.BaseURL+"/recipients", wrapper.PostRecipients)
	router.DELETE(options.BaseURL+"/recipients/:recipientId", wrapper.DeleteRecipientsRecipientId)
	router.PUT(options.BaseURL+"/recipients/:recipientId/subscriptions", wrapper.PutRecipientsRecipientIdSubscriptions)
	router.POST(options.BaseURL+"/recipients/:recipientId/test", wrapper.PostRecipientsRecipientIdTest)
	router.GET(options.BaseURL+"/roleBindings", wrapper.GetRoleBindings)
	router.PUT(options.BaseURL+"/roleBindings", wrapper.PutRoleBindings)
	router.GET(options.BaseURL+"/roles", wrapper.GetRoles)
	router.GET(options.BaseURL+"/serviceAccounts", wrapper.GetServiceAccounts)
	router.POST(options.BaseURL+"/serviceAccounts", wrapper.PostServiceAccounts)
	router.DELETE(options.BaseURL+"/serviceAccounts/:id", wrapper.DeleteServiceAccountsId)
	router.GET(options.BaseURL+"/serviceAccounts/:id", wrapper.GetServiceAccountsId)
	router.PUT(options.BaseURL+"/serviceAccounts/:id", wrapper.PutServiceAccountsId)
	router.GET(options.BaseURL+"/subscriptions", wrapper.GetSubscriptions)
	router.POST(options.BaseURL+"/subscriptions", wrapper.PostSubscriptions)
	router.DELETE(options.BaseURL+"/subscriptions/:subId", wrapper.DeleteSubscriptionsSubId)
	router.GET(options.BaseURL+"/users", wrapper.GetUsers)
	router.DELETE(options.BaseURL+"/users/:id", wrapper.DeleteUsersId)
	router.GET(options.BaseURL+"/users/:id", wrapper.GetUsersId)
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+xd32/bOPL/Vwh+F+j3ACXOdXMLbN7S7bbX225SJA3uIcgDbdEKW1lUSSppGvh/P/CH",
	"JFKiJMqxnKTtUzc2ORzOfOYHh0PvPVzQVU4znAkOj+4hwzynGcfqj+OUYRTf/fmVcMHPzDfyiwXNBM6E",
	"/E+U5ylZIEFoNvvEaSY/44trvELyv3JGc8wE0fRWPJH/iLscwyPIBSNZAtfrCDL8pSAMx/DoUg26ispB",
	"dP4JLwRcy1Ex5gtGcrkUPIKHB7+DVygGZ/hLgbmA6wi+QrH56+kxe9Bk9jVeoiJ9cpwatkAFBMCwKFhG",
	"sgRIGusIvkEkxfEHhhc0i4mct4VNLGiMPbuItq8HzT6w+ZfrnFDxhhZZ/PSgcwhOqACKOUVFL2ssFDNx",
	"jm8wI+JOfoCzYiWpLxgRZIFSGMFbpLRnLVYLV83/qD5tKQQJnFB251WKw6Ln+wyt/NrkFq+/MLyER/D/",
	"ZrULmpm9zdyNNWWnyFvEoppbl7e2gK09HzOGFCNE4BUP4kiJal0RRYqCpJmTv/CdR4gMI4HjYwWhJWUr",
	"JOARjJHAe4KoPYwWLYkdWgXJxG+HNSGSCZxgJkd+xn7l5YxkC5Kj1CVUkNjHD8cLhoWXUJHH43bXUKNa",
	"sOZGcxxZQrOXcCVT8eXVsNLGSPVqDXbq9gNKSIZKraA0PV3Co8umvnNnVN96Fr31ur2HJmGkmAjchd55",
	"U9olibbEvDIsxHXpGEqnklCapHiPokJcv4QRTIi4LubSxZAsprc8JTdS6SnVjodz6nU6fyj9vstuiEA6",
	"eqig+IrGHgvCK0RSL/oYTfG7OADDDTloitV83+b/lEPO8ILkBGfiD5otSTKGNWEE16eqirz2KQ0mFYXI",
	"LOFjsRbfVrxO917w15wwzMcQC3dRnXGCskTrdnO1b8k/lXDRHEVl7LGdVC0i12H1IKxW3zg3Zand46rq",
	"bx/ZXZGKkRE78rstm1SY6zqhgixNruZLNKuc7gHA5TiLMRvam83JuZ4h5wokCm471tc4xQJLdF1k8sgF",
	"I3gm//F5T4XcYFMURKS410eVPKj8Bkbwb8w5SiQxR45XQcZis1FttJJWyU5UKWFIfeOMw2HYYx72949s",
	"IJnFyqh9HZebKRRSTqTyQiDbzKCd9W1q423svLKF0PjY4fb9WX53DDxlyVaC33AUmjzU9ESXmrRPBh+k",
	"rbaEkJIVEaG+jC6XHIuNYKTXqUh0MOg1tAFjwspo+gadk2/YD84PlAudCfemlkMnrTHnpIZc3KNKTaiL",
	"2yoZ7GV4USWiQYmlyVubvBkqXaycY3ZDFvh4saDFAD8bVwE2Td2NdbjS7cmy1H6KeTW6dzdIBsD3+AZ3",
	"HDdKsW7CuD05slfycl08HL09eOxY0vbo/FxF7t71H5LFNLgzpDoYO2VJLyMjgknHCpXFBGPFVJ7KbDe4",
	"flRXtJrZSVMk9QJdTNMUvyJZTLJ+8Yyr9lSjy7N/h+26+x4k27tXuwJUUm9y0iGErTmrkfZSIWZrTjra",
	"JHEhYcnJ7tIYRdVIoD4yh2czlVjGJf61OjxZv6eOExOp3pXMS6hKXFcoz+V+6qzVv5C3MBRBnqLF5848",
	"RX7ZnlNwmTP7p1xwzDwIMUC7O1EeT29UqjfDATmVl/WhHMvPyEBe5tvv+srWRLOwWNZYtByNbHyHX+n0",
	"xuchgWYSFkiIlaYPugmajrxlUBv0gbj29rty8UHE5NCTILm1vTy0pndJz+x5vBBLYfXL8pGrAKzmZMym",
	"/IUyh1jYKV7SfAIiCNp796ZDd+smC54DgEq6/yi/DDk67+B+b5yzGhPsK9Hjr2iVS8d6CU9ZgjLyTenv",
	"b7yaq6qd/eFxvCLK2VWWOJDsbSmpMB63P6VoHg1tjUadWGkiY5yzaaDKs393xCPbG3eYGbm7446ziksy",
	"0BrJN08o5+bT0ZUoNdG7ji8daa27wY1dBG/x/JrSzxdn74fDnymJW1O8zFoH0DaTga5hXKViWz0ZirZb",
	"56gID211pMXZQvLY2wX3VaORdaHdu8ty3GbuvbvoHX65lCIu3tOEZGMWZpjTgi3wWOc/wo9zzEbkyNXw",
	"+g610oHrwC3W+7y01Os4pCgkdCDkkf2wlE4Q/36fq6eHeVrfIW47DlBysUk51DhDM93TBafaewrpO87l",
	"2qYdVTe3FOK6degrYyxAOggBlBPwGd/tg39jFGMGNH/gxSvEyQLMEce/Hf7/L/ef8d366Jd73Uu0/scL",
	"aLrrJDNzObTeyrUQuWoyxYhhVnIxV3+9KXf/n/9+dEiob5s05PZItqRK7Pp+Ft68hBG8wYzr7bzcP9hD",
	"aX6NVEaX4wzlBB7BX/cP9n+FEcyRuFYimaGyK039mehrHFc07wkXAKUpQDeIpGieYqBmAaGmKfpMAVeq",
	"Er7F4rgmGrkdwS8PDkb1RQY11JUIb7U+nv7lQEGZpy3+yytpVzYsLq+UBSB5nGpfcqptc3i1juDXPelv",
	"WSbPzIIVeB3BmdVj1SNIDSwOSAYoS8D8Dtgn27YsDVGpNIZWWCizv7yHRJL8UmDVs6jdanmNFlkSjHU/",
	"Ljw6iIKSIj/h8p7OQ/efDyLsnOorC5cStRcb8g5XU8Ks2b7XAbQIHh4cdhGruJu12oQfhFBP6vx1r5Tj",
	"Xo7ZinDjEgw691Oi+8dzyj0Y1b11AGUlUFuYrC9HudGZVTLfisT9169rNwgYo5tY7X3KPhhWtudBwa7V",
	"rfMkqFwWKmIi9jIaI4FKI+txZLN7Eq81RFIscBss+sKuRIp0Zco6XbzoQQYxOkPz6Kxfjs23Ds/G2Izk",
	"1pE/IiRYDEivjgCdotsR3J+DuKWQ1/5IKVOeOuyYw+ZwvOmMZ1cRzAuPSi90MalU6wsO3JJSw5kWDfVO",
	"4E2L5+BMnwO6dJ1wnCttNLJ6vcBbrPNrayygtxmOpU8Q11jmiT7H8M6i/WTTw3+FEZ4yg/N2NU9wXGh0",
	"GnfByRo2kIwpxjHI8C2QR11AMkGBQEyGDWrV1Pe9SZoLjylcS99DjB17GLuvfespm5z6+/BU/3PXXUPK",
	"Tvh6/dFgeheb9C6zPFNvkmchToWzHcXhLeWTDwDHzqPXBrgYSEVVELI1rTIXgUhaRiELBP5E9Qmp/3n4",
	"lOcAG5NS+3xJ6w1Cb3YjIdQsqLUgdNIY8LyTmg7ClOnHLB7C9bXGa8wXMLLeY5vPj9XH7rCr3VbEOp7B",
	"TF6A7UGsM7DKqgZBW4VA71FO5/sKuLrDGNClA2Hfcc5B8G7d4CRnx4G27vA0b2REHoDNw0Fin+baMKEs",
	"4bN71S0SkiOZSr78pzM7OmUJPzXtJ7tLWXp+bONxYpCUbKeWuHKPSVC2ooSdaeMwmWmH/N9iMST8rZjL",
	"KUueVI0jTNTBRTNaye8B9zR9npay5AUP1Kl+2GHpdBLv13g7Mpm3e26JZBiy+l1s1eszePWMmXDiLqin",
	"ApIB1FUgO6tXmNDqG28OHjMNquUyUFnS+aOqLGnxVjO7Beq8M5zy7s/7mPEpmt6D6s9WE1qoTvtLO/XA",
	"2b3VRxeSvviNqxsKOqGpwXDmtO0NRxK3zW/r9/7fufPdxB2U+VQfUGYuJruORXnR8Mb2NLCkDKAgd2K9",
	"m7QBdO4wsXM0TZJGDD4Q/ZlYjMW2g9UweAusQ6E/JHKcxQABOQis9G+YAEFrJA/EQwvBH+U6P5QbnKLJ",
	"LsitSaVJcTd/ucUbHxuPpfqbHeVoMDfD+7NMm+4P0Kb3NNry/I/wJki+q37yTjha+q/T7p5jrgstGTAr",
	"8faHSxdoU8Uq/+8CfG/59zi1Dpxky7cQof3TekKHL3n+TmRqu9+JwXfo2vP+rVvr3H1VwE09y6f58wbd",
	"nw09oY8Kp0XDiD6xxtDATusmSLx5ZhsdUxVfun8RZccdPc13sd9RV88DMNVfAmqMDm3gbiCwt8engcQf",
	"/TbLiG6vNN4RqgzoDQ9TTDt6THvNFW6ZT7mrt6mO7feOj7sDKxvHGzofbiD3636Ss8E2gsN3XsB6AAL7",
	"Dxmt4uyoGzNndm85o1l/nc6NtB7wP+bVmVtPHHl75lTAey/Q2tKdKI17RoXmR9DvQBplj53d82K+wTVa",
	"GCZMUmUveC6XC6ofczPy5wXazi8Zqp9f6GxPUiPqdzwd3vZC0fl52O//DYtpj/jVb2F0wUAN6O0qVSNC",
	"H1SYtzxl0yCJQcFJloBPt6LDPyiUbLuZdEqPsAN1BByfxgi6tMVHlfLWDOYRzaTrwYBamt2UMi1YCo/U",
	"U/PZzUtordNU5PFigTkHgip1vuAgZ3RJ1P8FwuhCMyaZd2ea/xMFByiLgblWrCa5MaA9uV627Eq0X9OV",
	"KLA+HCbRqgYrvszvgVhRvXGCGCasKtaKmnO1ZF00q5J2yCar1kun7ZPD9dX6fwEAAP//g1QI8+txAAA=",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
