pipeline:
  name: PR E2E Lightweight
  identifier: PR_E2E_Lightweight
  projectIdentifier: Rising_Wave_Cloud
  orgIdentifier: default
  tags: {}
  properties:
    ci:
      codebase:
        connectorRef: RisingWave_Github
        repoName: risingwave-cloud
        build: <+input>
  stages:
    - parallel:
        - stage:
            name: e2e-test-local-lightweight
            identifier: e2e_test_local_lightweight
            type: CI
            spec:
              cloneCodebase: true
              platform:
                os: Linux
                arch: Amd64
              runtime:
                type: Cloud
                spec: {}
              caching:
                enabled: false
                paths: []
              execution:
                steps:
                  - step:
                      name: docker-login
                      identifier: dockerlogin
                      template:
                        templateRef: dockerlogin
                        versionLabel: V1
                  - step:
                      type: Run
                      name: Checkout
                      identifier: Checkout
                      spec:
                        shell: Bash
                        command: |-
                          git submodule init
                          git config submodule.CloudAgent.url https://<+secrets.getValue("github_ci_access_token")>@github.com/risingwavelabs/CloudAgent.git
                          git submodule update --recursive
                  - step:
                      type: Action
                      name: Set up Golang
                      identifier: Set_up_Golang
                      spec:
                        uses: actions/setup-go@v5
                        with:
                          go-version: "1.24"
                  - step:
                      type: Action
                      name: Create k8s Kind Cluster
                      identifier: Create_k8s_Kind_Cluster
                      spec:
                        uses: helm/kind-action@v1
                        with:
                          install_only: true
                  - step:
                      type: Run
                      name: End to end test
                      identifier: End_to_end_test
                      spec:
                        shell: Bash
                        command: |-
                          make install-root
                          onebox setup --lightweight db -v
                          COLOR=ALWAYS onebox test e2e -v -- --skip_tenant_test
