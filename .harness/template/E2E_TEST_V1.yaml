template:
  name: E2E TEST
  type: Stage
  projectIdentifier: Rising_Wave_Cloud
  orgIdentifier: default
  spec:
    type: CI
    spec:
      cloneCodebase: true
      platform:
        os: Linux
        arch: Amd64
      runtime:
        type: Cloud
        spec: {}
      execution:
        steps:
          - step:
              type: Run
              name: checkout
              identifier: checkout
              spec:
                shell: Bash
                command: |-
                  git submodule init
                  git config submodule.CloudAgent.url https://<+secrets.getValue("github_ci_access_token")>@github.com/risingwavelabs/CloudAgent.git
                  git submodule update --recursive
          - step:
              type: Action
              name: Set up Golang
              identifier: set_up_golang
              spec:
                uses: actions/setup-go@v5
                with:
                  go-version: "1.24"
          - step:
              type: Run
              name: tenant-test
              identifier: tenant_test
              spec:
                shell: Bash
                command: "# Set up GCP credentials\ncat <<EOT >> /tmp/application_default_credentials.json\n<+secrets.getValue(\"gcp_rwc-cicd_harness\")>\nEOT\nexport GOOGLE_APPLICATION_CREDENTIALS=\"/tmp/application_default_credentials.json\"\n\n# Run test\ncd test/e2e\n\nmax_attempts=2\n# Set a counter for the number of attempts\nattempt_num=1\n# Set a flag to indicate whether the command was successful\nsuccess=false\n\n# Loop until the command is successful or the maximum number of attempts is reached\nwhile [ $success = false ] && [ $attempt_num -le $max_attempts ]; do\n\t# Execute the command and check the exit code of the command\n\tif go test -v \\\n  -stack=<+stage.variables.stack> \\\n  -region=<+stage.variables.region> \\\n  -timeout 120m \\\n  --least_supported_kernel_version \"<+stage.variables.least_supported_kernel_version>\" \\\n  --kernel_version \"<+stage.variables.kernel_version>\" \\\n  --use_cloud_pg \\\n  --use_distributed_rate_limiter \\\n  --distributed_rate_limiter_bucket \"rw-e2e-distributed-lock\" \\\n  --distributed_rate_limiter_key \"<+stage.variables.distributed_rate_limiter_key>\"; then\n\t\t# The command was successful\n\t\tsuccess=true\n\t\techo \"Attempt $attempt_num succeeded.\"\n\telse\n\t\t# The command was not successful\n\t\techo \"Attempt $attempt_num failed. Trying again...\"\n\t\t# Increment the attempt counter\n\t\tattempt_num=$((attempt_num + 1))\n\tfi\ndone\n\nif $success; then\n\texit 0\nelse\n\texit 1\nfi"
      caching:
        enabled: false
        paths: []
    variables:
      - name: stack
        type: String
        value: <+input>
      - name: region
        type: String
        value: <+input>
      - name: least_supported_kernel_version
        type: String
        value: <+input>
      - name: kernel_version
        type: String
        value: <+input>
      - name: distributed_rate_limiter_key
        type: String
        description: ""
        required: false
        value: <+input>
    when:
      pipelineStatus: All
  versionLabel: V1
  identifier: E2E_TEST
