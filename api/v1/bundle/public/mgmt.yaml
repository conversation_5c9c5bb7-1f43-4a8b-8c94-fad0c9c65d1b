# Code generated by redocly cli DO NOT EDIT.
openapi: 3.0.3
info:
  title: v1
  version: 1.0-alpha
servers:
  - url: /api/v1
tags:
  - name: tenants
    description: Operations about tenants
  - name: tenant
    description: Operations about tenants
  - name: dbusers
    description: RisingWave DB users
  - name: log
    description: Operations about logs
paths:
  /tiers:
    get:
      responses:
        '200':
          description: Get tiers Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Tiers'
        '500':
          description: Internal server error
  /rootca:
    get:
      tags:
        - rootca
      description: Get the root CA file of the RisingWave clusters in this region
      responses:
        '200':
          description: Get root CA file successfully
        '500':
          description: Internal server error
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants:
    post:
      tags:
        - tenants
      summary: Create tenants with tenantName
      x-required-permission: tenants.create
      x-tenant-operation: Provision
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TenantRequestRequestBody'
      responses:
        '202':
          description: Create tenant response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateTenantResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '422':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    get:
      tags:
        - tenants
      summary: Get all the tenants owned by the user
      x-required-permission: tenants.list
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantSizePage'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant:
    parameters:
      - in: query
        name: tenantId
        schema:
          type: integer
          format: uint64
      - in: query
        name: tenantName
        schema:
          type: string
    get:
      tags:
        - tenant
      summary: get a tenant by tenantId or tenantName
      x-required-permission: tenants.get
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Tenant'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - tenant
      summary: delete a tenant by tenantId or tenantName
      x-required-permission: tenants.delete
      x-tenant-operation: Delete
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '403':
          $ref: '#/components/responses/DefaultResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/resource:
    post:
      tags:
        - tenant
      summary: update a tenant resource by tenantId
      x-required-permission: tenants.updateResource
      x-tenant-operation: Scale
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantResourcesRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '422':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/tags:
    get:
      tags:
        - tenant
      responses:
        '200':
          description: get the latest risingwave cloud image tag
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetImageTagResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/privatelinks:
    parameters:
      - in: path
        required: true
        name: tenantId
        schema:
          type: integer
          format: uint64
    post:
      tags:
        - tenant
      x-required-permission: privateLinks.create
      summary: for a tenant to link to the source/sink in the user's VPC
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostPrivateLinkRequestBody'
      responses:
        '202':
          description: Create privatelink response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostPrivateLinkResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/privatelink/{privateLinkId}:
    parameters:
      - in: path
        required: true
        name: tenantId
        schema:
          type: integer
          format: uint64
      - in: path
        required: true
        name: privateLinkId
        schema:
          type: string
          format: uuid
    get:
      tags:
        - tenant
      x-required-permission: privateLinks.get
      summary: get a private link by id
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrivateLink'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - tenant
      x-required-permission: privateLinks.delete
      summary: delete a private link by id
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/config/etcd:
    put:
      tags:
        - tenant
      x-required-permission: tenants.update
      summary: Apply new etcd configuration.
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
      requestBody:
        description: 'etcd env config content. e.g. ETCD_MAX_REQUEST_BYTES: "1024"\nETCD_QUOTA_BACKEND_BYTES: "1024"'
        required: true
        content:
          text/plain:
            schema:
              type: string
      responses:
        '202':
          description: successfully send asynchronous request to apply new etcd configuration.
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '409':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/config/risingwave:
    put:
      tags:
        - tenant
      x-required-permission: tenants.update
      summary: Apply new risingwave configuration.
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
      requestBody:
        description: risingwave.toml config content.
        required: true
        content:
          text/plain:
            schema:
              type: string
      responses:
        '202':
          description: successfully send asynchronous request to apply new risingwave configuration.
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '409':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/{tenantId}/updateVersion:
    post:
      tags:
        - tenant
      x-required-permission: tenants.update
      summary: update the tenant rw version to latest
      parameters:
        - in: path
          required: true
          name: tenantId
          schema:
            type: integer
            format: uint64
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                version:
                  type: string
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
        '409':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenant/dbusers:
    get:
      tags:
        - dbusers
      x-required-permission: databaseUsers.list
      parameters:
        - in: query
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
      responses:
        '200':
          description: Database users Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DBUsers'
        '404':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    post:
      tags:
        - dbusers
      x-required-permission: databaseUsers.create
      summary: Create a database user with options SUPERUSER/NOSUPERUSER, CREATEDB/NOCREATEDB, CREATEUSER/NOCREATEUSER PASSWORD
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateDBUserRequestBody'
      responses:
        '200':
          description: create db users success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DBUser'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      x-audit-nodata: true
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    put:
      tags:
        - dbusers
      x-required-permission: databaseUsers.update
      summary: Alter db user's password
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateDBUserRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      x-audit-nodata: true
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - dbusers
      x-required-permission: databaseUsers.delete
      summary: delete database user by name
      parameters:
        - in: query
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
        - in: query
          name: username
          schema:
            type: string
          required: true
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /endpoints:
    get:
      tags:
        - tenants
      x-required-permission: endpoints.list
      summary: Get an endpoint of tenant by tenantName or tenantId
      parameters:
        - in: query
          name: tenantName
          schema:
            type: string
        - in: query
          name: tenantId
          schema:
            type: integer
            format: uint64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Endpoint'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /log/queryError:
    get:
      summary: Query error logs over a range of time
      description: Wrapper of Loki HTTP API - https://grafana.com/docs/loki/latest/reference/api/#query-loki-over-a-range-of-time
      tags:
        - log
      x-required-permission: log.get
      operationId: queryErrLog
      parameters:
        - in: query
          name: tenantId
          required: true
          schema:
            type: integer
            format: uint64
        - in: query
          name: target
          required: true
          schema:
            type: string
            enum:
              - source
              - sink
              - table
              - target
              - name
              - message
            description: use line_contains to filter logs, e.g. `"source_id":1`, `"target":"high_join_amplification"`, `"error":"failed to parse json payload"` or `"name":"executor"`
        - in: query
          name: targetId
          required: true
          schema:
            type: string
            description: targetId to filter logs, could be sourceId, sinkId, target message, actor/executor name, error message
        - in: query
          name: start
          schema:
            type: string
            format: date-time
            description: RFC3339/RFC3339Nano formatted date-time string (start means >=), defaults to one hour ago
            nullable: true
            default: null
        - in: query
          name: end
          schema:
            type: string
            format: date-time
            description: RFC3339/RFC3339Nano formatted date-time string (end means <), defaults to now
            nullable: true
            default: null
        - in: query
          name: direction
          schema:
            type: string
            enum:
              - forward
              - backward
            default: backward
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            minimum: 1
            default: 100
      responses:
        '200':
          description: ErrLogQueryResult
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrLogQueryResult'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '500':
          description: Internal server error
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /byoc-cluster/{name}:
    parameters:
      - in: path
        required: true
        name: name
        schema:
          type: string
    get:
      tags:
        - clusters
      summary: Get a BYOC cluster
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagedCluster'
        '404':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
components:
  responses:
    DefaultResponse:
      description: Default responses returning msg
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    BadRequestResponse:
      description: 400 Bad Request
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    NotFoundResponse:
      description: 404 Not Found
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    FailedPreconditionResponse:
      description: 400 Failed Precondition
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    ApiKeyAuth:
      type: http
      scheme: basic
      description: Service account api key. Header format 'Basic base64(${key}:${secret})'
  schemas:
    TierId:
      type: string
      enum:
        - Free
        - Invited
        - Developer-Free
        - Developer-Basic
        - Developer-Test
        - Standard
        - BYOC
        - Test
        - Benchmark
    AvailableComponentType:
      type: object
      required:
        - id
        - cpu
        - memory
        - maximum
      properties:
        id:
          type: string
        cpu:
          type: string
        memory:
          type: string
        maximum:
          type: integer
    AvailableMetaStoreEtcd:
      type: object
      required:
        - nodes
        - maximumSizeGiB
      properties:
        nodes:
          type: array
          items:
            $ref: '#/components/schemas/AvailableComponentType'
        maximumSizeGiB:
          type: integer
    AvailableMetaStorePostgreSql:
      type: object
      required:
        - nodes
        - maximumSizeGiB
      properties:
        nodes:
          type: array
          items:
            $ref: '#/components/schemas/AvailableComponentType'
        maximumSizeGiB:
          type: integer
    AvailableMetaStoreAwsRds:
      type: object
      properties:
        enabled:
          type: boolean
    AvailableMetaStoreGcpCloudSql:
      type: object
      properties:
        enabled:
          type: boolean
    AvailableMetaStoreAzrPostgres:
      type: object
      properties:
        enabled:
          type: boolean
    AvailableMetaStoreSharingPg:
      type: object
      properties:
        enabled:
          type: boolean
    AvailableMetaStore:
      type: object
      properties:
        etcd:
          $ref: '#/components/schemas/AvailableMetaStoreEtcd'
        postgresql:
          $ref: '#/components/schemas/AvailableMetaStorePostgreSql'
        aws_rds:
          $ref: '#/components/schemas/AvailableMetaStoreAwsRds'
        gcp_cloudsql:
          $ref: '#/components/schemas/AvailableMetaStoreGcpCloudSql'
        azr_postgres:
          $ref: '#/components/schemas/AvailableMetaStoreAzrPostgres'
        sharing_pg:
          $ref: '#/components/schemas/AvailableMetaStoreSharingPg'
    Tier:
      type: object
      required:
        - name
        - availableStandaloneNodes
        - availableComputeNodes
        - availableCompactorNodes
        - availableFrontendNodes
        - availableMetaNodes
        - maximumComputeNodeFileCacheSizeGiB
        - validityPeriod
        - retentionPeriod
      properties:
        id:
          $ref: '#/components/schemas/TierId'
        availableStandaloneNodes:
          type: array
          items:
            $ref: '#/components/schemas/AvailableComponentType'
        availableComputeNodes:
          type: array
          items:
            $ref: '#/components/schemas/AvailableComponentType'
        availableCompactorNodes:
          type: array
          items:
            $ref: '#/components/schemas/AvailableComponentType'
        availableFrontendNodes:
          type: array
          items:
            $ref: '#/components/schemas/AvailableComponentType'
        availableMetaNodes:
          type: array
          items:
            $ref: '#/components/schemas/AvailableComponentType'
        maximumComputeNodeFileCacheSizeGiB:
          type: integer
        validityPeriod:
          type: integer
        retentionPeriod:
          type: integer
        availableMetaStore:
          $ref: '#/components/schemas/AvailableMetaStore'
    TierArray:
      type: array
      items:
        $ref: '#/components/schemas/Tier'
    Tiers:
      type: object
      required:
        - tiers
      properties:
        tiers:
          $ref: '#/components/schemas/TierArray'
    Page:
      type: object
      required:
        - limit
        - offset
      properties:
        limit:
          type: integer
          format: uint64
        offset:
          type: integer
          format: uint64
    Size:
      type: object
      required:
        - size
      properties:
        size:
          type: integer
          format: uint64
    ComponentResource:
      type: object
      required:
        - componentTypeId
        - replica
        - cpu
        - memory
      properties:
        componentTypeId:
          type: string
        cpu:
          type: string
        memory:
          type: string
        replica:
          type: integer
    TenantResourceComponents:
      type: object
      properties:
        standalone:
          $ref: '#/components/schemas/ComponentResource'
        compute:
          $ref: '#/components/schemas/ComponentResource'
        compactor:
          $ref: '#/components/schemas/ComponentResource'
        frontend:
          $ref: '#/components/schemas/ComponentResource'
        meta:
          $ref: '#/components/schemas/ComponentResource'
        etcd:
          $ref: '#/components/schemas/ComponentResource'
    TenantResourceComputeCache:
      type: object
      required:
        - sizeGb
      properties:
        sizeGb:
          type: integer
    MetaStoreType:
      type: string
      enum:
        - etcd
        - postgresql
        - aws_rds
        - gcp_cloudsql
        - azr_postgres
        - sharing_pg
    MetaStoreEtcd:
      type: object
      required:
        - resource
        - sizeGb
      properties:
        resource:
          $ref: '#/components/schemas/ComponentResource'
        sizeGb:
          type: integer
    MetaStorePostgreSql:
      type: object
      required:
        - resource
        - sizeGb
      properties:
        resource:
          $ref: '#/components/schemas/ComponentResource'
        sizeGb:
          type: integer
    MetaStoreAwsRds:
      type: object
      required:
        - instanceClass
        - sizeGb
      properties:
        instanceClass:
          type: string
        sizeGb:
          type: integer
    MetaStoreGcpCloudSql:
      type: object
      required:
        - tier
        - sizeGb
      properties:
        tier:
          type: string
        sizeGb:
          type: integer
    MetaStoreAzrPostgres:
      type: object
      required:
        - sku
        - sizeGb
      properties:
        sku:
          type: string
        sizeGb:
          type: integer
    MetaStoreSharingPg:
      type: object
      required:
        - instanceId
      properties:
        instanceId:
          type: string
    TenantResourceMetaStore:
      type: object
      required:
        - type
        - rwu
      properties:
        type:
          $ref: '#/components/schemas/MetaStoreType'
        rwu:
          type: string
        etcd:
          $ref: '#/components/schemas/MetaStoreEtcd'
        postgresql:
          $ref: '#/components/schemas/MetaStorePostgreSql'
        aws_rds:
          $ref: '#/components/schemas/MetaStoreAwsRds'
        gcp_cloudsql:
          $ref: '#/components/schemas/MetaStoreGcpCloudSql'
        azr_postgres:
          $ref: '#/components/schemas/MetaStoreAzrPostgres'
        sharing_pg:
          $ref: '#/components/schemas/MetaStoreSharingPg'
    TenantResource:
      type: object
      required:
        - components
        - computeCache
      properties:
        components:
          $ref: '#/components/schemas/TenantResourceComponents'
        etcdVolumeSizeGiB:
          type: integer
        computeCache:
          $ref: '#/components/schemas/TenantResourceComputeCache'
        metaStore:
          $ref: '#/components/schemas/TenantResourceMetaStore'
    Tenant:
      type: object
      required:
        - id
        - userId
        - tenantName
        - region
        - status
        - tier
        - resources
        - createdAt
        - imageTag
        - latestImageTag
        - rw_config
        - updatedAt
        - health_status
        - nsId
        - orgId
        - etcd_config
        - usageType
      properties:
        id:
          type: integer
          format: uint64
        userId:
          type: integer
          format: uint64
        orgId:
          type: string
          format: uuid
        tenantName:
          type: string
        region:
          type: string
        resources:
          $ref: '#/components/schemas/TenantResource'
        status:
          type: string
          enum:
            - Creating
            - Running
            - Deleting
            - Failed
            - Stopped
            - Stopping
            - Starting
            - Expired
            - ConfigUpdating
            - Upgrading
            - Updating
            - Snapshotting
            - ExtensionCompactionEnabling
            - ExtensionCompactionDisabling
            - ExtensionServerlessBackfillUpdate
            - ExtensionServerlessBackfillEnabling
            - ExtensionServerlessBackfillDisabling
            - MetaMigrating
            - Restoring
        tier:
          $ref: '#/components/schemas/TierId'
        usageType:
          type: string
          enum:
            - general
            - pipeline
        imageTag:
          type: string
          example: v0.1.12
        latestImageTag:
          type: string
          example: v0.1.12
        rw_config:
          type: string
        etcd_config:
          type: string
        health_status:
          type: string
          enum:
            - Unknown
            - Healthy
            - Unhealthy
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        nsId:
          type: string
          format: uuid
        clusterName:
          type: string
        upcomingSnapshotTime:
          type: string
          format: date-time
    TenantArray:
      type: array
      items:
        $ref: '#/components/schemas/Tenant'
    TenantSizePage:
      type: object
      allOf:
        - $ref: '#/components/schemas/Page'
        - $ref: '#/components/schemas/Size'
        - type: object
          required:
            - tenants
          properties:
            tenants:
              $ref: '#/components/schemas/TenantArray'
    ComponentResourceRequest:
      type: object
      required:
        - componentTypeId
        - replica
      properties:
        componentTypeId:
          type: string
        replica:
          type: integer
    TenantResourceRequestComponents:
      type: object
      properties:
        standalone:
          $ref: '#/components/schemas/ComponentResourceRequest'
        compute:
          $ref: '#/components/schemas/ComponentResourceRequest'
        compactor:
          $ref: '#/components/schemas/ComponentResourceRequest'
        frontend:
          $ref: '#/components/schemas/ComponentResourceRequest'
        meta:
          $ref: '#/components/schemas/ComponentResourceRequest'
        etcd:
          $ref: '#/components/schemas/ComponentResourceRequest'
    TenantResourceRequestMetaStoreEtcd:
      type: object
      allOf:
        - $ref: '#/components/schemas/ComponentResourceRequest'
        - type: object
          required:
            - sizeGb
          properties:
            sizeGb:
              type: integer
    TenantResourceRequestMetaStorePostgreSql:
      type: object
      allOf:
        - $ref: '#/components/schemas/ComponentResourceRequest'
        - type: object
          required:
            - sizeGb
          properties:
            sizeGb:
              type: integer
    TenantResourceRequestMetaStoreAwsRds:
      type: object
      required:
        - instanceClass
        - sizeGb
      properties:
        instanceClass:
          type: string
        sizeGb:
          type: integer
    TenantResourceRequestMetaStore:
      type: object
      required:
        - type
      properties:
        type:
          $ref: '#/components/schemas/MetaStoreType'
        etcd:
          $ref: '#/components/schemas/TenantResourceRequestMetaStoreEtcd'
        postgresql:
          $ref: '#/components/schemas/TenantResourceRequestMetaStorePostgreSql'
        aws_rds:
          $ref: '#/components/schemas/TenantResourceRequestMetaStoreAwsRds'
    TenantResourceRequest:
      type: object
      required:
        - components
        - computeFileCacheSizeGiB
      properties:
        components:
          $ref: '#/components/schemas/TenantResourceRequestComponents'
        etcdVolumeSizeGiB:
          type: integer
        computeFileCacheSizeGiB:
          type: integer
        metaStore:
          $ref: '#/components/schemas/TenantResourceRequestMetaStore'
    TenantRequestRequestBody:
      type: object
      required:
        - tenantName
      properties:
        tenantName:
          type: string
        sku:
          type: string
        imageTag:
          type: string
          example: latest
        clusterName:
          type: string
        tier:
          $ref: '#/components/schemas/TierId'
        resources:
          $ref: '#/components/schemas/TenantResourceRequest'
        etcdConfig:
          type: string
        configId:
          type: string
          format: uuid
        rwConfig:
          description: if config ID is not provided, use this config. currently used in tf plugin
          type: string
        usageType:
          type: string
          enum:
            - general
            - pipeline
          default: general
    CreateTenantResponseBody:
      type: object
      required:
        - tenantId
        - tenantName
      properties:
        tenantId:
          type: integer
          format: uint64
        tenantName:
          type: string
    PostTenantResourcesRequestBody:
      type: object
      properties:
        meta:
          $ref: '#/components/schemas/ComponentResourceRequest'
        frontend:
          $ref: '#/components/schemas/ComponentResourceRequest'
        compute:
          $ref: '#/components/schemas/ComponentResourceRequest'
        compactor:
          $ref: '#/components/schemas/ComponentResourceRequest'
        standalone:
          $ref: '#/components/schemas/ComponentResourceRequest'
    GetImageTagResponse:
      type: object
      required:
        - imageTag
      properties:
        imageTag:
          type: string
    PostPrivateLinkRequestBody:
      type: object
      required:
        - target
        - connectionName
      properties:
        target:
          type: string
        connectionName:
          type: string
    PostPrivateLinkResponseBody:
      type: object
      required:
        - id
        - connectionName
      properties:
        id:
          type: string
          format: uuid
        connectionName:
          type: string
    PrivateLink:
      type: object
      required:
        - id
        - tenantId
        - status
        - connectionState
        - connectionName
      properties:
        connectionName:
          type: string
        id:
          type: string
          format: uuid
        tenantId:
          type: integer
          format: int64
        status:
          type: string
          enum:
            - CREATING
            - CREATED
            - DELETING
            - ERROR
            - UNKNOWN
        connectionState:
          type: string
          enum:
            - STATUS_UNSPECIFIED
            - PENDING
            - ACCEPTED
            - REJECTED
            - CLOSED
        target:
          type: string
        endpoint:
          type: string
    DBUser:
      type: object
      required:
        - usesysid
        - username
        - usecreatedb
        - usesuper
        - usecreateuser
        - canlogin
      properties:
        usesysid:
          type: integer
          format: uint64
        username:
          type: string
        usecreatedb:
          type: boolean
        usesuper:
          type: boolean
        usecreateuser:
          type: boolean
        canlogin:
          type: boolean
    DBUserArray:
      type: array
      items:
        $ref: '#/components/schemas/DBUser'
    DBUsers:
      type: object
      properties:
        dbusers:
          $ref: '#/components/schemas/DBUserArray'
    UpdateDBUserRequestBody:
      type: object
      required:
        - tenantId
        - username
        - password
      properties:
        tenantId:
          type: integer
          format: uint64
        username:
          type: string
        password:
          type: string
    CreateDBUserRequestBody:
      type: object
      required:
        - tenantId
        - username
        - password
        - superuser
        - createdb
      properties:
        tenantId:
          type: integer
          format: uint64
        username:
          type: string
        password:
          type: string
        superuser:
          type: boolean
        createdb:
          type: boolean
        createuser:
          type: boolean
    Endpoint:
      type: object
      required:
        - id
        - tenantId
        - host
        - port
        - database
        - options
        - internalHost
        - internalPort
      properties:
        id:
          type: integer
          format: int64
        tenantId:
          type: integer
          format: int64
        host:
          type: string
        port:
          type: integer
        database:
          type: string
        options:
          type: string
        internalHost:
          type: string
        internalPort:
          type: integer
    ErrLogQueryResult:
      type: object
      required:
        - status
        - values
      properties:
        status:
          type: string
        values:
          type: array
          items:
            type: array
            items:
              type: string
            minItems: 2
            maxItems: 2
    ClusterStatus:
      type: string
      enum:
        - Uninitialized
        - Provisioned
        - Ready
        - Terminating
        - Deleted
        - PendingResourceDeletion
        - Updating
        - Failed
    ManagedCluster:
      required:
        - id
        - org
        - name
        - status
        - master_url
        - cluster_service_account
        - token
        - serving_type
        - settings
      properties:
        id:
          type: integer
          format: uint64
        org:
          type: string
          format: uuid
        name:
          type: string
        status:
          $ref: '#/components/schemas/ClusterStatus'
        master_url:
          type: string
        cluster_service_account:
          type: string
        token:
          type: string
        serving_type:
          type: string
        settings:
          type: object
          additionalProperties:
            type: string
