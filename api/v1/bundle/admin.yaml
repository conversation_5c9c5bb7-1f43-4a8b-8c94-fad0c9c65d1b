# Code generated by redocly/cli DO NOT EDIT.
openapi: 3.0.3
info:
  title: Admin Service Api
  description: Admin Service Api
  version: 1.0-alpha
servers:
  - url: /api/v1
tags:
  - name: default
  - name: cloudAdmins
  - name: tenants
  - name: tenants.diagnosis
  - name: tenants.extensions
  - name: tenants.snapshots
  - name: tenants.resourceGroups
  - name: workflows
  - name: k8sClusters
  - name: byocClusters
  - name: users
  - name: orgs
  - name: billing
  - name: invitationCodes
paths:
  /:
    get:
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
  /version:
    get:
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
  /org:
    post:
      tags:
        - orgs
      description: Creates an organisation with the given Stripe ID.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StripeOrg'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
  /org/{orgId}/trial-before:
    parameters:
      - in: path
        name: orgId
        schema:
          type: string
          format: uuid
        required: true
    get:
      tags:
        - orgs
      description: Returns the timestamp when of the given org's trial period ends.
      responses:
        '200':
          description: trial before
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TrialBefore'
      security:
        - UserKeyAuth:
            - Role:Viewer
    put:
      tags:
        - orgs
      description: Sets the timestamp when of the given org's trial period ends. Fails if the given date is in the past. Returns the resulting timestamp.
      parameters:
        - in: query
          name: trialBefore
          schema:
            type: string
            format: date-time
          required: true
      responses:
        '200':
          description: trial before
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TrialBefore'
      security:
        - UserKeyAuth:
            - Role:Editor
    post:
      tags:
        - orgs
      description: Adds the given days to the given org's trial period. Fails if the resulting timestamp is in the past. Returns the resulting timestamp.
      parameters:
        - in: query
          name: daysAdded
          schema:
            type: integer
          required: true
      responses:
        '200':
          description: trial before
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TrialBefore'
      security:
        - UserKeyAuth:
            - Role:Editor
  /org/{orgId}/auto-invoice:
    parameters:
      - in: path
        name: orgId
        schema:
          type: string
          format: uuid
        required: true
    put:
      tags:
        - orgs
      description: Sets if the invoices for this customer are finalised automatically.
      parameters:
        - in: query
          name: autoInvoice
          schema:
            type: boolean
          required: true
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
  /regions:
    get:
      summary: Get all regions
      tags:
        - regions
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Regions'
      security:
        - UserKeyAuth:
            - Role:Viewer
  /region/{region}/tenant/{tenantId}:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    get:
      summary: Get a tenant
      tags:
        - tenants
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Tenant'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Viewer
    delete:
      summary: Delete a tenant
      tags:
        - tenants
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
  /region/{region}/tenants:
    get:
      summary: Get all the tenants in the a region
      tags:
        - tenants
      parameters:
        - in: path
          name: region
          schema:
            type: string
          required: true
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantPage'
      security:
        - UserKeyAuth:
            - Role:Viewer
  /region/{region}/tenant/{tenantId}/expire:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    get:
      summary: Get the expire information
      tags:
        - tenants
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantExpireInfo'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Viewer
    post:
      summary: Update expiration lifecycle
      description: Update the expire time and delete time
      tags:
        - tenants
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantExpireRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
    delete:
      summary: Cancel the expire workflow
      description: The tenant will never expire
      tags:
        - tenants
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/stop:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants
      summary: Stop the tenant
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/start:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants
      summary: Start the tenant
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/restart:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants
      summary: Restart the tenant
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/updateVersion:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants
      summary: Update the tenant version
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantUpdateVersionRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/config/risingwave:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
      - in: query
        name: component
        schema:
          type: string
      - in: query
        name: nodeGroup
        schema:
          type: string
    put:
      tags:
        - tenants
      summary: Update the tenant config
      requestBody:
        required: true
        content:
          text/plain:
            schema:
              type: string
            example: |
              [storage]
              meta_cache_capacity_mb = 1
              [system]
              sstable_size_mb = 1
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/configNoRestart/risingwave:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
      - in: query
        name: component
        schema:
          type: string
      - in: query
        name: nodeGroup
        schema:
          type: string
    put:
      tags:
        - tenants
      summary: Update the tenant config
      requestBody:
        required: true
        content:
          text/plain:
            schema:
              type: string
            example: |
              [storage]
              meta_cache_capacity_mb = 1
              [system]
              sstable_size_mb = 1
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/config/etcd:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    put:
      tags:
        - tenants
      summary: Update the tenant config
      requestBody:
        required: true
        content:
          text/plain:
            schema:
              type: string
            example: |
              ETCD_QUOTA_BACKEND_BYTES: '1000000000'
              ETCD_MAX_REQUEST_BYTES: '100000000'
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/resource:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants
      summary: Update a tenant resource
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantResourcesRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/status:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants
      summary: Reset tenant status
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantStatusRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/tier:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants
      summary: Update tenant tier
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantTierRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/endpoint:
    get:
      summary: Get the root endpoint of the specified tenant from the given region
      tags:
        - tenants
      parameters:
        - in: path
          name: region
          schema:
            type: string
          required: true
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/DatabaseConnectionUrl'
        '401':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - UserKeyAuth:
            - Role:Admin
  /region/{region}/tenant/{tenantId}/migrateMetaStore:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants
      summary: Migrate the risingwave meta store
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/upgradeMetaStore:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
      - in: query
        name: metaStore
        schema:
          type: string
          default: dedicated_pg
        required: true
    post:
      tags:
        - tenants
      summary: Upgrade the risingwave meta store
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/computeCache:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants
      summary: update compute cache for tenant
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantComputeCacheRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/extensions/compaction/enable:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants.extensions
      summary: Enable the risingwave extensions for serverless compaction
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/extensions/compaction/disable:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants.extensions
      summary: Disable the risingwave extensions for serverless compaction
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/extensions/compaction/update:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants.extensions
      summary: Update the risingwave extensions for serverless compaction
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantExtensionCompactionRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/extensions/compaction/parameters:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    get:
      tags:
        - tenants.extensions
      summary: Get the parameters of the risingwave extensions for serverless compaction
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTenantExtensionCompactionParametersResponseBody'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Viewer
        - UserKeyAuth:
            - Role:TenantViewer
  /region/{region}/tenant/{tenantId}/extensions/compaction/status:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    get:
      tags:
        - tenants.extensions
      summary: Get the status of the risingwave extensions for serverless compaction
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTenantExtensionCompactionStatusResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Viewer
        - UserKeyAuth:
            - Role:TenantViewer
    post:
      tags:
        - tenants.extensions
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantExtensionCompactionStatusRequestBody'
      summary: Get the status of the risingwave extensions for serverless compaction
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTenantExtensionCompactionStatusResponseBody'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/extensions/iceberg-compaction:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    get:
      tags:
        - tenants.extensions
      summary: Get iceberg compaction status
      responses:
        '200':
          description: Iceberg compaction status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IcebergCompaction'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
    post:
      tags:
        - tenants.extensions
      summary: Enable iceberg compaction
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                config:
                  type: string
                resources:
                  $ref: '#/components/schemas/ComponentResourceRequest'
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
    put:
      tags:
        - tenants.extensions
      summary: Update iceberg compaction
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                config:
                  type: string
                resources:
                  $ref: '#/components/schemas/ComponentResourceRequest'
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
    delete:
      tags:
        - tenants.extensions
      summary: Disable iceberg compaction
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/extensions/serverlessbackfill/enable:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants.extensions
      summary: Enable the risingwave extensions for serverless backfilling
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/extensions/serverlessbackfill/disable:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants.extensions
      summary: Disable the risingwave extensions for serverless backfilling
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/extensions/serverlessbackfill/version:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      tags:
        - tenants.extensions
      summary: Update the risingwave extensions for serverless backfilling to version. Version format like 1.0.0.
      requestBody:
        required: true
        content:
          text/plain:
            schema:
              type: string
            example: 1.2.3
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:TenantEditor
  /users:
    get:
      summary: Get all users
      tags:
        - users
      parameters:
        - in: query
          name: name
          schema:
            type: string
          description: use params to filter users by username or email
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserPage'
        '401':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - UserKeyAuth:
            - Role:Viewer
  /cloudAdmins:
    get:
      description: Get all active cloud admins
      tags:
        - cloudAdmins
      responses:
        '200':
          description: All active cloud admins
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CloudAdmins'
      security:
        - UserKeyAuth:
            - Role:Viewer
        - ServiceKeyAuth: []
    post:
      description: Grant cloud admin permission
      tags:
        - cloudAdmins
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GrantCloudAdminsRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - UserKeyAuth:
            - Role:RbacEditor
  /cloudAdmins/sync:
    post:
      description: Sync oncalls and cloud admins
      tags:
        - cloudAdmins
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - UserKeyAuth:
            - Role:RbacEditor
  /invitation-codes:
    get:
      summary: Get all invitation codes
      tags:
        - invitationCodes
      responses:
        '200':
          description: Get all invitation codes
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvitationCodes'
      security:
        - UserKeyAuth:
            - Role:Viewer
    post:
      summary: Create invitation codes
      tags:
        - invitationCodes
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateInvitationCodeRequestBody'
      responses:
        '200':
          description: Create invitation code returning
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlainInvitationCodes'
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:InvitationCodeEditor
    delete:
      summary: Soft delete an invitation code
      tags:
        - invitationCodes
      parameters:
        - in: query
          name: id
          required: true
          schema:
            type: integer
            format: uint64
      responses:
        '200':
          description: OK
      security:
        - UserKeyAuth:
            - Role:Editor
        - UserKeyAuth:
            - Role:InvitationCodeEditor
  /notification:
    post:
      tags:
        - notifications
      summary: Post a notification
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostNotificationRequestBody'
      responses:
        '200':
          description: OK
      security:
        - UserKeyAuth:
            - Role:Editor
  /email/raw:
    post:
      tags:
        - notifications
      summary: Send email
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostEmailRawRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
  /region/{region}/workflows:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
    get:
      summary: Get workflows created recently
      tags:
        - workflows
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowPage'
      security:
        - UserKeyAuth:
            - Role:Viewer
  /region/{region}/workflow/{id}:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
    get:
      summary: Get workflow with events
      tags:
        - workflows
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowWithEvents'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Viewer
  /region/{region}/workflow/{id}/cancel:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
    post:
      summary: Cancel a running workflow
      tags:
        - workflows
      responses:
        '200':
          description: OK
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
  /region/{region}/workflow/{id}/resume:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
    post:
      summary: Resume a cancelled workflow
      tags:
        - workflows
      responses:
        '200':
          description: OK
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
  /region/{region}/workflow/{id}/rerun:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
    post:
      description: Rerun a finished workflow
      tags:
        - workflows
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostWorkflowRerunRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
  /region/{region}/workflow/{id}/schedule:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        required: true
    post:
      description: Schedule a running workflow's delay
      tags:
        - workflows
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostWorkflowScheduleRequestBody'
      responses:
        '200':
          description: OK
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
  /region/{region}/clusters:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
    get:
      summary: Get all clusters
      tags:
        - k8sClusters
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagedClusters'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Viewer
        - ServiceKeyAuth: []
    post:
      summary: Create or update cluster by name
      tags:
        - k8sClusters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostClusterRequestBody'
      responses:
        '200':
          description: OK
      security:
        - UserKeyAuth:
            - Role:Admin
        - ServiceKeyAuth: []
  /region/{region}/cluster/{name}:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: name
        schema:
          type: string
        required: true
    get:
      summary: Get a cluster
      tags:
        - k8sClusters
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagedCluster'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Viewer
        - ServiceKeyAuth: []
    post:
      summary: Create or update cluster
      tags:
        - k8sClusters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostClusterRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - UserKeyAuth:
            - Role:Admin
        - ServiceKeyAuth: []
    delete:
      summary: delete cluster
      tags:
        - k8sClusters
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Admin
        - ServiceKeyAuth: []
  /region/{region}/byoc-clusters:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
    get:
      summary: Get all clusters
      tags:
        - byocClusters
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagedClusters'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Viewer
        - ServiceKeyAuth: []
  /region/{region}/byoc-cluster/{id}/access:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: id
        description: id of cluster row
        schema:
          type: integer
          format: uint64
        required: true
    post:
      summary: Get temporary access token for the requested cluster
      tags:
        - byocClusters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostByocClusterAccessRequestBody'
      responses:
        '200':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClusterAccessInfo'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Admin
        - ServiceKeyAuth: []
  /region/{region}/byoc-cluster/{id}/update:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: id
        description: id of cluster row
        schema:
          type: integer
          format: uint64
        required: true
    post:
      summary: update the BYOC cluster version, if version is not specified, update it to the default version
      tags:
        - byocClusters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostByocClusterUpdateRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/DefaultResponse'
        '404':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - UserKeyAuth:
            - Role:Admin
        - ServiceKeyAuth: []
  /region/{region}/clusters/{uuid}/prometheus/api/v1/query:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: uuid
        schema:
          type: string
          format: uuid
        required: true
    get:
      tags:
        - k8sClusters
      responses:
        '200':
          description: prometheus result
          content:
            application/json:
              schema:
                type: object
      security:
        - UserKeyAuth:
            - Role:Viewer
        - ServiceKeyAuth: []
    post:
      tags:
        - k8sClusters
      responses:
        '200':
          description: prometheus result
          content:
            application/json:
              schema:
                type: object
      security:
        - UserKeyAuth:
            - Role:Viewer
        - ServiceKeyAuth: []
  /privatelink-allowed-targets:
    get:
      summary: Get all allowed private link targets
      tags:
        - privatelinks
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrivateLinkAllowedTargetSizePage'
    put:
      tags:
        - privatelinks
      summary: Create/update allowed private link target
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrivateLinkAllowedTarget'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
  /privatelink-allowed-targets/{target}:
    parameters:
      - in: path
        name: target
        schema:
          type: string
        required: true
    get:
      tags:
        - privatelinks
      summary: Get allowed private link target
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrivateLinkAllowedTarget'
    delete:
      tags:
        - privatelinks
      summary: Delete allowed private link target
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
  /region/{region}/tenant/{tenantId}/privatelink/{privateLinkId}:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
      - in: path
        name: privateLinkId
        schema:
          type: string
          format: uuid
        required: true
    delete:
      summary: Delete a tenant's private link by id
      tags:
        - privatelinks
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Editor
  /billing/prometheus/{name}:
    parameters:
      - in: path
        name: name
        schema:
          type: string
        required: true
    get:
      tags:
        - billing
      description: Returns data about the Prometheus endpoint with the given name.
      responses:
        '200':
          description: prometheus info
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PrometheusInfo'
      security:
        - ServiceKeyAuth: []
    put:
      tags:
        - billing
      description: Creates or updates the Prometheus endpoint with the given name.
      requestBody:
        description: Data about the resulting Prometheus endpoint.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PrometheusInfo'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - ServiceKeyAuth: []
    delete:
      tags:
        - billing
      description: Deletes the Prometheus endpoint with the given name.
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - ServiceKeyAuth: []
  /region/{region}/tenant/{tenantId}/diagnosis/report/generate:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
    post:
      summary: generate a RW Diagnosis Report
      description: generate a RW Diagnosis Report from a RW tenant, persist it in a cloud storage bucket, and return its info
      tags:
        - tenants.diagnosis
      operationId: genDiagReport
      responses:
        '200':
          description: Generated Report Info Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RwDiagReport'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:TenantEditor
        - ServiceKeyAuth: []
  /region/{region}/tenant/{tenantId}/diagnosis/report/{reportId}:
    get:
      summary: fetch the content of the RW Diagnosis Report
      description: fetch the content of the pre-generated RW Diagnosis Report if available from a cloud storage bucket
      tags:
        - tenants.diagnosis
      operationId: getDiagReport
      parameters:
        - in: path
          name: region
          schema:
            type: string
          required: true
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
        - in: path
          name: reportId
          schema:
            type: integer
            format: uint64
          required: true
      responses:
        '200':
          description: Generated Report Info Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RwDiagReport'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:Viewer
        - ServiceKeyAuth: []
  /region/{region}/tenant/{tenantId}/diagnosis/report/range:
    get:
      summary: list a set of RW Diagnosis Reports within a specified range of time
      description: list a set of RW Diagnosis Reports (excluding content) within a specified range of time from a cloud storage bucket
      tags:
        - tenants.diagnosis
      operationId: listDiagReportTimeRange
      parameters:
        - in: path
          name: region
          schema:
            type: string
          required: true
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
        - in: query
          name: startTime
          description: RFC3339/RFC3339Nano formatted starting time (inclusive)
          schema:
            type: string
            format: date-time
            nullable: true
            default: null
        - in: query
          name: endTime
          description: RFC3339/RFC3339Nano formatted ending time (inclusive)
          schema:
            type: string
            format: date-time
            nullable: true
            default: null
        - in: query
          name: limit
          description: pagination - how many report metas to return
          schema:
            type: integer
            format: uint64
            nullable: true
            default: null
        - in: query
          name: offset
          description: pagination - how many report metas to skip
          schema:
            type: integer
            format: uint64
            nullable: true
            default: 0
      responses:
        '200':
          description: Retrieved set of Reports Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RwDiagReportMetaPage'
      security:
        - UserKeyAuth:
            - Role:Viewer
        - ServiceKeyAuth: []
  /region/{region}/tenant/{tenantId}/backup:
    post:
      summary: create a backup snapshot for the meta store of the tenant
      tags:
        - tenants.snapshots
      parameters:
        - in: path
          name: region
          schema:
            type: string
          required: true
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
      security:
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/backup/{snapshotId}:
    delete:
      summary: delete a backup snapshot for the meta store of the tenant
      tags:
        - tenants.snapshots
      parameters:
        - in: path
          name: region
          schema:
            type: string
          required: true
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
        - in: path
          name: snapshotId
          schema:
            type: string
            format: uuid
          required: true
      responses:
        '202':
          description: Workflow ID response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OptionalWorkflowIdResponseBody'
      security:
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/backup/{snapshotId}/restore:
    post:
      summary: Restore to a new cluster based on the snapshot id
      tags:
        - tenants.snapshots
      parameters:
        - in: path
          name: region
          schema:
            type: string
          required: true
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
        - in: path
          name: snapshotId
          schema:
            type: string
            format: uuid
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostTenantRestoreRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - UserKeyAuth:
            - Role:Admin
  /region/{region}/tenant/{tenantId}/backup/{snapshotId}/in-place-restore:
    post:
      summary: In place restore to a new cluster based on the snapshot id
      tags:
        - tenants.snapshots
      parameters:
        - in: path
          name: region
          schema:
            type: string
          required: true
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
        - in: path
          name: snapshotId
          schema:
            type: string
            format: uuid
          required: true
      responses:
        '202':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - UserKeyAuth:
            - Role:Admin
  /region/{region}/tenant/{tenantId}/resourceGroups:
    post:
      tags:
        - tenants.resourceGroups
      summary: Create resource group
      parameters:
        - in: path
          name: region
          schema:
            type: string
          required: true
        - in: path
          name: tenantId
          schema:
            type: integer
            format: uint64
          required: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateResourceGroupsRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:TenantEditor
  /region/{region}/tenant/{tenantId}/resourceGroups/{resourceGroup}:
    parameters:
      - in: path
        name: region
        schema:
          type: string
        required: true
      - in: path
        name: tenantId
        schema:
          type: integer
          format: uint64
        required: true
      - in: path
        name: resourceGroup
        schema:
          type: string
        required: true
    post:
      tags:
        - tenants.resourceGroups
      summary: Update resource group
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateResourceGroupsRequestBody'
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:TenantEditor
    delete:
      tags:
        - tenants.resourceGroups
      summary: Delete resource group
      responses:
        '202':
          $ref: '#/components/responses/WorkflowIdResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - UserKeyAuth:
            - Role:TenantEditor
components:
  securitySchemes:
    UserKeyAuth:
      type: http
      scheme: bearer
    ServiceKeyAuth:
      type: http
      scheme: bearer
  responses:
    DefaultResponse:
      description: Default responses returning msg
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    NotFoundResponse:
      description: 404 Not Found
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    WorkflowIdResponse:
      description: Workflow ID response
      content:
        application/json:
          schema:
            type: object
            required:
              - workflowId
            properties:
              workflowId:
                type: string
                format: uuid
    BadRequestResponse:
      description: 400 Bad Request
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    FailedPreconditionResponse:
      description: 400 Failed Precondition
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
  schemas:
    StripeOrg:
      type: object
      required:
        - name
        - stripe_id
      properties:
        name:
          type: string
        stripe_id:
          type: string
    TrialBefore:
      type: object
      required:
        - trial_before
      properties:
        trial_before:
          type: string
          format: date-time
    Region:
      type: object
      required:
        - id
        - regionName
        - isRegionReady
        - isBYOCOnly
        - platform
        - url
        - urlV2
        - adminUrl
        - pgwebUrl
      properties:
        id:
          type: integer
          format: uint64
        regionName:
          type: string
        isRegionReady:
          type: boolean
        isBYOCOnly:
          type: boolean
        platform:
          type: string
        url:
          type: string
        urlV2:
          type: string
        adminUrl:
          type: string
        pgwebUrl:
          type: string
    RegionArray:
      type: array
      items:
        $ref: '#/components/schemas/Region'
    Regions:
      type: object
      required:
        - regions
      properties:
        regions:
          $ref: '#/components/schemas/RegionArray'
    ComponentResource:
      type: object
      required:
        - componentTypeId
        - replica
        - cpu
        - memory
      properties:
        componentTypeId:
          type: string
        cpu:
          type: string
        memory:
          type: string
        replica:
          type: integer
    TenantResourceComponents:
      type: object
      properties:
        standalone:
          $ref: '#/components/schemas/ComponentResource'
        compute:
          $ref: '#/components/schemas/ComponentResource'
        compactor:
          $ref: '#/components/schemas/ComponentResource'
        frontend:
          $ref: '#/components/schemas/ComponentResource'
        meta:
          $ref: '#/components/schemas/ComponentResource'
        etcd:
          $ref: '#/components/schemas/ComponentResource'
    TenantResourceComputeCache:
      type: object
      required:
        - sizeGb
      properties:
        sizeGb:
          type: integer
    MetaStoreType:
      type: string
      enum:
        - etcd
        - postgresql
        - aws_rds
        - gcp_cloudsql
        - azr_postgres
        - sharing_pg
    MetaStoreEtcd:
      type: object
      required:
        - resource
        - sizeGb
      properties:
        resource:
          $ref: '#/components/schemas/ComponentResource'
        sizeGb:
          type: integer
    MetaStorePostgreSql:
      type: object
      required:
        - resource
        - sizeGb
      properties:
        resource:
          $ref: '#/components/schemas/ComponentResource'
        sizeGb:
          type: integer
    MetaStoreAwsRds:
      type: object
      required:
        - instanceClass
        - sizeGb
      properties:
        instanceClass:
          type: string
        sizeGb:
          type: integer
    MetaStoreGcpCloudSql:
      type: object
      required:
        - tier
        - sizeGb
      properties:
        tier:
          type: string
        sizeGb:
          type: integer
    MetaStoreAzrPostgres:
      type: object
      required:
        - sku
        - sizeGb
      properties:
        sku:
          type: string
        sizeGb:
          type: integer
    MetaStoreSharingPg:
      type: object
      required:
        - instanceId
      properties:
        instanceId:
          type: string
    TenantResourceMetaStore:
      type: object
      required:
        - type
        - rwu
      properties:
        type:
          $ref: '#/components/schemas/MetaStoreType'
        rwu:
          type: string
        etcd:
          $ref: '#/components/schemas/MetaStoreEtcd'
        postgresql:
          $ref: '#/components/schemas/MetaStorePostgreSql'
        aws_rds:
          $ref: '#/components/schemas/MetaStoreAwsRds'
        gcp_cloudsql:
          $ref: '#/components/schemas/MetaStoreGcpCloudSql'
        azr_postgres:
          $ref: '#/components/schemas/MetaStoreAzrPostgres'
        sharing_pg:
          $ref: '#/components/schemas/MetaStoreSharingPg'
    TenantResource:
      type: object
      required:
        - components
        - computeCache
      properties:
        components:
          $ref: '#/components/schemas/TenantResourceComponents'
        etcdVolumeSizeGiB:
          type: integer
        computeCache:
          $ref: '#/components/schemas/TenantResourceComputeCache'
        metaStore:
          $ref: '#/components/schemas/TenantResourceMetaStore'
    TierId:
      type: string
      enum:
        - Free
        - Invited
        - Developer-Free
        - Developer-Basic
        - Developer-Test
        - Standard
        - BYOC
        - Test
        - Benchmark
    Tenant:
      type: object
      required:
        - id
        - userId
        - tenantName
        - region
        - status
        - tier
        - resources
        - createdAt
        - imageTag
        - latestImageTag
        - rw_config
        - updatedAt
        - health_status
        - nsId
        - orgId
        - etcd_config
        - usageType
      properties:
        id:
          type: integer
          format: uint64
        userId:
          type: integer
          format: uint64
        orgId:
          type: string
          format: uuid
        tenantName:
          type: string
        region:
          type: string
        resources:
          $ref: '#/components/schemas/TenantResource'
        status:
          type: string
          enum:
            - Creating
            - Running
            - Deleting
            - Failed
            - Stopped
            - Stopping
            - Starting
            - Expired
            - ConfigUpdating
            - Upgrading
            - Updating
            - Snapshotting
            - ExtensionCompactionEnabling
            - ExtensionCompactionDisabling
            - ExtensionServerlessBackfillUpdate
            - ExtensionServerlessBackfillEnabling
            - ExtensionServerlessBackfillDisabling
            - MetaMigrating
            - Restoring
        tier:
          $ref: '#/components/schemas/TierId'
        usageType:
          type: string
          enum:
            - general
            - pipeline
        imageTag:
          type: string
          example: v0.1.12
        latestImageTag:
          type: string
          example: v0.1.12
        rw_config:
          type: string
        etcd_config:
          type: string
        health_status:
          type: string
          enum:
            - Unknown
            - Healthy
            - Unhealthy
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        nsId:
          type: string
          format: uuid
        clusterName:
          type: string
        upcomingSnapshotTime:
          type: string
          format: date-time
    Page:
      type: object
      required:
        - limit
        - offset
      properties:
        limit:
          type: integer
          format: uint64
        offset:
          type: integer
          format: uint64
    TenantArray:
      type: array
      items:
        $ref: '#/components/schemas/Tenant'
    TenantPage:
      type: object
      allOf:
        - $ref: '#/components/schemas/Page'
        - type: object
          required:
            - tenants
          properties:
            tenants:
              $ref: '#/components/schemas/TenantArray'
    TenantExpireInfo:
      type: object
      required:
        - expireAt
        - deleteAt
      properties:
        expireAt:
          type: string
          format: date-time
          nullable: true
        deleteAt:
          type: string
          format: date-time
          nullable: true
    PostTenantExpireRequestBody:
      type: object
      properties:
        expireAt:
          type: string
          format: date-time
        deleteAt:
          type: string
          format: date-time
    PostTenantUpdateVersionRequestBody:
      type: object
      required:
        - version
      properties:
        version:
          type: string
        skipBackup:
          type: boolean
          default: false
    ComponentResourceRequest:
      type: object
      required:
        - componentTypeId
        - replica
      properties:
        componentTypeId:
          type: string
        replica:
          type: integer
    PostTenantResourcesRequestBody:
      type: object
      properties:
        meta:
          $ref: '#/components/schemas/ComponentResourceRequest'
        frontend:
          $ref: '#/components/schemas/ComponentResourceRequest'
        compute:
          $ref: '#/components/schemas/ComponentResourceRequest'
        compactor:
          $ref: '#/components/schemas/ComponentResourceRequest'
        standalone:
          $ref: '#/components/schemas/ComponentResourceRequest'
    PostTenantStatusRequestBody:
      type: object
      required:
        - target
        - previous
      properties:
        target:
          type: string
        previous:
          type: string
    PostTenantTierRequestBody:
      type: object
      required:
        - target
        - previous
      properties:
        target:
          type: string
        previous:
          type: string
    DatabaseConnectionUrl:
      type: object
      required:
        - url
      properties:
        url:
          type: string
          example: postgresql://identifier;username:password@host:port/database
    PostTenantComputeCacheRequestBody:
      type: object
      properties:
        computeCacheSpec:
          $ref: '#/components/schemas/TenantResourceComputeCache'
        resourceGroups:
          type: array
          items:
            type: object
            required:
              - name
              - computeCacheSpec
            properties:
              name:
                type: string
              computeCacheSpec:
                $ref: '#/components/schemas/TenantResourceComputeCache'
    PostTenantExtensionCompactionCompactorRequestBody:
      type: object
      required:
        - CpuRequest
        - CpuLimit
        - MemoryRequest
        - MemoryLimit
      properties:
        CpuRequest:
          type: string
        CpuLimit:
          type: string
        MemoryRequest:
          type: string
        MemoryLimit:
          type: string
    PostTenantExtensionCompactionScalerRequestBody:
      type: object
      required:
        - PollingInterval
        - CollectInterval
        - ScaleDownToZeroRequiredTimes
        - ScaleDownToNRequiredTimes
        - CoolDownPeriod
        - MinReplicas
        - MaxReplicas
        - DesiredReplicas
        - DefaultParallelism
        - ExpirationExpireTime
        - DefaultCapacityReservedBuffer
      properties:
        PollingInterval:
          type: integer
        CollectInterval:
          type: integer
        ScaleDownToZeroRequiredTimes:
          type: integer
        ScaleDownToNRequiredTimes:
          type: integer
        CoolDownPeriod:
          type: integer
        MinReplicas:
          type: integer
        MaxReplicas:
          type: integer
        DesiredReplicas:
          type: integer
        DefaultParallelism:
          type: integer
        ExpirationExpireTime:
          type: integer
        DefaultCapacityReservedBuffer:
          type: integer
    PostTenantExtensionCompactionRequestBody:
      type: object
      properties:
        Compactor:
          $ref: '#/components/schemas/PostTenantExtensionCompactionCompactorRequestBody'
        Scaler:
          $ref: '#/components/schemas/PostTenantExtensionCompactionScalerRequestBody'
    GetTenantExtensionCompactionParametersResponseBody:
      type: object
      required:
        - parameters
      properties:
        Compactor:
          $ref: '#/components/schemas/PostTenantExtensionCompactionCompactorRequestBody'
        Scaler:
          $ref: '#/components/schemas/PostTenantExtensionCompactionScalerRequestBody'
    GetTenantExtensionCompactionStatusResponseBody:
      type: object
      required:
        - status
      properties:
        status:
          type: string
    PostTenantExtensionCompactionStatusRequestBody:
      type: object
      required:
        - previous
        - target
      properties:
        previous:
          type: string
        target:
          type: string
    IcebergCompaction:
      type: object
      required:
        - status
      properties:
        status:
          type: string
        config:
          type: string
        resources:
          $ref: '#/components/schemas/ComponentResource'
    AuthType:
      type: string
      enum:
        - google-oauth2
        - github
        - windowslive
        - local
        - sso
    Role:
      type: object
      required:
        - id
        - name
        - description
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        description:
          type: string
    RoleArray:
      type: array
      items:
        $ref: '#/components/schemas/Role'
    User:
      type: object
      required:
        - id
        - username
        - email
        - org
        - authType
        - createdAt
        - resourceId
      properties:
        id:
          type: integer
          format: uint64
        username:
          type: string
        email:
          type: string
        org:
          type: string
          format: uuid
        createdAt:
          type: string
          format: date-time
        lastLoginAt:
          type: string
          format: date-time
        authType:
          $ref: '#/components/schemas/AuthType'
        resourceId:
          type: string
          format: uuid
        roles:
          $ref: '#/components/schemas/RoleArray'
    UserArray:
      type: array
      items:
        $ref: '#/components/schemas/User'
    UserPage:
      type: object
      allOf:
        - $ref: '#/components/schemas/Page'
        - type: object
          required:
            - users
          properties:
            users:
              $ref: '#/components/schemas/UserArray'
    CloudAdmin:
      type: object
      required:
        - email
        - expiredAt
      properties:
        email:
          type: string
        expiredAt:
          type: string
          format: date-time
    CloudAdminArray:
      type: array
      items:
        $ref: '#/components/schemas/CloudAdmin'
    CloudAdmins:
      type: object
      required:
        - cloudAdmins
      properties:
        cloudAdmins:
          $ref: '#/components/schemas/CloudAdminArray'
    GrantCloudAdminsRequestBody:
      type: object
      required:
        - email
        - validityPeriod
      properties:
        email:
          type: string
        validityPeriod:
          type: string
          example: 12h, 3d
          description: period of validity. Max value 14d
    InvitationCode:
      type: object
      required:
        - id
        - code
        - status
      properties:
        id:
          type: integer
          format: uint64
        code:
          type: string
        org_id:
          type: string
          format: uuid
        user_id:
          type: integer
          format: uint64
        status:
          type: string
          enum:
            - Used
            - Unused
            - Deleted
        created_at:
          type: string
          format: date-time
        redeemed_at:
          type: string
          format: date-time
    InvitationCodeArray:
      type: array
      items:
        $ref: '#/components/schemas/InvitationCode'
    InvitationCodes:
      type: object
      required:
        - invitation_codes
      properties:
        invitation_codes:
          $ref: '#/components/schemas/InvitationCodeArray'
    CreateInvitationCodeRequestBody:
      type: object
      required:
        - number
      properties:
        number:
          type: integer
    PlainInvitationCodes:
      type: object
      required:
        - invitation_codes
      properties:
        invitation_codes:
          type: array
          items:
            type: string
    NotificationSender:
      type: object
      required:
        - name
        - email
      properties:
        name:
          type: string
        email:
          type: string
    PostNotificationRequestBody:
      type: object
      required:
        - sender
        - toUserId
        - title
        - content
        - messageType
      properties:
        sender:
          $ref: '#/components/schemas/NotificationSender'
        toOrgId:
          type: array
          items:
            type: string
            format: uuid
        toUserId:
          type: array
          items:
            type: string
            format: uuid
        title:
          type: string
        content:
          type: string
        messageType:
          type: string
          enum:
            - Alert
            - Message
            - Notification
    PostEmailRawRequestBody:
      type: object
      required:
        - title
        - body
      properties:
        userResourceId:
          type: string
          format: uuid
        orgId:
          type: string
          format: uuid
        email:
          type: string
        title:
          type: string
        body:
          type: string
    Workflow:
      type: object
      required:
        - id
        - workflowType
        - fsmState
        - runningState
        - context
        - createdAt
        - updatedAt
      properties:
        id:
          type: string
          format: uuid
        workflowType:
          type: string
        fsmState:
          type: string
        runningState:
          type: string
          enum:
            - RunningWorkflow
            - FailedWorkflow
            - CompletedWorkflow
            - CancelledWorkflow
        context: {}
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        lockedAt:
          type: string
          format: date-time
        delayAt:
          type: string
          format: date-time
    WorkflowArray:
      type: array
      items:
        $ref: '#/components/schemas/Workflow'
    Workflows:
      type: object
      required:
        - workflows
      properties:
        workflows:
          $ref: '#/components/schemas/WorkflowArray'
    WorkflowPage:
      type: object
      allOf:
        - $ref: '#/components/schemas/Page'
        - $ref: '#/components/schemas/Workflows'
    WorkflowEvent:
      required:
        - id
        - workflowId
        - type
        - timestamp
        - attributes
      properties:
        id:
          type: integer
          format: uint64
        workflowId:
          type: string
          format: uuid
        type:
          type: string
        timestamp:
          type: string
          format: date-time
        attributes: {}
    WorkflowEventArray:
      type: array
      items:
        $ref: '#/components/schemas/WorkflowEvent'
    WorkflowWithEvents:
      type: object
      required:
        - workflow
        - events
      properties:
        workflow:
          $ref: '#/components/schemas/Workflow'
        events:
          $ref: '#/components/schemas/WorkflowEventArray'
    PostWorkflowRerunRequestBody:
      type: object
      properties:
        context:
          type: object
          description: context will be merged into the previous context
        fromState:
          type: string
        delayAt:
          type: string
          format: date-time
    PostWorkflowScheduleRequestBody:
      type: object
      properties:
        delayAt:
          type: string
          format: date-time
          nullable: true
    ClusterStatus:
      type: string
      enum:
        - Uninitialized
        - Provisioned
        - Ready
        - Terminating
        - Deleted
        - PendingResourceDeletion
        - Updating
        - Failed
    ManagedCluster:
      required:
        - id
        - org
        - name
        - status
        - master_url
        - cluster_service_account
        - token
        - serving_type
        - settings
      properties:
        id:
          type: integer
          format: uint64
        org:
          type: string
          format: uuid
        name:
          type: string
        status:
          $ref: '#/components/schemas/ClusterStatus'
        master_url:
          type: string
        cluster_service_account:
          type: string
        token:
          type: string
        serving_type:
          type: string
        settings:
          type: object
          additionalProperties:
            type: string
    ManagedClusterArray:
      type: array
      items:
        $ref: '#/components/schemas/ManagedCluster'
    ManagedClusters:
      type: object
      required:
        - clusters
      properties:
        clusters:
          $ref: '#/components/schemas/ManagedClusterArray'
    PostClusterRequestBody:
      required:
        - name
        - master_url
        - cluster_service_account
        - token
        - serving_type
        - settings
      properties:
        name:
          type: string
        master_url:
          type: string
        cluster_service_account:
          type: string
        token:
          type: string
        serving_type:
          type: string
        settings:
          type: object
          additionalProperties:
            type: string
    PostByocClusterAccessRequestBody:
      required:
        - timeoutMins
      properties:
        timeoutMins:
          type: integer
          description: timeout in minutes
    ClusterAccessInfo:
      required:
        - endpoint
        - caCertBase64
        - token
      properties:
        endpoint:
          type: string
        caCertBase64:
          type: string
        token:
          type: string
    PostByocClusterUpdateRequestBody:
      type: object
      properties:
        version:
          type: string
        customSettings:
          description: base64 encoded custom settings
          type: string
    Size:
      type: object
      required:
        - size
      properties:
        size:
          type: integer
          format: uint64
    PrivateLinkAllowedTarget:
      type: object
      required:
        - target
        - orgId
      properties:
        target:
          type: string
        orgId:
          type: string
          format: uuid
    PrivateLinkAllowedTargetArray:
      type: array
      items:
        $ref: '#/components/schemas/PrivateLinkAllowedTarget'
    PrivateLinkAllowedTargetSizePage:
      type: object
      allOf:
        - $ref: '#/components/schemas/Page'
        - $ref: '#/components/schemas/Size'
        - type: object
          required:
            - privateLinkAllowedTargets
          properties:
            privateLinkAllowedTargets:
              $ref: '#/components/schemas/PrivateLinkAllowedTargetArray'
    PrometheusInfo:
      description: Used to create or update a Prometheus endpoint.
      type: object
      required:
        - prometheus_url
      properties:
        prometheus_url:
          type: string
        prefer_global_src:
          type: boolean
          default: false
    RwDiagReportMeta:
      type: object
      required:
        - id
        - tenantId
        - processedAt
      properties:
        id:
          type: integer
          description: can be unix timestamp seconds, or other things
          format: uint64
        tenantId:
          type: integer
          format: uint64
        processedAt:
          type: string
          format: date-time
    RwDiagReport:
      type: object
      required:
        - meta
        - presignedGetUrl
      properties:
        meta:
          $ref: '#/components/schemas/RwDiagReportMeta'
        presignedGetUrl:
          type: string
    RwDiagReportMetaPage:
      type: object
      required:
        - metas
      properties:
        metas:
          type: array
          items:
            $ref: '#/components/schemas/RwDiagReportMeta'
        limit:
          type: integer
          format: uint64
        offset:
          type: integer
          format: uint64
    OptionalWorkflowIdResponseBody:
      type: object
      properties:
        workflowId:
          type: string
          format: uuid
        msg:
          type: string
    PostTenantRestoreRequestBody:
      type: object
      description: configuration for new tenant to be created in restore command
      required:
        - newTenantName
      properties:
        newTenantName:
          type: string
    CreateResourceGroupsRequestBody:
      type: object
      required:
        - name
        - resource
      properties:
        name:
          type: string
        resource:
          $ref: '#/components/schemas/ComponentResourceRequest'
        fileCacheSizeGb:
          type: integer
    UpdateResourceGroupsRequestBody:
      type: object
      required:
        - resource
      properties:
        resource:
          $ref: '#/components/schemas/ComponentResourceRequest'
