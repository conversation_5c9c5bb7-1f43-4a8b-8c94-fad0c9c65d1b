openapi: 3.0.3

info:
  title: v2
  version: 2.0-alpha

servers:
  - url: /api/v2 # baseURL for kin-openapi openapifilter (should be consistent with spec.GinServerOptions)

tags:
  - name: tenants
    description: Operations about tenants
  - name: databaseUsers
    description: Operations about RisingWave DB users
  - name: privateLinks
    description: Operations about privateLinks
  - name: MViews
    description: Operations about MViews
  - name: tables
    description: Operations about tables
  - name: backups
    description: Operations about backups
  - name: sources
    description: Operations about sources
  - name: sinks
    description: Operations about sinks
  - name: byocClusters
    description: Operations about byoc Clusters
  - name: secrets
    description: Operations about secrets
  - name: metrics
    description: Operations about metrics

paths:
  /tenants:
    post:
      tags:
        - tenants
      summary: Create tenants with tenantName
      x-required-permission: tenants.create
      x-tenant-operation: Provision
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/TenantRequestRequestBody"
      responses:
        "202":
          description: "Create tenant response"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/Tenant"
        "409":
          $ref: "response.yaml#/components/responses/AlreadyExistsResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    get:
      tags:
        - tenants
      summary: List all the tenants owned by the org
      x-required-permission: tenants.list
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/TenantPagination"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/endpoint:
    get:
      tags:
        - tenants
      x-required-permission: endpoints.list
      summary: Get an endpoint of tenant by nsId
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/Endpoint"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}:
    parameters:
      - in: path
        name: nsId
        schema:
          type: string
          format: uuid
        required: true
    get:
      tags:
        - tenants
      summary: Get a tenant by nsId
      x-required-permission: tenants.get
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/Tenant"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    delete:
      tags:
        - tenants
      summary: Delete a tenant by nsId
      x-required-permission: tenants.delete
      x-tenant-operation: Delete
      responses:
        "202":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/updateResource:
    post:
      tags:
        - tenants
      summary: Update a tenant resource by nsId
      x-required-permission: tenants.updateResource
      x-tenant-operation: Scale
      parameters:
        - in: path
          required: true
          name: nsId
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostTenantResourcesRequestBody"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "403":
          $ref: "response.yaml#/components/responses/ForbiddenResponse"
        "409":
          $ref: "response.yaml#/components/responses/AlreadyExistsResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/updateVersion:
    post:
      tags:
        - tenants
      x-required-permission: tenants.update
      summary: Update the tenant rw version to latest
      parameters:
        - in: path
          required: true
          name: nsId
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostTenantUpdateVersionRequestBody"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
        "409":
          $ref: "response.yaml#/components/responses/AlreadyExistsResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/start:
    post:
      tags:
        - tenants
      x-required-permission: tenants.update
      x-tenant-operation: Start
      summary: Start a cluster that has been stopped
      parameters:
        - in: path
          required: true
          name: nsId
          schema:
            type: string
            format: uuid
      responses:
        "202":
          description: cluster start has been initiated
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/stop:
    post:
      tags:
        - tenants
      x-required-permission: tenants.update
      x-tenant-operation: Stop
      summary: Stop a running a cluster
      parameters:
        - in: path
          required: true
          name: nsId
          schema:
            type: string
            format: uuid
      responses:
        "202":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/restart:
    post:
      tags:
        - tenants
      x-required-permission: tenants.update
      x-tenant-operation: Restart
      summary: Restart a cluster
      parameters:
        - in: path
          required: true
          name: nsId
          schema:
            type: string
            format: uuid
      responses:
        "202":
          description: cluster restart has been initiated
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/resourceGroups:
    post:
      tags:
        - resourceGroups
      x-required-permission: resourceGroups.create
      x-allowed-tenant-tiers: Invited,BYOC
      x-tenant-ns-id-path-param-locator: nsId
      summary: Create resource group
      parameters:
        - in: path
          required: true
          name: nsId
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/CreateResourceGroupsRequestBody"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/resourceGroups/{resourceGroup}:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
      - in: path
        required: true
        name: resourceGroup
        schema:
          type: string
    post:
      tags:
        - resourceGroups
      x-required-permission: resourceGroups.update
      x-allowed-tenant-tiers: Invited,BYOC
      x-tenant-ns-id-path-param-locator: nsId
      summary: Update resource group
      requestBody:
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/UpdateResourceGroupsRequestBody"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - resourceGroups
      x-required-permission: resourceGroups.delete
      summary: Delete resource group
      responses:
        "202":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases:
    get:
      tags:
        - tenants
      x-required-permission: databases.list
      summary: List all databases in tenant by nsId
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
      responses:
        "200":
          description: Use the SHOW DATABASES command to show all databases.
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/DatabasesPagination"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/executeSQL:
    post:
      tags:
        - tenants
      summary: Execute a sql with provided rw credentials in tenant by nsId
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/SqlExecutionRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/databases/{databaseName}/querySQL:
    post:
      tags:
        - tenants
      summary: Execute a sql with and return the sql result
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/SqlQueryRequestBody"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/SqlQueryResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/relations:
    parameters:
      - in: path
        name: nsId
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: databaseName
        schema:
          type: string
        required: true
    get:
      tags:
        - tenants
      summary: List all relations in user's risingwave cluster
      description: List all relations with their names and types including materialized view, sink, table, and source
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/TenantRelationsInfo"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/relations/{relName}:
    parameters:
      - in: path
        name: nsId
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: databaseName
        schema:
          type: string
        required: true
      - in: path
        name: relName
        schema:
          type: string
        required: true
    delete:
      tags:
        - tenants
      summary: Delete a relation in user's risingwave cluster
      description: Drop relation (matviews, tables, sources, sinks, ...) in user's risingwave cluster
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databaseUsers:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
    get:
      tags:
        - databaseUsers
      x-required-permission: databaseUsers.list
      summary: List all databaseUsers by nsId
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
      responses:
        "200":
          description: "Database users Response"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/DBUsersPagination"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    post:
      tags:
        - databaseUsers
      x-audit-nodata: true
      x-required-permission: databaseUsers.create
      summary: Create a database user with options SUPERUSER/NOSUPERUSER, CREATEDB/NOCREATEDB, CREATEUSER/NOCREATEUSER, PASSWORD
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/CreateDBUserRequestBody"
      responses:
        "200":
          description: "create db users success"
          content:
            "application/json":
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/DBUser"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "403":
          $ref: "response.yaml#/components/responses/ForbiddenResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
        "409":
          $ref: "response.yaml#/components/responses/AlreadyExistsResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databaseUsers/{dbuserName}:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
      - in: path
        required: true
        name: dbuserName
        schema:
          type: string
    put:
      tags:
        - databaseUsers
      x-audit-nodata: true
      x-required-permission: databaseUsers.update
      summary: Alter db user's password
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/UpdateDBUserRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    delete:
      tags:
        - databaseUsers
      x-required-permission: databaseUsers.delete
      summary: Delete database user by name
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/privatelinks:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
    post:
      tags:
        - privateLinks
      x-required-permission: privateLinks.create
      summary: Create a privateLink to the source/sink in the user's VPC
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostPrivateLinkRequestBody"
      responses:
        "202":
          description: "Create privatelink response"
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/PostPrivateLinkResponseBody"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/privatelinks/{privateLinkId}:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
      - in: path
        required: true
        name: privateLinkId
        schema:
          type: string
          format: uuid
    get:
      tags:
        - privateLinks
      x-required-permission: privateLinks.get
      summary: Get a private link by id
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/PrivateLink"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    delete:
      tags:
        - privateLinks
      x-required-permission: privateLinks.delete
      summary: Delete a private link by id
      responses:
        "202":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/tables:
    get:
      tags:
        - tables
      x-required-permission: tables.list
      summary: List all tables in tenant by nsId and databaseName
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
        - in: query
          name: schema
          schema:
            type: string
      responses:
        "200":
          description: "Tables Response"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/TablesPagination"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/tables/{tableName}:
    get:
      tags:
        - tables
      x-required-permission: tables.get
      summary: Get table by name in tenant
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: tableName
          schema:
            type: string
          required: true
      responses:
        "200":
          description: "Get table by name"
          content:
            "application/json":
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/TableDetail"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    delete:
      tags:
        - tables
      x-required-permission: tables.delete
      summary: delete table by name in tenant
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: tableName
          schema:
            type: string
          required: true
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/tables/{tableName}/dependencies:
    x-internal: true
    get:
      tags:
        - tables
      x-required-permission: tables.getDependencies
      summary: Get table downstream dependencies from risingwave by table name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: tableName
          schema:
            type: string
          required: true
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
        - in: query
          name: type
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/RwDependencyPagination"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/tables/{tableName}/rowCount:
    x-internal: true
    get:
      tags:
        - metrics
      x-required-permission: tables.getMetrics
      summary: Get table row count by table name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: tableName
          schema:
            type: string
          required: true
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/RowCount"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/tables/{tableName}/throughput:
    x-internal: true
    get:
      tags:
        - metrics
      x-required-permission: tables.getMetrics
      summary: Get table throughput by table name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: tableName
          schema:
            type: string
          required: true
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/Throughput"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/tables/{tableName}/dataLatency:
    x-internal: true
    get:
      tags:
        - metrics
      x-required-permission: tables.getMetrics
      summary: Get table data latency by table name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: tableName
          schema:
            type: string
          required: true
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/DataLatency"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/tables/{tableName}/metrics:
    x-internal: true
    get:
      tags:
        - metrics
      x-required-permission: tables.getMetrics
      summary: Get table metrics by table name and metrics name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: tableName
          schema:
            type: string
          required: true
        - in: query
          name: startTime
          schema:
            type: string
            format: date-time
        - in: query
          name: endTime
          schema:
            type: string
            format: date-time
        - in: query
          name: period
          schema:
            type: integer
            format: uint64
        - in: query
          name: metrics
          schema:
            type: string
            enum: [throughput, dataLatency]
          required: true
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/Metrics"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/matviews:
    get:
      tags:
        - MViews
      x-required-permission: materializedViews.list
      summary: List all materialized views in tenant by nsId
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
      responses:
        "200":
          description: "Materialized Views Response"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/MatViewsPagination"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/matviews:
    get:
      tags:
        - MViews
      x-required-permission: materializedViews.list
      summary: List all materialized views in tenant by nsId and databaseName
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: query
          name: schema
          schema:
            type: string
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
      responses:
        "200":
          description: "Materialized Views Response"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/MatViewsPagination"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/matviews/{matViewName}:
    get:
      tags:
        - MViews
      x-required-permission: materializedViews.get
      summary: Get materialized view by name in tenant
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: matViewName
          schema:
            type: string
          required: true
      responses:
        "200":
          description: "Get materialized view by name"
          content:
            "application/json":
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/MatView"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    delete:
      tags:
        - MViews
      x-required-permission: materializedViews.delete
      summary: delete materialized view by name in tenant
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: matViewName
          schema:
            type: string
          required: true
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/matviews/{matViewName}/dependencies:
    x-internal: true
    get:
      tags:
        - MViews
      x-required-permission: materializedViews.getDependencies
      summary: Get materializedView dependencies from risingwave by materializedView name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: matViewName
          schema:
            type: string
          required: true
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
        - in: query
          name: dependencyDirection
          schema:
            type: string
            enum: [downstream, upstream]
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/RwDependencyPagination"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/matviews/{matViewName}/rowCount:
    x-internal: true
    get:
      tags:
        - metrics
      x-required-permission: materializedViews.getMetrics
      summary: Get materializedView row count by materializedView name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: matViewName
          schema:
            type: string
          required: true
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/RowCount"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/matviews/{matViewName}/throughput:
    x-internal: true
    get:
      tags:
        - metrics
      x-required-permission: materializedViews.getMetrics
      summary: Get materializedView throughput by materializedView name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: matViewName
          schema:
            type: string
          required: true
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/Throughput"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/matviews/{matViewName}/dataLatency:
    x-internal: true
    get:
      tags:
        - metrics
      x-required-permission: materializedViews.getMetrics
      summary: Get materializedView data latency by materializedView name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: matViewName
          schema:
            type: string
          required: true
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/DataLatency"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/matviews/{matViewName}/metrics:
    x-internal: true
    get:
      tags:
        - metrics
      x-required-permission: materializedViews.getMetrics
      summary: Get materializedView metrics by materializedView name and metrics name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: path
          name: matViewName
          schema:
            type: string
          required: true
        - in: query
          name: startTime
          schema:
            type: string
            format: date-time
        - in: query
          name: endTime
          schema:
            type: string
            format: date-time
        - in: query
          name: period
          schema:
            type: integer
            format: uint64
        - in: query
          name: metrics
          schema:
            type: string
            enum: [throughput, dataLatency]
          required: true
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/Metrics"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/matviews/{matViewName}/progress:
    x-internal: true
    parameters:
      - in: path
        name: nsId
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: databaseName
        schema:
          type: string
        required: true
      - in: path
        name: matViewName
        schema:
          type: string
        required: true
    get:
      tags:
        - MViews
      x-required-permission: materializedViews.getProgress
      summary: Get mv ddl progress in a tenant by mv name
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/TenantDdlProgress"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/backups:
    parameters:
      - in: path
        name: nsId
        schema:
          type: string
          format: uuid
        required: true
    get:
      tags:
        - backups
      x-required-permission: backups.list
      summary: Get all available backup snapshots
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
      responses:
        "200":
          description: Get all available backup snapshots.
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/BackupSnapshotsPagination"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    post:
      tags:
        - backups
      x-required-permission: backups.create
      summary: Start a new backup procedure
      responses:
        "202":
          description: The backup procedure is started.
          content:
            application/json:
              schema:
                $ref: "mgmt_body.yaml#/components/schemas/PostSnapshotResponseBody"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/backups/{snapshotId}:
    delete:
      tags:
        - backups
      x-required-permission: backups.delete
      summary: Delete a backup snapshot
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: snapshotId
          schema:
            type: string
            format: uuid
          required: true
      responses:
        "202":
          description: The snapshot deletion procedure is started.
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/backups/{snapshotId}/restore:
    post:
      tags:
        - backups
      x-required-permission: backups.restoreSnapshot
      summary: Restore a backup snapshot
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: snapshotId
          schema:
            type: string
            format: uuid
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostTenantRestoreRequestBody"
      responses:
        "202":
          description: Starting to restore data from this snapshot.
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/backups/{snapshotId}/in-place-restore:
    post:
      tags:
        - backups
      x-required-permission: backups.restoreSnapshot
      summary: Restore a backup snapshot
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: snapshotId
          schema:
            type: string
            format: uuid
          required: true
      responses:
        "202":
          description: Starting to restore data from this snapshot.
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/sources/fetchSchema:
    post:
      tags:
        - sources
      x-required-permission: sources.fetchSchema
      summary: Fetch source schema
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostSourcesFetchSchemaRequestBody"
      responses:
        "200":
          description: schema definition of the source
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/ColumnDescs"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /tenants/{nsId}/sources/fetchKafkaSchema:
    post:
      tags:
        - sources
      x-required-permission: sources.fetchSchema
      summary: Fetch kafka row and parse schema
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostSourcesFetchKafkaSchemaRequestBody"
      responses:
        "200":
          description: schema definition of the source
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/ColumnDescs"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/sources/ping:
    post:
      tags:
        - sources
      x-required-permission: sources.ping
      summary: Check connectivity of a source to a tenant
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostSourcesPingRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/AvailabilityResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/sources:
    get:
      tags:
        - sources
      x-required-permission: sources.list
      summary: Get all sources by nsId, dbname, dbuser and dbpwd
      description: Get all sources by posting nsId, dbname, dbuser and dbpwd (also used for db login)
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: query
          name: schema
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/TenantSourcesInfo"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/sources/{sourceName}:
    parameters:
      - in: path
        name: nsId
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: sourceName
        schema:
          type: string
        required: true
      - in: path
        name: databaseName
        schema:
          type: string
        required: true
    get:
      tags:
        - sources
      x-required-permission: sources.get
      summary: Get source schema from risingwave by source name
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/TenantSourceDetail"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    delete:
      tags:
        - sources
      x-required-permission: sources.delete
      summary: delete source from risingwave by source name
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/sources/{sourceName}/dependencies:
    x-internal: true
    get:
      tags:
        - sources
      x-required-permission: sources.getDependencies
      summary: Get source dependencies from risingwave by source name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: sourceName
          schema:
            type: string
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
        - in: query
          name: type
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/RwDependencyPagination"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/sources/{sourceName}/metrics:
    x-internal: true
    get:
      tags:
        - metrics
        - sources
      x-required-permission: sources.getMetrics
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: sourceName
          schema:
            type: string
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: query
          name: startTime
          schema:
            type: string
            format: date-time
        - in: query
          name: endTime
          schema:
            type: string
            format: date-time
        - in: query
          name: period
          schema:
            type: integer
            format: uint64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/Metrics"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/sources/{sourceName}/throughput:
    x-internal: true
    get:
      tags:
        - metrics
        - sources
      x-required-permission: sources.getMetrics
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: sourceName
          schema:
            type: string
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/Throughput"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/sinks:
    get:
      tags:
        - sinks
      x-required-permission: sinks.list
      summary: Get all sinks by tenant id
      description: Get all sinks by posting tenantId, dbname, dbuser and dbpwd (also used for db login)
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: query
          name: schema
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/TenantSinksInfo"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/sinks/{sinkName}:
    x-internal: true
    parameters:
      - in: path
        name: nsId
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: databaseName
        schema:
          type: string
        required: true
      - in: path
        name: sinkName
        schema:
          type: string
        required: true
    get:
      tags:
        - sinks
      x-required-permission: sinks.get
      summary: Get sink info and schema in a tenant by sink name
      description: Get sink info schema by tenant id, database name and sink name.
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/TenantSinkDetail"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    delete:
      tags:
        - sinks
      x-required-permission: sinks.delete
      summary: delete sink from risingwave by sink name
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/sinks/{sinkName}/dependencies:
    x-internal: true
    get:
      tags:
        - sinks
      x-required-permission: sinks.getDependencies
      summary: Get sink dependencies from risingwave by sink name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: sinkName
          schema:
            type: string
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
        - in: query
          name: type
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/RwDependencyPagination"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/sinks/{sinkName}/metrics:
    x-internal: true
    get:
      tags:
        - metrics
        - sinks
      x-required-permission: sinks.getMetrics
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: sinkName
          schema:
            type: string
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: query
          name: startTime
          schema:
            type: string
            format: date-time
        - in: query
          name: endTime
          schema:
            type: string
            format: date-time
        - in: query
          name: period
          schema:
            type: integer
            format: uint64
        - in: query
          name: metrics
          schema:
            type: string
            enum: [throughput, dataLatency]
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/Metrics"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/sinks/{sinkName}/throughput:
    x-internal: true
    get:
      tags:
        - metrics
        - sinks
      x-required-permission: sinks.getMetrics
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: sinkName
          schema:
            type: string
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/Throughput"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/sinks/{sinkName}/dataLatency:
    x-internal: true
    get:
      tags:
        - metrics
        - sinks
      x-required-permission: sinks.getMetrics
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: sinkName
          schema:
            type: string
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/DataLatency"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/sinks/{sinkName}/progress:
    x-internal: true
    parameters:
      - in: path
        name: nsId
        schema:
          type: string
          format: uuid
        required: true
      - in: path
        name: databaseName
        schema:
          type: string
        required: true
      - in: path
        name: sinkName
        schema:
          type: string
        required: true
    get:
      tags:
        - sinks
      x-required-permission: sinks.getProgress
      summary: Get sink ddl progress in a tenant by sink name
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/TenantDdlProgress"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/schemas:
    x-internal: true
    get:
      tags:
        - schemas
      x-required-permission: schemas.list
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/TenantSchemasInfo"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/caCert:
    parameters:
      - in: path
        name: nsId
        schema:
          type: string
          format: uuid
        required: true
    get:
      summary: get the CA cert for the BYOC cluster version
      responses:
        "200":
          description: OK
          content:
            application/x-pem-file:
              schema:
                type: string
                format: binary
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /byoc-clusters:
    post:
      x-allowed-org-tiers: Advanced
      tags:
        - byocClusters
      summary: Create a BYOC cluster
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostByocClustersRequestBody"
      responses:
        "200":
          description: "Create BYOC cluster response"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/ManagedCluster"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "409":
          $ref: "response.yaml#/components/responses/AlreadyExistsResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    get:
      tags:
        - byocClusters
      summary: List BYOC clusters
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/ManagedClustersPagination"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /byoc-clusters/{name}:
    parameters:
      - in: path
        required: true
        name: name
        schema:
          type: string
    put:
      x-allowed-org-tiers: Advanced
      tags:
        - byocClusters
      summary: Update a BYOC cluster
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PutByocClusterRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    get:
      tags:
        - byocClusters
      summary: Get a BYOC cluster
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/ManagedCluster"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    delete:
      tags:
        - byocClusters
      summary: Delete a BYOC cluster
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /byoc-clusters/{name}/manualUpdate:
    parameters:
      - in: path
        required: true
        name: name
        schema:
          type: string
    post:
      x-allowed-org-tiers: Advanced
      tags:
        - byocClusters
      summary: update the BYOC cluster version, if version is not specified, update it to the default version
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostByocClusterUpdateRequestBody"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /byoc-clusters/{name}/update:
    parameters:
      - in: path
        required: true
        name: name
        schema:
          type: string
    post:
      x-allowed-org-tiers: Advanced
      tags:
        - byocClusters
      summary: update the BYOC cluster version, if version is not specified, update it to the default version
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/PostByocClusterUpdateRequestBody"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/secrets:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
      - in: path
        required: true
        name: databaseName
        schema:
          type: string
    get:
      tags:
        - secrets
      x-required-permission: secrets.list
      summary: List all secrets by nsId
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
      responses:
        "200":
          description: "Secrets Response"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/SecretsPagination"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    post:
      tags:
        - secrets
      x-audit-nodata: true
      x-required-permission: secrets.create
      summary: Create a database secret
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "mgmt_body.yaml#/components/schemas/CreateSecretRequestBody"
      responses:
        "200":
          description: "create db users success"
          content:
            "application/json":
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/Secret"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "403":
          $ref: "response.yaml#/components/responses/ForbiddenResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
        "409":
          $ref: "response.yaml#/components/responses/AlreadyExistsResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/secrets/{name}:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
      - in: path
        required: true
        name: databaseName
        schema:
          type: string
      - in: path
        required: true
        name: name
        schema:
          type: string

    delete:
      tags:
        - secrets
      x-required-permission: secrets.delete
      summary: Delete secret by name
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/databases/{databaseName}/secrets/{secretName}/references:
    x-internal: true
    get:
      tags:
        - secrets
      x-required-permission: secrets.getReferences
      summary: Get references from risingwave by secret name
      parameters:
        - in: path
          name: nsId
          schema:
            type: string
            format: uuid
          required: true
        - in: path
          name: secretName
          schema:
            type: string
          required: true
        - in: path
          name: databaseName
          schema:
            type: string
          required: true
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/RwSecretReferencesPagination"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  # Prometheus API endpoints under /tenants/{nsId}/prometheus
  # The following ones are supported:
  # - /api/v1/query
  # - /api/v1/query_range
  # - /api/v1/series
  # - /api/v1/labels
  # - /api/v1/label/{label_name}/values
  # - /api/v1/rules

  /tenants/{nsId}/prometheus/api/v1/query:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid

    get:
      tags:
        - prometheus
      x-required-permission: prometheus.get
      x-audit-nodata: true
      summary: Instant query
      parameters:
        - in: query
          name: query
          required: true
          schema:
            type: string
        - in: query
          name: time
          schema:
            type: string
        - in: query
          name: timeout
          schema:
            type: string
        - in: query
          name: limit
          schema:
            type: integer

      responses:
        "200":
          $ref: "response.yaml#/components/responses/PrometheusAPIResponse"
        "422":
          $ref: "response.yaml#/components/responses/PrometheusAPIResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    post:
      tags:
        - prometheus
      x-required-permission: prometheus.get
      x-audit-nodata: true
      summary: Instant query
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              required: [query]
              properties:
                query:
                  type: string
                time:
                  type: string
                  nullable: true
                timeout:
                  type: string
                  nullable: true
                limit:
                  type: integer
                  nullable: true
      responses:
        "200":
          $ref: "response.yaml#/components/responses/PrometheusAPIResponse"
        "422":
          $ref: "response.yaml#/components/responses/PrometheusAPIResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/prometheus/api/v1/query_range:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid

    get:
      tags:
        - prometheus
      x-required-permission: prometheus.get
      x-audit-nodata: true
      summary: Range query
      parameters:
        - in: query
          name: query
          required: true
          schema:
            type: string
        - in: query
          name: start
          required: true
          schema:
            type: string
        - in: query
          name: end
          required: true
          schema:
            type: string
        - in: query
          name: step
          required: true
          schema:
            type: string
        - in: query
          name: timeout
          schema:
            type: string
        - in: query
          name: limit
          schema:
            type: integer

      responses:
        "200":
          $ref: "response.yaml#/components/responses/PrometheusAPIResponse"
        "422":
          $ref: "response.yaml#/components/responses/PrometheusAPIResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    post:
      tags:
        - prometheus
      x-required-permission: prometheus.get
      x-audit-nodata: true
      summary: Range query
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              required: [query, start, end, step]
              properties:
                query:
                  type: string
                start:
                  type: string
                end:
                  type: string
                step:
                  type: string
                timeout:
                  type: string
                  nullable: true
                limit:
                  type: integer
                  nullable: true
      responses:
        "200":
          $ref: "response.yaml#/components/responses/PrometheusAPIResponse"
        "422":
          $ref: "response.yaml#/components/responses/PrometheusAPIResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/prometheus/api/v1/series:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid

    get:
      tags:
        - prometheus
      x-required-permission: prometheus.get
      x-audit-nodata: true
      summary: Query series
      parameters:
        - in: query
          name: "match[]"
          required: true
          schema:
            type: array
            items:
              type: string
        - in: query
          name: start
          schema:
            type: string
        - in: query
          name: end
          schema:
            type: string
        - in: query
          name: limit
          schema:
            type: integer

      responses:
        "200":
          $ref: "response.yaml#/components/responses/PrometheusAPIResponse"
        "422":
          $ref: "response.yaml#/components/responses/PrometheusAPIResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    post:
      tags:
        - prometheus
      x-required-permission: prometheus.get
      x-audit-nodata: true
      summary: Query series
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              required: ["match[]"]
              properties:
                "match[]":
                  type: array
                  items:
                    type: string
                start:
                  type: string
                  nullable: true
                end:
                  type: string
                  nullable: true
                limit:
                  type: integer
                  nullable: true
      responses:
        "200":
          $ref: "response.yaml#/components/responses/PrometheusAPIResponse"
        "422":
          $ref: "response.yaml#/components/responses/PrometheusAPIResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/prometheus/api/v1/labels:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid

    get:
      tags:
        - prometheus
      x-required-permission: prometheus.get
      x-audit-nodata: true
      summary: Query labels
      parameters:
        - in: query
          name: "match[]"
          schema:
            type: array
            items:
              type: string
        - in: query
          name: start
          schema:
            type: string
        - in: query
          name: end
          schema:
            type: string
        - in: query
          name: limit
          schema:
            type: integer

      responses:
        "200":
          $ref: "response.yaml#/components/responses/PrometheusAPIResponse"
        "422":
          $ref: "response.yaml#/components/responses/PrometheusAPIResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    post:
      tags:
        - prometheus
      x-required-permission: prometheus.get
      x-audit-nodata: true
      summary: Query labels
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                "match[]":
                  type: array
                  items:
                    type: string
                  nullable: true
                start:
                  type: string
                  nullable: true
                end:
                  type: string
                  nullable: true
                limit:
                  type: integer
                  nullable: true
      responses:
        "200":
          $ref: "response.yaml#/components/responses/PrometheusAPIResponse"
        "422":
          $ref: "response.yaml#/components/responses/PrometheusAPIResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/prometheus/api/v1/label/{labelName}/values:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
      - in: path
        required: true
        name: labelName
        schema:
          type: string

    get:
      tags:
        - prometheus
      x-required-permission: prometheus.get
      x-audit-nodata: true
      summary: Query values
      parameters:
        - in: query
          name: "match[]"
          schema:
            type: array
            items:
              type: string
        - in: query
          name: start
          schema:
            type: string
        - in: query
          name: end
          schema:
            type: string
        - in: query
          name: limit
          schema:
            type: integer

      responses:
        "200":
          $ref: "response.yaml#/components/responses/PrometheusAPIResponse"
        "422":
          $ref: "response.yaml#/components/responses/PrometheusAPIResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/prometheus/api/v1/rules:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid

    get:
      tags:
        - prometheus
      x-required-permission: prometheus.get
      x-audit-nodata: true
      summary: Dummy rules
      responses:
        "200":
          $ref: "response.yaml#/components/responses/PrometheusAPIResponse"
        "422":
          $ref: "response.yaml#/components/responses/PrometheusAPIResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/metrics:
    x-internal: true
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid
    get:
      tags:
        - "metrics"
      x-required-permission: tenants.getMetrics
      responses:
        "200":
          description: Pull the risingwave metrics of this tenant.
          content:
            text/plain:
              schema:
                type: string

      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /tenants/{nsId}/extensions/iceberg-compaction:
    parameters:
      - in: path
        required: true
        name: nsId
        schema:
          type: string
          format: uuid

    get:
      tags:
        - tenants
      x-required-permission: tenants.get
      x-audit-nodata: true
      summary: Get iceberg compaction status
      responses:
        "200":
          description: "Iceberg compaction status"
          content:
            application/json:
              schema:
                $ref: "mgmt_resource.yaml#/components/schemas/IcebergCompaction"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    post:
      x-allowed-tenant-tiers: Invited,BYOC
      x-tenant-ns-id-path-param-locator: nsId
      tags:
        - tenants
      x-required-permission: tenants.update
      x-audit-nodata: true
      summary: Enable iceberg compaction
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                config:
                  type: string
                resources:
                  $ref: "mgmt_body.yaml#/components/schemas/ComponentResourceRequest"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    put:
      tags:
        - tenants
      x-required-permission: tenants.update
      x-allowed-tenant-tiers: Invited,BYOC
      x-tenant-ns-id-path-param-locator: nsId
      x-audit-nodata: true
      summary: Update iceberg compaction
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                config:
                  type: string
                resources:
                  $ref: "mgmt_body.yaml#/components/schemas/ComponentResourceRequest"
      responses:
        "202":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    delete:
      tags:
        - tenants
      x-required-permission: tenants.update
      x-audit-nodata: true
      summary: Disable iceberg compaction
      responses:
        "202":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-KEY
      description: Service account api key. Header format '${key}:${secret}'
