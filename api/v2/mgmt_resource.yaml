openapi: 3.0.3

info:
  title: v2
  version: 2.0-alpha
paths: {}
components:
  schemas:
    ##############################
    # Tier
    ##############################
    TierId:
      type: string
      enum:
        - Free
        - Invited
        - Developer-Free
        - Developer-Basic
        - Developer-Test
        - Standard
        - BYOC
        - Test
        - Benchmark
    Tier:
      type: object
      required:
        - name
        - availableStandaloneNodes
        - availableComputeNodes
        - availableCompactorNodes
        - availableFrontendNodes
        - availableMetaNodes
        - maximumComputeNodeFileCacheSizeGiB
        - validityPeriod
        - retentionPeriod
      properties:
        id:
          $ref: "#/components/schemas/TierId"
        availableStandaloneNodes:
          type: array
          items:
            $ref: "#/components/schemas/AvailableComponentType"
        availableComputeNodes:
          type: array
          items:
            $ref: "#/components/schemas/AvailableComponentType"
        availableCompactorNodes:
          type: array
          items:
            $ref: "#/components/schemas/AvailableComponentType"
        availableFrontendNodes:
          type: array
          items:
            $ref: "#/components/schemas/AvailableComponentType"
        availableMetaNodes:
          type: array
          items:
            $ref: "#/components/schemas/AvailableComponentType"
        maximumComputeNodeFileCacheSizeGiB:
          type: integer
        validityPeriod:
          type: integer
        retentionPeriod:
          type: integer
        availableMetaStore:
          $ref: "#/components/schemas/AvailableMetaStore"
    TierArray:
      type: array
      items:
        $ref: "#/components/schemas/Tier"
    Tiers:
      type: object
      required: [tiers]
      properties:
        tiers:
          $ref: "mgmt_resource.yaml#/components/schemas/TierArray"
    AvailableComponentType:
      type: object
      required: [id, cpu, memory, maximum]
      properties:
        id:
          type: string
        cpu:
          type: string
        memory:
          type: string
        maximum:
          type: integer
    AvailableMetaStore:
      type: object
      properties:
        etcd:
          $ref: "mgmt_resource.yaml#/components/schemas/AvailableMetaStoreEtcd"
        postgresql:
          $ref: "mgmt_resource.yaml#/components/schemas/AvailableMetaStorePostgreSql"
        aws_rds:
          $ref: "mgmt_resource.yaml#/components/schemas/AvailableMetaStoreAwsRds"
        gcp_cloudsql:
          $ref: "mgmt_resource.yaml#/components/schemas/AvailableMetaStoreGcpCloudSql"
        sharing_pg:
          $ref: "mgmt_resource.yaml#/components/schemas/AvailableMetaStoreSharingPg"

    AvailableMetaStoreEtcd:
      type: object
      required: [nodes, maximumSizeGiB]
      properties:
        nodes:
          type: array
          items:
            $ref: "#/components/schemas/AvailableComponentType"
        maximumSizeGiB:
          type: integer
    AvailableMetaStorePostgreSql:
      type: object
      required: [nodes, maximumSizeGiB]
      properties:
        nodes:
          type: array
          items:
            $ref: "#/components/schemas/AvailableComponentType"
        maximumSizeGiB:
          type: integer
    AvailableMetaStoreAwsRds:
      type: object
      properties:
        enabled:
          type: boolean
    AvailableMetaStoreGcpCloudSql:
      type: object
      properties:
        enabled:
          type: boolean
    AvailableMetaStoreSharingPg:
      type: object
      properties:
        enabled:
          type: boolean

    ##############################
    # Tenant
    ##############################
    Tenant:
      type: object
      required:
        - id
        - userId
        - tenantName
        - region
        - status
        - tier
        - resources
        - createdAt
        - imageTag
        - latestImageTag
        - rw_config
        - updatedAt
        - health_status
        - nsId
        - orgId
        - etcd_config
        - usageType
      properties:
        id:
          type: integer
          format: uint64
        userId:
          type: integer
          format: uint64
        orgId:
          type: string
          format: uuid
        tenantName:
          type: string
        region:
          type: string
        resources:
          $ref: "#/components/schemas/TenantResource"
        status:
          type: string
          enum:
            - Creating
            - Running
            - Deleting
            - Failed
            - Stopped
            - Stopping
            - Starting
            - Expired
            - ConfigUpdating
            - Upgrading
            - Updating
            - Snapshotting
            - ExtensionCompactionEnabling
            - ExtensionCompactionDisabling
            - ExtensionServerlessBackfillEnabling
            - ExtensionServerlessBackfillUpdate
            - ExtensionServerlessBackfillDisabling
            - MetaMigrating
            - Restoring
        tier:
          $ref: "#/components/schemas/TierId"
        usageType:
          type: string
          enum:
            - general
            - pipeline
        imageTag:
          type: string
          example: v0.1.12
        latestImageTag:
          type: string
          example: v0.1.12
        rw_config:
          type: string
        etcd_config:
          type: string
        health_status:
          type: string
          enum:
            - Unknown
            - Healthy
            - Unhealthy
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        nsId:
          type: string
          format: uuid
        clusterName:
          type: string
        upcomingSnapshotTime:
          type: string
          format: date-time
        # Extensions will only be returned by the GET tenant API.
        extensions:
          $ref: "#/components/schemas/TenantExtensions"
    TenantArray:
      type: array
      items:
        $ref: "#/components/schemas/Tenant"
    TenantPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: "schema.yaml#/components/schemas/Pagination"
        - type: object
          required: [tenants]
          properties:
            tenants:
              $ref: "#/components/schemas/TenantArray"
    TenantExtensions:
      type: object
      properties:
        serverlessCompaction:
          $ref: "#/components/schemas/TenantExtensionServerlessCompaction"
    TenantExtensionServerlessCompaction:
      type: object
      required: [enabled, maximumCompactionConcurrency]
      properties:
        enabled:
          type: boolean
        maximumCompactionConcurrency:
          type: integer
    TenantResourceComputeCache:
      type: object
      required:
        - sizeGb
      properties:
        sizeGb:
          type: integer
    TenantResource:
      type: object
      required: [components, computeCache]
      properties:
        components:
          $ref: "#/components/schemas/TenantResourceComponents"
        etcdVolumeSizeGiB:
          type: integer
        computeCache:
          $ref: "#/components/schemas/TenantResourceComputeCache"
        metaStore:
          $ref: "#/components/schemas/TenantResourceMetaStore"
    TenantResourceComponents:
      type: object
      properties:
        standalone:
          $ref: "#/components/schemas/ComponentResource"
        compute:
          $ref: "#/components/schemas/ComponentResource"
        compactor:
          $ref: "#/components/schemas/ComponentResource"
        frontend:
          $ref: "#/components/schemas/ComponentResource"
        meta:
          $ref: "#/components/schemas/ComponentResource"
        etcd:
          $ref: "#/components/schemas/ComponentResource"
    ComponentResource:
      type: object
      required: [componentTypeId, replica, cpu, memory]
      properties:
        componentTypeId:
          type: string
        cpu:
          type: string
        memory:
          type: string
        replica:
          type: integer
    TenantResourceMetaStore:
      type: object
      required: [type, rwu]
      properties:
        type:
          $ref: "#/components/schemas/MetaStoreType"
        rwu:
          type: string
        etcd:
          $ref: "#/components/schemas/MetaStoreEtcd"
        postgresql:
          $ref: "#/components/schemas/MetaStorePostgreSql"
        aws_rds:
          $ref: "#/components/schemas/MetaStoreAwsRds"
        gcp_cloudsql:
          $ref: "#/components/schemas/MetaStoreGcpCloudSql"
        azr_postgres:
          $ref: "#/components/schemas/MetaStoreAzrPostgres"
        sharing_pg:
          $ref: "#/components/schemas/MetaStoreSharingPg"
    MetaStoreType:
      type: string
      enum:
        - etcd
        - postgresql
        - aws_rds
        - gcp_cloudsql
        - azr_postgres
        - sharing_pg
    MetaStoreEtcd:
      type: object
      required: [resource, sizeGb]
      properties:
        resource:
          $ref: "#/components/schemas/ComponentResource"
        sizeGb:
          type: integer
    MetaStorePostgreSql:
      type: object
      required: [resource, sizeGb]
      properties:
        resource:
          $ref: "#/components/schemas/ComponentResource"
        sizeGb:
          type: integer
    MetaStoreAwsRds:
      type: object
      required: [instanceClass, sizeGb]
      properties:
        instanceClass:
          type: string
        sizeGb:
          type: integer
    MetaStoreGcpCloudSql:
      type: object
      required: [tier, sizeGb]
      properties:
        tier:
          type: string
        sizeGb:
          type: integer
    MetaStoreAzrPostgres:
      type: object
      required: [sku, sizeGb]
      properties:
        sku:
          type: string
        sizeGb:
          type: integer
    MetaStoreSharingPg:
      type: object
      required: [instanceId]
      properties:
        instanceId:
          type: string

    TenantStatusCount:
      type: object
      required: [status, count]
      properties:
        count:
          type: integer
          format: int64
        status:
          type: string
    TenantStatusCountArray:
      type: array
      items:
        $ref: "#/components/schemas/TenantStatusCount"
    TenantStatusCounts:
      type: object
      required: [status]
      properties:
        status:
          $ref: "mgmt_resource.yaml#/components/schemas/TenantStatusCountArray"

    IcebergCompaction:
      type: object
      required: [status]
      properties:
        status:
          type: string
        config:
          type: string
        resources:
          $ref: "#/components/schemas/ComponentResource"

    ##############################
    # DBUser
    ##############################
    DBUser:
      type: object
      required:
        [usesysid, username, usecreatedb, usesuper, usecreateuser, canlogin]
      properties:
        usesysid:
          type: integer
          format: uint64
        username:
          type: string
        usecreatedb:
          type: boolean
        usesuper:
          type: boolean
        usecreateuser:
          type: boolean
        canlogin:
          type: boolean
    DBUserArray:
      type: array
      items:
        $ref: "#/components/schemas/DBUser"
    DBUsersPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: "schema.yaml#/components/schemas/Pagination"
        - type: object
          required: [dbusers]
          properties:
            dbusers:
              $ref: "#/components/schemas/DBUserArray"

    ##############################
    # Databases
    ##############################
    Database:
      type: object
      required: [name, resourceGroup]
      properties:
        name:
          type: string
        resourceGroup:
          type: string

    DatabasesPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: "schema.yaml#/components/schemas/Pagination"
        - type: object
          required: [databases]
          properties:
            databases:
              type: array
              items:
                $ref: "#/components/schemas/Database"

    ##############################
    # Table
    ##############################
    Table:
      type: object
      required:
        - databaseName
        - definition
        - tableId
        - tableName
        - tableSchema
        - tableOwner
      properties:
        databaseName:
          type: string
        definition:
          type: string
        tableId:
          type: integer
          format: int64
        tableName:
          type: string
        tableSchema:
          type: string
        tableOwner:
          type: string
        createdAt:
          type: string
          format: date-time
        size:
          type: string
        columns:
          $ref: "#/components/schemas/ColumnDescArray"
    TableDetail:
      type: object
      required: [table, connector]
      properties:
        table:
          $ref: "#/components/schemas/Table"
        connector:
          $ref: "#/components/schemas/Connector"
    TableArray:
      type: array
      items:
        $ref: "#/components/schemas/Table"
    TablesPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: "schema.yaml#/components/schemas/Pagination"
        - type: object
          required: [tables]
          properties:
            tables:
              $ref: "#/components/schemas/TableArray"

    Connector:
      type: object
      properties:
        connectorType:
          type: string
        importantConfigs:
          type: object
          additionalProperties:
            type: string
        properties:
          type: object
          additionalProperties:
            type: string

    ##############################
    # MatView and Endpoint
    ##############################
    MatView:
      type: object
      required:
        - databaseName
        - definition
        - matViewId
        - matViewName
        - matViewSchema
        - matViewOwner
        - fragmentIds
      properties:
        databaseName:
          type: string
        definition:
          type: string
        matViewId:
          type: integer
          format: int64
        matViewName:
          type: string
        matViewSchema:
          type: string
        matViewOwner:
          type: string
        fragmentIds:
          type: array
          items:
            type: integer
            format: int64
        columns:
          $ref: "#/components/schemas/ColumnDescArray"
        createdAt:
          type: string
          format: date-time
        size:
          type: string
    MatViewArray:
      type: array
      items:
        $ref: "#/components/schemas/MatView"
    MatViewsPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: "schema.yaml#/components/schemas/Pagination"
        - type: object
          required: [matViews]
          properties:
            matViews:
              $ref: "#/components/schemas/MatViewArray"

    Endpoint:
      type: object
      required:
        [id, nsId, host, port, database, options, internalHost, internalPort]
      properties:
        id:
          type: integer
          format: int64
        nsId:
          type: string
          format: uuid
        host:
          type: string
        port:
          type: integer
        database:
          type: string
        options:
          type: string
        internalHost:
          type: string
        internalPort:
          type: integer
        awsServingPrivateLink:
          type: object
          $ref: "#/components/schemas/AWSServingPrivateLinkInfo"

    AWSServingPrivateLinkInfo:
      type: object
      required: [host, port, serviceName, azs]
      properties:
        host:
          type: string
        port:
          type: integer
        serviceName:
          type: string
        azs:
          type: array
          items:
            type: string

    ##############################
    # Private Link
    ##############################
    PrivateLink:
      type: object
      required: [id, tenantId, status, connectionState, connectionName]
      properties:
        connectionName:
          type: string
        id:
          type: string
          format: uuid
        tenantId:
          type: integer
          format: int64
        status:
          type: string
          enum: [CREATING, CREATED, DELETING, ERROR, UNKNOWN]
        connectionState:
          type: string
          enum: [STATUS_UNSPECIFIED, PENDING, ACCEPTED, REJECTED, CLOSED]
        target:
          type: string
        endpoint:
          type: string
    PrivateLinkArray:
      type: array
      items:
        $ref: "#/components/schemas/PrivateLink"
    PrivateLinkPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: "schema.yaml#/components/schemas/Pagination"
        - type: object
          required: [privateLinks]
          properties:
            privateLinks:
              $ref: "#/components/schemas/PrivateLinkArray"

    ##############################
    # Metrics
    ##############################
    MetricPoint:
      type: object
      required: [timestamp, value]
      properties:
        timestamp:
          type: string
          format: date-time
        value:
          type: number
          format: double

    LabelItem:
      type: object
      required: [key, value]
      properties:
        key:
          type: string
        value:
          type: string

    MetricItem:
      type: object
      required: [labels, values]
      properties:
        labels:
          type: array
          items:
            $ref: "#/components/schemas/LabelItem"
        values:
          type: array
          items:
            $ref: "#/components/schemas/MetricPoint"

    Throughput:
      type: object
      required: [value]
      description: unit in rows/s
      properties:
        value:
          type: number
          format: double

    RowCount:
      type: object
      required: [value]
      properties:
        value:
          type: integer
          format: uint64

    DataLatency:
      type: object
      required: [value]
      description: unit in second
      properties:
        value:
          type: number
          format: double

    Metrics:
      type: object
      required: [name, items]
      properties:
        name:
          type: string
        items:
          type: array
          items:
            $ref: "#/components/schemas/MetricItem"

    ##############################
    # Tenant Expiration
    ##############################
    TenantExpireInfo:
      type: object
      required: [expireAt, deleteAt]
      properties:
        expireAt:
          type: string
          format: date-time
          nullable: true
        deleteAt:
          type: string
          format: date-time
          nullable: true

    ##############################
    # Source
    ##############################
    KafkaConfig:
      type: object
      required:
        - server
        - topic
      properties:
        server:
          type: string
        topic:
          type: string
        securityProtocol: # SASL_SSL
          type: string
          enum: [SASL_SSL, SSL, SASL_PLAINTEXT]
        saslMechanisms: # PLAIN
          type: string
          enum: [OAUTHBEARER, GSSAPI, PLAIN, SCRAM-SHA-256, SCRAM-SHA-512]
        saslUsername: # API KEY
          type: string
        saslPassword: # API SECRET
          type: string
        caCertificate: # PEM encoded CA certificate
          type: string
    PostgresCdcConfig:
      type: object
      required: [hostname, port, username, password, database]
      properties:
        hostname:
          type: string
        port:
          type: integer
        username:
          type: string
        password:
          type: string
        database:
          type: string
        sslMode:
          type: string
          enum: [disabled, preferred, required, verify-ca, verify-full]

    ColumnDesc:
      type: object
      required: [name, type]
      properties:
        name:
          type: string
        type:
          type: string
        fields:
          $ref: "#/components/schemas/ColumnDescArray"
    ColumnDescArray:
      type: array
      items:
        $ref: "#/components/schemas/ColumnDesc"
    ColumnDescs:
      type: object
      required: [columns]
      properties:
        columns:
          $ref: "#/components/schemas/ColumnDescArray"

    TenantSourceDetail:
      type: object
      required: [info, columns, importantConfigs]
      properties:
        info:
          $ref: "#/components/schemas/SourceInfo"
        columns:
          $ref: "#/components/schemas/ColumnDescArray"
        importantConfigs:
          type: object
          additionalProperties:
            type: string

    TenantSourcesInfo:
      type: object
      required:
        - sources
      properties:
        sources:
          type: array
          items:
            $ref: "#/components/schemas/SourceInfo"

    TenantRelationsInfo:
      type: object
      required:
        - relations
      properties:
        relations:
          type: array
          items:
            $ref: "#/components/schemas/RelationInfo"

    RelationInfo:
      type: object
      required: [relname, reltype]
      properties:
        relname:
          type: string
        reltype:
          type: string

    TenantSinksInfo:
      type: object
      required:
        - sinks
      properties:
        sinks:
          type: array
          items:
            $ref: "#/components/schemas/SinkInfo"

    TenantSinkDetail:
      type: object
      required: [info, columns, importantConfigs]
      properties:
        info:
          $ref: "#/components/schemas/SinkInfo"
        columns:
          $ref: "#/components/schemas/ColumnDescArray"
        importantConfigs:
          type: object
          additionalProperties:
            type: string

    SourceInfo:
      type: object
      required:
        [
          id,
          sourceName,
          isSchemaRegistry,
          hasPrimaryKey,
          definition,
          properties,
          owner,
          schema,
        ]
      properties:
        id:
          type: integer
          format: int32
        sourceName:
          type: string
        connectorType:
          type: string
        topic:
          type: string
        endpoint:
          type: string
        scan.startup.mode: # earliest, latest
          type: string
        scan.startup.timestamp_millis: # UNIX timestamp (milliseconds)
          type: string
        connection.name: # private link connection name
          type: string
        webLocation:
          type: string
        healthCheck:
          type: string
          enum: [Active, Failed, Unknown]
        throughput:
          type: number
          format: double
        dataFormat:
          type: string
          # !: enum is ignored since we need to use RegExp to parse source definition
          # enum: [AVRO, UPSERT_AVRO, JSON, UPSERT_JSON, PROTOBUF, DEBEZIUM_JSON, CSV, MAXWELL]
        isSchemaRegistry:
          type: boolean
          default: false
        hasPrimaryKey:
          type: boolean
          default: false
        definition:
          type: string
        properties:
          type: object
          additionalProperties:
            type: string
        createdAt:
          type: string
          format: date-time
        owner:
          type: string
        schema:
          type: string

    SinkInfo:
      type: object
      required: [id, sinkName, definition, connectorType, owner, schema]
      properties:
        id:
          type: integer
          format: int32
        sinkName:
          type: string
        connectorType:
          type: string
        endpoint:
          type: string
        type:
          type: string # upsert, debezium, force_append_only
        force_append_only:
          type: string # true when type is force_append_only
        primary_key:
          type: string
        healthCheck:
          type: string
          enum: [Active, Failed]
          default: Active
        throughput:
          type: number
          format: double
        properties:
          type: object
          additionalProperties:
            type: string
        definition:
          type: string
        createdAt:
          type: string
          format: date-time
        owner:
          type: string
        schema:
          type: string

    RwDependencyPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: "schema.yaml#/components/schemas/Pagination"
        - type: object
          required: [dependencies]
          properties:
            dependencies:
              $ref: "#/components/schemas/RwDependencyArray"

    RwDependencyArray:
      type: array
      items:
        $ref: "#/components/schemas/RwDependency"

    RwDependency:
      type: object
      required: [name, type, id, schema]
      properties:
        name:
          type: string
        type:
          type: string
        id:
          type: integer
          format: uint64
        schema:
          type: string

    SchemaInfo:
      type: object
      required: [catalog_name, schema_name, schema_owner]
      properties:
        catalog_name:
          type: string
        schema_name:
          type: string
        schema_owner:
          type: string

    TenantSchemasInfo:
      type: object
      required:
        - schemas
      properties:
        schemas:
          type: array
          items:
            $ref: "#/components/schemas/SchemaInfo"

    RwSecretReferencesPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: "schema.yaml#/components/schemas/Pagination"
        - type: object
          required: [references]
          properties:
            references:
              $ref: "#/components/schemas/RwDependencyArray"

    TenantDdlProgress:
      type: object
      required: [status, progress, initialized_at]
      properties:
        status:
          $ref: "#/components/schemas/DdlProgressStatus"
        progress:
          type: string
        initialized_at:
          type: string
          format: date-time

    DdlProgressStatus:
      type: string
      enum:
        - InProgress
        - Completed

    ##############################
    # Query
    ##############################
    QueryColumn:
      type: object
      required: [name, dataType]
      properties:
        name:
          type: string
        dataType:
          type: string
    QueryRow:
      type: array
      items: {} # any values

    ##############################
    # Managed Cluster
    ##############################
    ClusterStatus:
      type: string
      enum:
        [
          Uninitialized,
          Provisioned,
          Ready,
          Terminating,
          Deleted,
          PendingResourceDeletion,
          Updating,
          Failed,
        ]

    ManagedCluster:
      required:
        [
          id,
          org,
          name,
          status,
          master_url,
          cluster_service_account,
          token,
          serving_type,
          settings,
        ]
      properties:
        id:
          type: integer
          format: uint64
        org:
          type: string
          format: uuid
        name:
          type: string
        status:
          $ref: "#/components/schemas/ClusterStatus"
        master_url:
          type: string
        cluster_service_account:
          type: string
        token:
          type: string
        serving_type:
          type: string
        settings: # map[string]string
          type: object
          additionalProperties:
            type: string
    ManagedClusterArray:
      type: array
      items:
        $ref: "#/components/schemas/ManagedCluster"
    ManagedClustersPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: "schema.yaml#/components/schemas/Pagination"
        - type: object
          required: [clusters]
          properties:
            clusters:
              $ref: "#/components/schemas/ManagedClusterArray"

    BackupSnapshotItem:
      type: object
      required: [id, created_at_unix_mills, status]
      properties:
        id:
          type: string
          format: uuid
        created_at_unix_mills:
          type: integer
          format: int64
        meta_snapshot_id:
          type: integer
          format: int64
        status:
          type: string
        rw_version:
          type: string

    BackupSnapshotsPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: "schema.yaml#/components/schemas/Pagination"
        - type: object
          required: [items]
          properties:
            items:
              type: array
              items:
                $ref: "#/components/schemas/BackupSnapshotItem"

    ##############################
    # Secrets
    ##############################
    Secret:
      type: object
      required: [id, name, owner, schema]
      properties:
        id:
          type: integer
          format: uint64
        name:
          type: string
        owner:
          type: string
        schema:
          type: string
    SecretArray:
      type: array
      items:
        $ref: "#/components/schemas/Secret"
    SecretsPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: "schema.yaml#/components/schemas/Pagination"
        - type: object
          required: [secrets]
          properties:
            secrets:
              $ref: "#/components/schemas/SecretArray"
