# Code generated by redocly/cli DO NOT EDIT.
openapi: 3.0.3
info:
  title: v2
  version: 2.0-alpha
servers:
  - url: /api/v2
tags:
  - name: users
    description: Access to user's profile
  - name: notifications
    description: Messages and alerts
  - name: invitations
    description: Access to org's invitations
  - name: serviceAccounts
    description: Access to org's service accounts and api keys
  - name: roles
    description: Access to org's roles and role bindings
  - name: orgs
    description: Access to org's information
paths:
  /users/{id}:
    x-internal: true
    delete:
      tags:
        - users
      description: delete user in org by id using jwt
      x-required-permission: users.delete
      parameters:
        - in: path
          required: true
          name: id
          schema:
            type: string
            format: uuid
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    get:
      tags:
        - users
      description: get user in org by id using jwt
      x-required-permission: users.get
      parameters:
        - in: path
          required: true
          name: id
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /users:
    x-internal: true
    get:
      tags:
        - users
      description: Get users owned by org
      x-required-permission: users.list
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 50
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserPagination'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /invitations:
    x-internal: true
    post:
      tags:
        - invitations
      description: Invite new user into target organization.
      x-required-permission: invitations.create
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateInvitationRequestBody'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Invitation'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '409':
          $ref: '#/components/responses/AlreadyExistsResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    get:
      tags:
        - invitations
      description: Get all invitations owned by the org
      x-required-permission: invitations.list
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 50
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvitationPagination'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /invitations/{id}:
    x-internal: true
    get:
      tags:
        - invitations
      description: Get an invitation's detail by the invitation id
      x-required-permission: invitations.get
      parameters:
        - in: path
          required: true
          name: id
          schema:
            type: integer
            format: uint64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Invitation'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - invitations
      description: delete an invitation by id
      x-required-permission: invitations.delete
      parameters:
        - in: path
          required: true
          name: id
          schema:
            type: integer
            format: uint64
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /notifications:
    x-internal: true
    get:
      tags:
        - notifications
      description: Get all the notifications
      x-required-permission: notifications.list
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 50
        - in: query
          name: order
          schema:
            type: string
            enum:
              - createdAtAsc
              - createdAtDesc
            default: createdAtDesc
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationPagination'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /notifications/{id}:
    x-internal: true
    put:
      tags:
        - notifications
      description: update the status of notification
      x-required-permission: notifications.update
      parameters:
        - in: path
          required: true
          name: id
          schema:
            type: integer
            format: uint64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PutNotificationsStatusRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
      security:
        - BearerAuth: []
  /serviceAccounts:
    x-internal: true
    get:
      tags:
        - serviceAccounts
      description: List service accounts by org
      x-required-permission: serviceAccounts.list
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 50
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceAccountPagination'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    post:
      description: Create a service account
      x-required-permission: serviceAccounts.create
      tags:
        - serviceAccounts
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostServiceAccountRequestBody'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceAccount'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '409':
          $ref: '#/components/responses/AlreadyExistsResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /serviceAccounts/{id}:
    x-internal: true
    parameters:
      - in: path
        required: true
        name: id
        schema:
          type: string
          format: uuid
    get:
      tags:
        - serviceAccounts
      description: get service account by id
      x-required-permission: serviceAccounts.get
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceAccount'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    put:
      tags:
        - serviceAccounts
      description: Updated service account's description
      x-required-permission: serviceAccounts.update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PutServiceAccountRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - service-account
      description: Delete service account by id
      x-required-permission: serviceAccounts.delete
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /apiKeys:
    x-audit-nodata: true
    x-internal: true
    get:
      tags:
        - serviceAccounts
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
        - in: query
          name: principal
          required: true
          schema:
            type: string
            format: uuid
      description: List api keys in org by principal
      x-required-permission: apiKeys.list
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiKeyPagination'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    post:
      description: Create an api key
      x-required-permission: apiKeys.create
      tags:
        - serviceAccounts
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostApiKeyRequestBody'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiKey'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /apiKeys/{id}:
    x-audit-nodata: true
    x-internal: true
    parameters:
      - in: path
        required: true
        name: id
        schema:
          type: integer
          format: uint64
    get:
      tags:
        - serviceAccounts
      description: get api key by id
      x-required-permission: apiKeys.get
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiKey'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    put:
      tags:
        - serviceAccounts
      description: Updated api key's description
      x-required-permission: apiKeys.update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PutApiKeyRequestBody'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiKey'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - serviceAccounts
      description: Delete api key by id
      x-required-permission: apiKeys.delete
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /roles:
    x-internal: true
    get:
      tags:
        - roles
      description: List all available roles
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RolePagination'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /roleBindings:
    x-internal: true
    get:
      tags:
        - roles
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
        - in: query
          name: principal
          schema:
            type: string
            format: uuid
      description: List all role bindings in a org
      x-required-permission: roleBindings.list
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleBindingPagination'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    put:
      tags:
        - roles
      description: update role bindings for principal in a org
      x-required-permission: roleBindings.update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PutRoleBindingRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /orgs/{orgId}:
    x-internal: true
    parameters:
      - in: path
        required: true
        name: orgId
        schema:
          type: string
          format: uuid
    delete:
      tags:
        - orgs
      description: delete org by org id
      x-required-permissions: orgs.delete
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/FailedPreconditionResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    put:
      tags:
        - orgs
      description: update org's information by org id
      x-required-permissions: orgs.update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PutOrgRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    get:
      tags:
        - orgs
      description: Get org information by org id
      x-required-permissions: orgs.get
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Org'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /subscriptions:
    x-internal: true
    get:
      tags:
        - notifications
      description: List alert notification subscriptions in a org
      x-required-permission: subscriptions.list
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionArray'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    post:
      tags:
        - notifications
      description: create new alert subscription in a org
      x-required-permission: subscriptions.create
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostSubscriptionRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /subscriptions/{subId}:
    delete:
      tags:
        - notifications
      parameters:
        - in: path
          required: true
          name: subId
          schema:
            type: string
            format: uuid
      description: delete notification subscription in a org
      x-required-permission: subscriptions.delete
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /recipients:
    x-internal: true
    get:
      tags:
        - notifications
      description: List alert notification recipients in a org
      x-required-permission: recipients.list
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RecipientArray'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    post:
      tags:
        - subscriptions
      description: create new alert recipient in a org
      x-required-permission: recipients.create
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostRecipientRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /recipients/{recipientId}:
    delete:
      tags:
        - notifications
      parameters:
        - in: path
          required: true
          name: recipientId
          schema:
            type: string
            format: uuid
      description: delete notification recipient in a org
      x-required-permission: recipients.delete
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /recipients/{recipientId}/subscriptions:
    put:
      tags:
        - notifications
      parameters:
        - in: path
          required: true
          name: recipientId
          schema:
            type: string
            format: uuid
      description: put notification subscription for a recipient in a org
      x-required-permission: subscriptions.delete
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PutRecipientSubscriptionRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /alertTypes:
    x-internal: true
    get:
      tags:
        - notifications
        - alerts
      description: List all available alert types
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AlertTypeArray'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  /recipients/{recipientId}/test:
    x-internal: true
    post:
      tags:
        - notifications
        - alerts
      parameters:
        - in: path
          required: true
          name: recipientId
          schema:
            type: string
            format: uuid
      description: send a test message to recipient
      x-required-permission: recipients.sendTestNotification
      responses:
        '200':
          $ref: '#/components/responses/DefaultResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
components:
  schemas:
    AuthType:
      type: string
      enum:
        - google-oauth2
        - github
        - windowslive
        - local
        - sso
    User:
      type: object
      required:
        - id
        - username
        - email
        - authType
        - createdAt
        - resourceId
        - roles
      properties:
        id:
          type: integer
          format: uint64
        username:
          type: string
        email:
          type: string
        createdAt:
          type: string
          format: date-time
        lastLoginAt:
          type: string
          format: date-time
        authType:
          $ref: '#/components/schemas/AuthType'
        resourceId:
          type: string
          format: uuid
        roles:
          type: array
          items:
            type: string
    Page:
      type: object
      required:
        - limit
        - offset
      properties:
        limit:
          type: integer
          format: uint64
        offset:
          type: integer
          format: uint64
    Size:
      type: object
      required:
        - size
      properties:
        size:
          type: integer
          format: uint64
    Pagination:
      type: object
      allOf:
        - $ref: '#/components/schemas/Page'
        - $ref: '#/components/schemas/Size'
    UserArray:
      type: array
      items:
        $ref: '#/components/schemas/User'
    UserPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: '#/components/schemas/Pagination'
        - type: object
          required:
            - users
          properties:
            users:
              $ref: '#/components/schemas/UserArray'
    Invitation:
      type: object
      required:
        - id
        - email
        - orgId
        - name
        - createdAt
        - expiresAt
        - updatedAt
        - roleId
      properties:
        id:
          type: integer
          format: uint64
        name:
          type: string
        email:
          type: string
        orgId:
          type: string
        createdAt:
          type: string
          format: date-time
        expiresAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        roleId:
          type: string
          format: uuid
    InvitationArray:
      type: array
      items:
        $ref: '#/components/schemas/Invitation'
    InvitationPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: '#/components/schemas/Pagination'
        - type: object
          required:
            - invitations
          properties:
            invitations:
              $ref: '#/components/schemas/InvitationArray'
    CreateInvitationRequestBody:
      type: object
      required:
        - email
        - roleId
      properties:
        email:
          type: string
        roleId:
          type: string
          format: uuid
    NotificationSender:
      type: object
      required:
        - name
        - email
      properties:
        name:
          type: string
        email:
          type: string
    Notification:
      type: object
      required:
        - id
        - time
        - type
        - status
        - sender
        - title
        - content
      properties:
        id:
          type: integer
          format: uint64
        time:
          type: string
          format: date-time
        type:
          type: string
          enum:
            - Alert
            - Message
            - Notification
        status:
          type: string
          enum:
            - Deleted
            - Unread
            - Read
        sender:
          $ref: '#/components/schemas/NotificationSender'
        content:
          type: string
        title:
          type: string
    NotificationArray:
      type: array
      items:
        $ref: '#/components/schemas/Notification'
    NotificationPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: '#/components/schemas/Pagination'
        - type: object
          required:
            - notifications
            - unreadNum
          properties:
            notifications:
              $ref: '#/components/schemas/NotificationArray'
            unreadNum:
              type: integer
              format: uint64
    PutNotificationsStatusRequestBody:
      type: object
      required:
        - status
      properties:
        status:
          type: string
          enum:
            - Deleted
            - Unread
            - Read
    ServiceAccount:
      type: object
      required:
        - id
        - name
        - orgId
        - createdAt
        - updatedAt
        - description
        - apiKeyCount
        - roles
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        orgId:
          type: string
          format: uuid
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        description:
          type: string
        apiKeyCount:
          type: integer
          format: uint64
        roles:
          type: array
          items:
            type: string
          example:
            - OrganizationMember
            - OrganizationAdmin
    ServiceAccountArray:
      type: array
      items:
        $ref: '#/components/schemas/ServiceAccount'
    ServiceAccountPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: '#/components/schemas/Pagination'
        - type: object
          required:
            - serviceAccounts
          properties:
            serviceAccounts:
              $ref: '#/components/schemas/ServiceAccountArray'
    PostServiceAccountRequestBody:
      type: object
      required:
        - name
        - description
        - roleId
      properties:
        description:
          type: string
        name:
          type: string
        roleId:
          type: string
          format: uuid
    PutServiceAccountRequestBody:
      type: object
      required:
        - description
      properties:
        description:
          type: string
    ApiKey:
      type: object
      required:
        - id
        - principal
        - key
        - createdAt
        - updatedAt
        - description
        - secret
      properties:
        id:
          type: integer
          format: uint64
        principal:
          type: string
          format: uuid
        key:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        description:
          type: string
        secret:
          type: string
    ApiKeyArray:
      type: array
      items:
        $ref: '#/components/schemas/ApiKey'
    ApiKeyPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: '#/components/schemas/Pagination'
        - type: object
          required:
            - apiKeys
          properties:
            apiKeys:
              $ref: '#/components/schemas/ApiKeyArray'
    PostApiKeyRequestBody:
      type: object
      required:
        - description
        - principal
      properties:
        description:
          type: string
        principal:
          type: string
          format: uuid
    PutApiKeyRequestBody:
      type: object
      required:
        - description
      properties:
        description:
          type: string
    Role:
      type: object
      required:
        - id
        - name
        - description
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        description:
          type: string
    RoleArray:
      type: array
      items:
        $ref: '#/components/schemas/Role'
    RolePagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: '#/components/schemas/Pagination'
        - type: object
          required:
            - roles
          properties:
            roles:
              $ref: '#/components/schemas/RoleArray'
    RoleBinding:
      type: object
      required:
        - principal
        - roleId
        - roleName
      properties:
        principal:
          type: string
          format: uuid
        roleName:
          type: string
        roleId:
          type: string
          format: uuid
        principalType:
          type: string
    RoleBindingArray:
      type: array
      items:
        $ref: '#/components/schemas/RoleBinding'
    RoleBindingPagination:
      type: object
      allOf:
        - type: object
          properties:
            pagination:
              $ref: '#/components/schemas/Pagination'
        - type: object
          required:
            - roleBindings
          properties:
            roleBindings:
              $ref: '#/components/schemas/RoleBindingArray'
    PutRoleBindingRequestBody:
      type: object
      required:
        - principal
        - roleIds
        - principalType
      properties:
        principal:
          type: string
          format: uuid
        roleIds:
          type: array
          items:
            type: string
            format: uuid
        principalType:
          type: string
    Org:
      type: object
      required:
        - orgId
        - name
        - createdAt
        - updatedAt
      properties:
        orgId:
          type: string
          format: uuid
        name:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    PutOrgRequestBody:
      type: object
      required:
        - name
      properties:
        name:
          type: string
    AlertSeverity:
      type: string
      enum:
        - critical
        - warning
    Subscription:
      type: object
      required:
        - id
        - recipientId
        - severity
      properties:
        id:
          type: string
          format: uuid
        recipientId:
          type: string
          format: uuid
        severity:
          $ref: '#/components/schemas/AlertSeverity'
    SubscriptionArray:
      type: array
      items:
        $ref: '#/components/schemas/Subscription'
    PostSubscriptionRequestBody:
      type: object
      required:
        - recipientId
        - alertLevel
      properties:
        alertLevel:
          type: string
        recipientId:
          type: string
          format: uuid
    RecipientType:
      type: string
      enum:
        - email
        - slack
        - user
    EmailRecipientConfig:
      type: object
      required:
        - type
        - email
      properties:
        type:
          $ref: '#/components/schemas/RecipientType'
        email:
          type: string
    UserRecipientConfig:
      type: object
      required:
        - type
        - userId
      properties:
        type:
          $ref: '#/components/schemas/RecipientType'
        userId:
          type: string
          format: uuid
    SlackRecipientConfig:
      type: object
      required:
        - type
        - webhookURL
      properties:
        type:
          $ref: '#/components/schemas/RecipientType'
        webhookURL:
          type: string
    RecipientConfig:
      discriminator:
        propertyName: type
        mapping:
          email: '#/components/schemas/EmailRecipientConfig'
          user: '#/components/schemas/UserRecipientConfig'
          slack: '#/components/schemas/SlackRecipientConfig'
      oneOf:
        - $ref: '#/components/schemas/EmailRecipientConfig'
        - $ref: '#/components/schemas/UserRecipientConfig'
        - $ref: '#/components/schemas/SlackRecipientConfig'
    Recipient:
      type: object
      required:
        - id
        - config
        - orgId
        - createdAt
        - updatedAt
      properties:
        id:
          type: string
          format: uuid
        config:
          $ref: '#/components/schemas/RecipientConfig'
        orgId:
          type: string
          format: uuid
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    RecipientArray:
      type: array
      items:
        $ref: '#/components/schemas/Recipient'
    PostRecipientRequestBody:
      type: object
      required:
        - config
      properties:
        config:
          $ref: '#/components/schemas/RecipientConfig'
    PutRecipientSubscriptionRequestBody:
      type: object
      required:
        - severities
      properties:
        severities:
          type: array
          items:
            $ref: '#/components/schemas/AlertSeverity'
    AlertType:
      type: object
      required:
        - name
        - severity
        - category
        - description
      properties:
        name:
          type: string
        severity:
          $ref: '#/components/schemas/AlertSeverity'
        category:
          type: string
        description:
          type: string
    AlertTypeArray:
      type: array
      items:
        $ref: '#/components/schemas/AlertType'
  responses:
    DefaultResponse:
      description: Default responses returning msg
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    BadRequestResponse:
      description: 400 Bad Request
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    AlreadyExistsResponse:
      description: 409 Bad Request
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    NotFoundResponse:
      description: 404 Not Found
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
    FailedPreconditionResponse:
      description: 400 Failed Precondition
      content:
        application/json:
          schema:
            type: object
            required:
              - msg
            properties:
              msg:
                type: string
              code:
                type: string
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    ApiKeyAuth:
      type: http
      scheme: basic
      description: Service account api key. Header format 'Basic base64(${key}:${secret})'
