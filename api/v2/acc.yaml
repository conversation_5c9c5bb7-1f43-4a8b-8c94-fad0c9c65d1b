openapi: 3.0.3

info:
  title: v2
  version: 2.0-alpha

servers:
  - url: /api/v2 # baseURL for kin-openapi openapifilter (should be consistent with spec.GinServerOptions)

tags:
  - name: users
    description: Access to user's profile
  - name: notifications
    description: Messages and alerts
  - name: invitations
    description: Access to org's invitations
  - name: serviceAccounts
    description: Access to org's service accounts and api keys
  - name: roles
    description: Access to org's roles and role bindings
  - name: orgs
    description: Access to org's information

paths:
  /users/{id}:
    x-internal: true
    delete:
      tags:
        - users
      description: delete user in org by id using jwt
      x-required-permission: users.delete
      parameters:
        - in: path
          required: true
          name: id
          schema:
            type: string
            format: uuid
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    get:
      tags:
        - users
      description: get user in org by id using jwt
      x-required-permission: users.get
      parameters:
        - in: path
          required: true
          name: id
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/User"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /users:
    x-internal: true
    get:
      tags:
        - users
      description: Get users owned by org
      x-required-permission: users.list
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 50
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/UserPagination"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /invitations:
    x-internal: true
    post:
      tags:
        - invitations
      description: Invite new user into target organization.
      x-required-permission: invitations.create
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/CreateInvitationRequestBody"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/Invitation"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "409":
          $ref: "response.yaml#/components/responses/AlreadyExistsResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    get:
      tags:
        - invitations
      description: Get all invitations owned by the org
      x-required-permission: invitations.list
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 50
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/InvitationPagination"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /invitations/{id}:
    x-internal: true
    get:
      tags:
        - invitations
      description: Get an invitation's detail by the invitation id
      x-required-permission: invitations.get
      parameters:
        - in: path
          required: true
          name: id
          schema:
            type: integer
            format: uint64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/Invitation"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    delete:
      tags:
        - invitations
      description: delete an invitation by id
      x-required-permission: invitations.delete
      parameters:
        - in: path
          required: true
          name: id
          schema:
            type: integer
            format: uint64
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /notifications:
    x-internal: true
    get:
      tags:
        - notifications
      description: Get all the notifications
      x-required-permission: notifications.list
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 50
        - in: query
          name: order
          schema:
            type: string
            enum:
              - createdAtAsc
              - createdAtDesc
            default: createdAtDesc
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/NotificationPagination"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /notifications/{id}:
    x-internal: true
    put:
      tags:
        - notifications
      description: update the status of notification
      x-required-permission: notifications.update
      parameters:
        - in: path
          required: true
          name: id
          schema:
            type: integer
            format: uint64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PutNotificationsStatusRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
      security:
        - BearerAuth: []

  /serviceAccounts:
    x-internal: true
    get:
      tags:
        - serviceAccounts
      description: List service accounts by org
      x-required-permission: serviceAccounts.list
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 50
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/ServiceAccountPagination"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    post:
      description: Create a service account
      x-required-permission: serviceAccounts.create
      tags:
        - serviceAccounts
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PostServiceAccountRequestBody"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/ServiceAccount"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "409":
          $ref: "response.yaml#/components/responses/AlreadyExistsResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /serviceAccounts/{id}:
    x-internal: true
    parameters:
      - in: path
        required: true
        name: id
        schema:
          type: string
          format: uuid
    get:
      tags:
        - serviceAccounts
      description: get service account by id
      x-required-permission: serviceAccounts.get
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/ServiceAccount"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    put:
      tags:
        - serviceAccounts
      description: Updated service account's description
      x-required-permission: serviceAccounts.update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PutServiceAccountRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    delete:
      tags:
        - service-account
      description: Delete service account by id
      x-required-permission: serviceAccounts.delete
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /apiKeys:
    x-audit-nodata: true
    x-internal: true
    get:
      tags:
        - serviceAccounts
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
        - in: query
          name: principal
          required: true
          schema:
            type: string
            format: uuid
      description: List api keys in org by principal
      x-required-permission: apiKeys.list
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/ApiKeyPagination"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    post:
      description: Create an api key
      x-required-permission: apiKeys.create
      tags:
        - serviceAccounts
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PostApiKeyRequestBody"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/ApiKey"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /apiKeys/{id}:
    x-audit-nodata: true
    x-internal: true
    parameters:
      - in: path
        required: true
        name: id
        schema:
          type: integer
          format: uint64
    get:
      tags:
        - serviceAccounts
      description: get api key by id
      x-required-permission: apiKeys.get
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/ApiKey"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    put:
      tags:
        - serviceAccounts
      description: Updated api key's description
      x-required-permission: apiKeys.update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PutApiKeyRequestBody"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/ApiKey"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    delete:
      tags:
        - serviceAccounts
      description: Delete api key by id
      x-required-permission: apiKeys.delete
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /roles:
    x-internal: true
    get:
      tags:
        - roles
      description: List all available roles
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/RolePagination"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
  
  /roleBindings:
    x-internal: true
    get:
      tags:
        - roles
      parameters:
        - in: query
          name: offset
          schema:
            type: integer
            format: uint64
            default: 0
        - in: query
          name: limit
          schema:
            type: integer
            format: uint64
            default: 10
        - in: query
          name: principal
          schema:
            type: string
            format: uuid
      description: List all role bindings in a org
      x-required-permission: roleBindings.list
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/RoleBindingPagination"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    put:
      tags:
        - roles
      description: update role bindings for principal in a org
      x-required-permission: roleBindings.update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PutRoleBindingRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /orgs/{orgId}:
    x-internal: true
    parameters:
      - in: path
        required: true
        name: orgId
        schema: 
          type: string
          format: uuid
    delete:
      tags:
        - orgs
      description: delete org by org id
      x-required-permissions: orgs.delete
      responses: 
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/FailedPreconditionResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

    put:
      tags:
        - orgs
      description: update org's information by org id
      x-required-permissions: orgs.update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PutOrgRequestBody"
      responses:
        "200": 
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []  

    get:
      tags:
        - orgs
      description: Get org information by org id
      x-required-permissions: orgs.get
      responses: 
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/Org"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

# TODO: CLOUD-3860
  /subscriptions:
    x-internal: true
    get:
      tags:
        - notifications
      description: List alert notification subscriptions in a org
      x-required-permission: subscriptions.list
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/SubscriptionArray"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    post:
      tags:
        - notifications
      description: create new alert subscription in a org
      x-required-permission: subscriptions.create
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PostSubscriptionRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /subscriptions/{subId}:
    delete:
      tags:
        - notifications
      parameters:
        - in: path
          required: true
          name: subId
          schema:
            type: string
            format: uuid
      description: delete notification subscription in a org
      x-required-permission: subscriptions.delete
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /recipients:
    x-internal: true
    get:
      tags:
        - notifications
      description: List alert notification recipients in a org
      x-required-permission: recipients.list
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/RecipientArray"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []
    post:
      tags:
        - subscriptions
      description: create new alert recipient in a org
      x-required-permission: recipients.create
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PostRecipientRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /recipients/{recipientId}:
    delete:
      tags:
        - notifications
      parameters:
        - in: path
          required: true
          name: recipientId
          schema:
            type: string
            format: uuid
      description: delete notification recipient in a org
      x-required-permission: recipients.delete
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /recipients/{recipientId}/subscriptions:
    put:
      tags:
        - notifications
      parameters:
        - in: path
          required: true
          name: recipientId
          schema:
            type: string
            format: uuid
      description: put notification subscription for a recipient in a org
      x-required-permission: subscriptions.delete
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "acc_body.yaml#/components/schemas/PutRecipientSubscriptionRequestBody"
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
        "404":
          $ref: "response.yaml#/components/responses/NotFoundResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /alertTypes:
    x-internal: true
    get:
      tags:
        - notifications
        - alerts
      description: List all available alert types
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "acc_resource.yaml#/components/schemas/AlertTypeArray"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

  /recipients/{recipientId}/test:
    x-internal: true
    post:
      tags:
        - notifications
        - alerts
      parameters:
        - in: path
          required: true
          name: recipientId
          schema:
            type: string
            format: uuid
      description: send a test message to recipient
      x-required-permission: recipients.sendTestNotification
      responses:
        "200":
          $ref: "response.yaml#/components/responses/DefaultResponse"
        "400":
          $ref: "response.yaml#/components/responses/BadRequestResponse"
      security:
        - BearerAuth: []
        - ApiKeyAuth: []

components:
  schemas:
  responses:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    ApiKeyAuth:
      type: http
      scheme: basic
      description: Service account api key. Header format 'Basic base64(${key}:${secret})'
