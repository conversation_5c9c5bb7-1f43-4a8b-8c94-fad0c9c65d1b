output "vpc" {
  value = {
    resource_group_id = azurerm_resource_group.this.id
    vpc_id            = azurerm_virtual_network.this.id
    public_subnets = [
      for _, subnet in local.public_subnet : {
        id               = azurerm_subnet.this[subnet.subnet_name].id
        name             = azurerm_subnet.this[subnet.subnet_name].name
        address_prefixes = azurerm_subnet.this[subnet.subnet_name].address_prefixes
      }
    ],
    private_subnets = [
      for _, subnet in local.private_subnet : {
        id               = azurerm_subnet.this[subnet.subnet_name].id
        name             = azurerm_subnet.this[subnet.subnet_name].name
        address_prefixes = azurerm_subnet.this[subnet.subnet_name].address_prefixes
      }
    ],
    intra_subnet = {
      id               = azurerm_subnet.this[local.intra_subnet.subnet_name].id
      name             = azurerm_subnet.this[local.intra_subnet.subnet_name].name
      address_prefixes = azurerm_subnet.this[local.intra_subnet.subnet_name].address_prefixes
    }

    appgw_subnet = length(var.subnet_group_set.appgw_cidrs) > 0 ? {
      id               = azurerm_subnet.this[local.appgw_subnet[0].subnet_name].id
      name             = azurerm_subnet.this[local.appgw_subnet[0].subnet_name].name
      address_prefixes = azurerm_subnet.this[local.appgw_subnet[0].subnet_name].address_prefixes
      } : {
      id               = ""
      name             = ""
      address_prefixes = []
    }
    node_subnets = [
      for _, subnet in local.node_subnet : {
        id               = azurerm_subnet.this[subnet.subnet_name].id
        name             = azurerm_subnet.this[subnet.subnet_name].name
        address_prefixes = azurerm_subnet.this[subnet.subnet_name].address_prefixes
      }
    ],
    pod_subnets = [
      for _, subnet in local.pod_subnet : {
        id               = azurerm_subnet.this[subnet.subnet_name].id
        name             = azurerm_subnet.this[subnet.subnet_name].name
        address_prefixes = azurerm_subnet.this[subnet.subnet_name].address_prefixes
      }
    ],
    service_cidr = var.subnet_group_set.service_cidr
    private_link_svc_subnet = {
      id               = azurerm_subnet.this[local.private_link_svc_subnet.subnet_name].id
      name             = azurerm_subnet.this[local.private_link_svc_subnet.subnet_name].name
      address_prefixes = azurerm_subnet.this[local.private_link_svc_subnet.subnet_name].address_prefixes
    }
    delegate_subnets = {
      for subnet_key, subnet_data in azurerm_subnet.delegates : subnet_key => {
        id               = subnet_data.id
        name             = subnet_data.name
        address_prefixes = subnet_data.address_prefixes
      }
    }
    azure_svc_privatelink_subnet = {
      id               = azurerm_subnet.this[local.azure_svc_privatelink_subnet_name].id
      name             = azurerm_subnet.this[local.azure_svc_privatelink_subnet_name].name
      address_prefixes = azurerm_subnet.this[local.azure_svc_privatelink_subnet_name].address_prefixes
    }
    byoc_privatelink_subnets = [
      for _, subnet in local.byoc_privatelink_subnet : {
        id               = azurerm_subnet.this[subnet.subnet_name].id
        name             = azurerm_subnet.this[subnet.subnet_name].name
        address_prefixes = azurerm_subnet.this[subnet.subnet_name].address_prefixes
      }
    ],
    tenant_privatelink_subnets = [
      for _, subnet in local.tenant_privatelink_subnet : {
        id               = azurerm_subnet.this[subnet.subnet_name].id
        name             = azurerm_subnet.this[subnet.subnet_name].name
        address_prefixes = azurerm_subnet.this[subnet.subnet_name].address_prefixes
      }
    ],

    nat_gateway_public_ips = [
      for _, zone in var.zones : {
        name = azurerm_public_ip.nat[zone].name,
        ip   = azurerm_public_ip.nat[zone].ip_address,
      }
    ],

    tracking_ranges = {
      private_link = flatten([for _, subnet in local.tenant_privatelink_subnet :
        azurerm_subnet.this[subnet.subnet_name].address_prefixes
      ])
      vpc            = var.address_space
      object_storage = azurerm_subnet.this[local.azure_svc_privatelink_subnet_name].address_prefixes
      byoc_subnet = flatten([for _, subnet in local.byoc_privatelink_subnet :
        azurerm_subnet.this[subnet.subnet_name].address_prefixes
      ])
    }

    internal_zone_ranges = {
      for _, zone in var.zones :
      "internal_zone_${zone}" => flatten([
        for _, subnet in concat(
          local.public_subnet,
          local.private_subnet,
          local.node_subnet,
          local.pod_subnet,
          [
            local.intra_subnet,
            local.azure_svc_privatelink_subnet,
            local.private_link_svc_subnet,
          ],
          local.byoc_privatelink_subnet,
          local.tenant_privatelink_subnet,
          local.appgw_subnet,
        ) : subnet.address_prefixes if endswith(subnet.subnet_name, zone)
      ])
    }
  }
}
