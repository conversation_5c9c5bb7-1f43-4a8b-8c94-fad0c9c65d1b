output "vpc" {
  value = {
    vpc_id                 = aws_vpc.this.id
    vpc_name               = local.vpc_name
    azs                    = var.azs
    vpc_cidr_block         = aws_vpc.this.cidr_block
    public_subnets         = [for s in local.public_subnets : aws_subnet.public[s].id]
    database_subnets       = [for s in local.database_subnets : aws_subnet.database[s].id]
    cluster_subnets        = [for s in local.cluster_subnets : aws_subnet.cluster[s].id]
    private_subnets        = [for s in local.private_subnets : aws_subnet.private[s].id]
    node_subnets           = [for s in local.node_subnets : aws_subnet.node[s].id]
    intra_subnets          = [for s in local.intra_subnets : aws_subnet.intra[s].id]
    byoc_vpce_subnets      = [for s in local.byoc_vpce_subnets : aws_subnet.byoc_vpce[s].id]
    tenant_vpce_subnets    = [for s, _ in var.tenant_vpce_subnet_group_set : aws_subnet.tenant_vpce[s].id]
    database_subnet_group  = aws_db_subnet_group.database.id
    nat_gateway_public_ips = [for az in var.azs : aws_nat_gateway.this[az].public_ip]

    tracking_ranges = {
      private_link   = flatten([for s, _ in var.tenant_vpce_subnet_group_set : s])
      vpc            = [aws_vpc.this.cidr_block]
      object_storage = data.aws_prefix_list.private_s3.cidr_blocks
      byoc_subnet    = [for s in local.byoc_vpce_subnets : s]
    }

    internal_zone_ranges = {
      for _, az in var.azs :
      "internal_zone_${az}" => flatten([
        [for subnet in local.public_subnets : subnet if local.public_subnet_to_az[subnet] == az],
        [for subnet in local.private_subnets : subnet if local.private_subnet_to_az[subnet] == az],
        [for subnet in local.cluster_subnets : subnet if local.cluster_subnet_to_az[subnet] == az],
        [for subnet in local.node_subnets : subnet if local.node_subnet_to_az[subnet] == az],
        [for subnet in local.intra_subnets : subnet if local.intra_subnet_to_az[subnet] == az],
        [for subnet in local.database_subnets : subnet if local.database_subnet_to_az[subnet] == az],
      ])
    }
  }
}
