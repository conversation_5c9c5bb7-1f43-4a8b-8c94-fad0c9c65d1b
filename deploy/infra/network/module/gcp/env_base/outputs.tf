output "vpc" {
  value = {
    vpc_name      = module.vpc.network_name
    vpc_self_link = module.vpc.network_self_link
    vpc_id        = module.vpc.network_id

    public_subnets = [
      for s in local.public_subnets : {
        name      = s.subnet_name
        cidr      = s.subnet_ip
        self_link = local.self_link_map[s.subnet_name]
      }
    ]
    private_subnets = [
      for s in local.private_subnets : {
        name      = s.subnet_name
        cidr      = s.subnet_ip
        self_link = local.self_link_map[s.subnet_name]
      }
    ]
    intra_subnets = [
      for s in local.intra_subnets : {
        name      = s.subnet_name
        cidr      = s.subnet_ip
        self_link = local.self_link_map[s.subnet_name]
      }
    ]
    node_subnet = {
      name      = local.node_subnet.subnet_name
      cidr      = local.node_subnet.subnet_ip
      self_link = local.self_link_map[local.node_subnet.subnet_name]
    }
    service_secondary_subnet = {
      name = "${local.service_secondary_subnet.range_name}"
      cidr = local.service_secondary_subnet.ip_cidr_range
    }
    pod_secondary_subnets = [
      for s in local.pod_secondary_subnets : {
        name = s.range_name
        cidr = s.ip_cidr_range
      }
    ]
    control_plane_subnet = var.control_plane_subnet

    tenant_psc_consumer_subnets = [
      for s in local.tenant_psc_consumer_subnets : {
        name      = s.subnet_name
        cidr      = s.subnet_ip
        self_link = local.self_link_map[s.subnet_name]
      }
    ]

    byoc_psc_consumer_subnets = [
      for s in local.byoc_psc_consumer_subnets : {
        name      = s.subnet_name
        cidr      = s.subnet_ip
        self_link = local.self_link_map[s.subnet_name]
      }
    ]

    private_service_subnets = {
      for s in local.private_service_subnets : local.private_service_subnet_name_to_service[s.subnet_name] => {
        name      = s.subnet_name
        cidr      = s.subnet_ip
        self_link = local.self_link_map[s.subnet_name]
      }
    }

    psa_ranges = {
      for name, range in var.private_service_access_ranges : name => {
        ip_range_name = google_compute_global_address.psa_ip[name].name
        address       = google_compute_global_address.psa_ip[name].address
        prefix_length = google_compute_global_address.psa_ip[name].prefix_length
      }
    }

    tracking_ranges = {
      private_link = [for s in local.tenant_psc_consumer_subnets : s.subnet_ip]
      vpc = [
        # https://cloud.google.com/vpc/docs/subnets#valid-ranges
        "10.0.0.0/8",
        "**********/12",
        "***********/16",
        "**********/10",
        "*********/24",
        "*********/24",
        "************/24",
        "***********/24",
        "***********/24",
        "**********/15",
        "240.0.0.0/4",
      ]
      object_storage = ["${var.private_service_connect_ip}/32"]
      byoc_subnet    = [for s in local.byoc_psc_consumer_subnets : s.subnet_ip]
    }
    // Note: The internal_zone_ranges are not used in GCP, but keep the structure for consistency.
    internal_zone_ranges = {
      "internal_zone_any" = ["0.0.0.0/0"]
    }
  }
}
