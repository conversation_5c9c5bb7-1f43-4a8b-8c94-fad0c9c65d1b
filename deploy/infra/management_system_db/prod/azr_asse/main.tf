locals {
  location = "southeastasia"


  remote_workspace                   = "prod-azr-asse-mgmt-db"
  remote_network_workspace           = "prod-azr-asse-mgmt-network"
  remote_management_system_workspace = "prod-azr-asse-mgmt"


  env = "prod"

  msdb_replica_zones          = ["1"]
  msdb_high_availability_mode = "SameZone"
}
module "main" {
  source = "../../modules/azr/env_base_prod"

  location = local.location


  remote_workspace                   = local.remote_workspace
  remote_network_workspace           = local.remote_network_workspace
  remote_management_system_workspace = local.remote_management_system_workspace

  msdb_replica_zones          = local.msdb_replica_zones
  msdb_high_availability_mode = local.msdb_high_availability_mode

  harness_api_key    = var.harness_api_key
  harness_account_id = var.harness_account_id
  harness_project_id = var.harness_project_id
  harness_org_id     = var.harness_org_id

  risingwave_license_key                     = var.risingwave_license_key
  risingwave_standard_tier_license_key       = var.risingwave_standard_tier_license_key
  risingwave_advanced_tier_license_key       = var.risingwave_advanced_tier_license_key
  risingwave_secret_store_key_encryption_key = var.risingwave_secret_store_key_encryption_key
}
