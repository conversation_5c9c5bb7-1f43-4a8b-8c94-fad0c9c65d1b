locals {
  region = "europe-north1"

  remote_workspace            = "prod-gcp-euno1-mgmt-db"
  management_system_workspace = "prod-gcp-euno1-mgmt"
  network_workspace           = "prod-gcp-euno1-mgmt-network"

  msdb_primary_zone  = "europe-north1-b"
  msdb_replica_zones = ["europe-north1-a"]
}

module "main" {
  source = "../../modules/gcp/env_base_prod"
  region = local.region

  remote_workspace            = local.remote_workspace
  management_system_workspace = local.management_system_workspace
  network_workspace           = local.network_workspace

  msdb_region        = local.region
  msdb_primary_zone  = local.msdb_primary_zone
  msdb_replica_zones = local.msdb_replica_zones

  harness_api_key    = var.harness_api_key
  harness_account_id = var.harness_account_id
  harness_project_id = var.harness_project_id
  harness_org_id     = var.harness_org_id

  risingwave_license_key                     = var.risingwave_license_key
  risingwave_standard_tier_license_key       = var.risingwave_standard_tier_license_key
  risingwave_advanced_tier_license_key       = var.risingwave_advanced_tier_license_key
  risingwave_secret_store_key_encryption_key = var.risingwave_secret_store_key_encryption_key
}
