variable "harness_api_key" {
  type      = string
  sensitive = true
}

variable "harness_account_id" {
  type = string
}

variable "harness_project_id" {
  type = string
}

variable "harness_org_id" {
  type = string
}

variable "risingwave_license_key" {
  description = "License key used by cloud tenants."
  type        = string
  sensitive   = true
}

variable "risingwave_standard_tier_license_key" {
  description = "License key used by cloud tenants with standard tier."
  type        = string
  sensitive   = true
}

variable "risingwave_advanced_tier_license_key" {
  description = "License key used by cloud tenants with advanced tier."
  type        = string
  sensitive   = true
}

variable "risingwave_secret_store_key_encryption_key" {
  description = "key used for rw secret store key encryption/decryption."
  type        = string
  sensitive   = true
}
