locals {
  tf_cloud_organization = "risingwave-cloud"
  location              = "eastus2"

  role_arn = "arn:aws:iam::023339134545:role/deploy"

  remote_workspace                   = "test-azr-use2-mgmt-db"
  remote_network_workspace           = "test-azr-use2-mgmt-network"
  remote_management_system_workspace = "test-azr-use2-mgmt"
  remote_config_center_workspace     = "test-useast2-config-center"

  env                         = "test"
  msdb_replica_zones          = ["1"]
  msdb_high_availability_mode = "SameZone"
  msdb_sku_override           = "GP_Standard_D2ds_v4"

  version_tag = "20240222"
  project     = "risingwave-cloud"
}
module "main" {
  source = "../../modules/azr/env_base"

  location              = local.location
  tf_cloud_organization = local.tf_cloud_organization

  role_arn = local.role_arn

  remote_workspace                   = local.remote_workspace
  remote_network_workspace           = local.remote_network_workspace
  remote_config_center_workspace     = local.remote_config_center_workspace
  remote_management_system_workspace = local.remote_management_system_workspace

  msdb_replica_zones          = local.msdb_replica_zones
  msdb_high_availability_mode = local.msdb_high_availability_mode
  msdb_sku_override           = local.msdb_sku_override

  harness_api_key    = var.harness_api_key
  harness_account_id = var.harness_account_id
  harness_project_id = var.harness_project_id
  harness_org_id     = var.harness_org_id

  risingwave_license_key                     = var.risingwave_license_key
  risingwave_standard_tier_license_key       = var.risingwave_standard_tier_license_key
  risingwave_advanced_tier_license_key       = var.risingwave_advanced_tier_license_key
  risingwave_secret_store_key_encryption_key = var.risingwave_secret_store_key_encryption_key

  tags = {
    stack       = local.remote_workspace
    versiontag  = local.version_tag
    project     = local.project
    environment = local.env
  }
}
