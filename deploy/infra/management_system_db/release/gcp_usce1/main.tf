locals {
  version_tag = "20230503"
  project_id  = "rwcrls"
  region      = "us-central1"

  role_arn = "arn:aws:iam::023339134545:role/deploy"

  remote_workspace            = "rls-gcp-usce1-mgmt-db"
  management_system_workspace = "rls-gcp-usce1-mgmt"
  config_center_workspace     = "rls-apse1-config-center"
  network_workspace           = "rls-gcp-usce1-mgmt-network"

  msdb_primary_zone  = "us-central1-b"
  msdb_replica_zones = ["us-central1-a"]

  project = "risingwave-cloud"
  env     = "rls"
}

module "main" {
  tf_cloud_organization = "risingwave-cloud"
  source                = "../../modules/gcp/env_base"

  project_id = local.project_id
  region     = local.region
  env        = local.env

  role_arn = local.role_arn

  remote_workspace            = local.remote_workspace
  management_system_workspace = local.management_system_workspace
  config_center_workspace     = local.config_center_workspace
  network_workspace           = local.network_workspace

  msdb_region        = local.region
  msdb_primary_zone  = local.msdb_primary_zone
  msdb_replica_zones = local.msdb_replica_zones

  harness_api_key    = var.harness_api_key
  harness_account_id = var.harness_account_id
  harness_project_id = var.harness_project_id
  harness_org_id     = var.harness_org_id

  risingwave_license_key                     = var.risingwave_license_key
  risingwave_standard_tier_license_key       = var.risingwave_standard_tier_license_key
  risingwave_advanced_tier_license_key       = var.risingwave_advanced_tier_license_key
  risingwave_secret_store_key_encryption_key = var.risingwave_secret_store_key_encryption_key

  labels = {
    stack       = local.remote_workspace
    versiontag  = local.version_tag
    project     = local.project
    environment = local.env
  }
}
