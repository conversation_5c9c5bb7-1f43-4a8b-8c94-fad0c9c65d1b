locals {
  mgmt_kv_path = "rwc_mgmt_kv_${replace(local.cluster_name, "-", "_")}"
}

resource "vault_mount" "mgmt_kv" {
  path = local.mgmt_kv_path
  type = "kv-v2"
  options = {
    version = "2"
  }
  description = "This is the kv secret engine used by mgmt service."
}

resource "vault_kv_secret_v2" "msdb_credentials" {
  mount = local.mgmt_kv_path
  name  = "db_credential"
  data_json = jsonencode(
    {
      "user" : var.msdb_user,
      "password" : random_password.msdb_master_password.result,
      "host" : azurerm_postgresql_flexible_server.msdb.fqdn,
      "port" : var.msdb_port,
      "db" : var.db_name,
      "sslmode" : "require"
    }
  )
  depends_on = [
    vault_mount.mgmt_kv
  ]
}

resource "vault_kv_secret_v2" "app_tokens" {
  mount = local.mgmt_kv_path
  name  = "app_tokens"
  data_json = jsonencode(
    {
      "risingwavelicensekey" : var.risingwave_license_key,
      "risingwavestandardtierlicensekey" : var.risingwave_standard_tier_license_key,
      "risingwaveadvancedtierlicensekey" : var.risingwave_advanced_tier_license_key,
      "risingwavesecretstorekeyencryptionkey" : var.risingwave_secret_store_key_encryption_key
    }
  )
  depends_on = [
    vault_mount.mgmt_kv
  ]
}
