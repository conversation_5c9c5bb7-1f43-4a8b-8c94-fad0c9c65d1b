variable "remote_workspace" {
  description = "name of the remote workspace"
  type        = string
}

variable "remote_network_workspace" {
  description = "name of the remote network workspace"
  type        = string
}

variable "role_arn" {
  type        = string
  description = "The role arn of the AWS cluster."
}

variable "remote_config_center_workspace" {
  description = "The config center workspace name of the associated config center"
  type        = string
}

variable "remote_management_system_workspace" {
  description = "The management workspace name of the associated management system"
  type        = string
}

variable "tf_cloud_organization" {
  description = "cloud organization"
  type        = string
}

variable "location" {
  description = "location of the workspace"
  type        = string
}

variable "msdb_user" {
  description = "user for the db"
  type        = string
  default     = "rwc_root"
}

variable "msdb_port" {
  description = "port for the db"
  type        = number
  default     = 5432
}

variable "db_name" {
  description = "name of the database"
  type        = string
  default     = "msdb"
}

variable "backup_retention_period" {
  description = "The number of days to retain msdb backups for"
  type        = number
  default     = 7
}

variable "tags" {
  description = "The tags used to tag the resources."
  type        = map(string)
  default     = {}
}

variable "msdb_sku_override" {
  description = "The replica zones of the management system database"
  type        = string
  default     = ""
}

variable "msdb_replica_zones" {
  description = "The replica zones of the management system database"
  type        = list(string)
}

variable "msdb_high_availability_mode" {
  type    = string
  default = "ZoneRedundant"

  validation {
    condition     = contains(["ZoneRedundant", "SameZone", ""], var.msdb_high_availability_mode)
    error_message = "Valid values for var: msdb_high_availability_mode are ('ZoneRedundant', 'SameZone', ' )."
  }
}

variable "harness_api_key" {
  type      = string
  sensitive = true
}

variable "harness_account_id" {
  type = string
}

variable "harness_org_id" {
  description = "ID of the Harness org."
  type        = string
}

variable "harness_project_id" {
  description = "ID of the Harness project"
  type        = string
}

variable "risingwave_license_key" {
  description = "License key used by cloud tenants."
  type        = string
  sensitive   = true
}

variable "risingwave_standard_tier_license_key" {
  description = "License key used by cloud tenants with standard tier."
  type        = string
  sensitive   = true
}

variable "risingwave_advanced_tier_license_key" {
  description = "License key used by cloud tenants with advance tier."
  type        = string
  sensitive   = true
}

variable "risingwave_secret_store_key_encryption_key" {
  description = "key used for rw secret store key encryption/decryption."
  type        = string
  sensitive   = true
}
