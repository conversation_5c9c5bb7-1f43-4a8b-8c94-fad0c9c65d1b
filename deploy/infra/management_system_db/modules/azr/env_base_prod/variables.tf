variable "remote_workspace" {
  description = "name of the remote workspace"
  type        = string
}

variable "remote_network_workspace" {
  description = "name of the remote network workspace"
  type        = string
}

variable "remote_management_system_workspace" {
  description = "The management workspace name of the associated management system"
  type        = string
}

variable "location" {
  description = "location of the workspace"
  type        = string
}

variable "msdb_sku_override" {
  description = "The replica zones of the management system database"
  type        = string
  default     = ""
}

variable "msdb_replica_zones" {
  description = "The replica zones of the management system database"
  type        = list(string)
}

variable "msdb_high_availability_mode" {
  type    = string
  default = "ZoneRedundant"

  validation {
    condition     = contains(["ZoneRedundant", "SameZone", ""], var.msdb_high_availability_mode)
    error_message = "Valid values for var: msdb_high_availability_mode are ('ZoneRedundant', 'SameZone', ' )."
  }
}

variable "harness_api_key" {
  type      = string
  sensitive = true
}

variable "harness_account_id" {
  type = string
}

variable "harness_org_id" {
  description = "ID of the Harness org."
  type        = string
}

variable "harness_project_id" {
  description = "ID of the Harness project"
  type        = string
}

variable "risingwave_license_key" {
  description = "License key used by cloud tenants."
  type        = string
  sensitive   = true
}

variable "risingwave_standard_tier_license_key" {
  description = "License key used by cloud tenants with standard tier."
  type        = string
  sensitive   = true
}

variable "risingwave_advanced_tier_license_key" {
  description = "License key used by cloud tenants with advanced tier."
  type        = string
  sensitive   = true
}

variable "risingwave_secret_store_key_encryption_key" {
  description = "key used for rw secret store key encryption/decryption."
  type        = string
  sensitive   = true
}
