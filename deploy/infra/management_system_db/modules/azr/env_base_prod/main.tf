module "canary_config" {
  source = "../../../../shared_modules/canary_config"
}

locals {
  tf_cloud_organization = "risingwave-cloud"

  role_arn = "arn:aws:iam::600598779918:role/deploy"

  remote_config_center_workspace = "prod-useast2-config-center"


  is_canary = contains(module.canary_config.canary_regions.azr, var.location)
  env       = local.is_canary ? "canary" : "prod"

  version_tag = "20250303"
  project     = "risingwave-cloud"
}
module "main" {
  source = "../env_base"

  location              = var.location
  tf_cloud_organization = local.tf_cloud_organization

  role_arn = local.role_arn

  remote_workspace                   = var.remote_workspace
  remote_network_workspace           = var.remote_network_workspace
  remote_management_system_workspace = var.remote_management_system_workspace
  remote_config_center_workspace     = local.remote_config_center_workspace

  msdb_replica_zones          = var.msdb_replica_zones
  msdb_high_availability_mode = var.msdb_high_availability_mode
  msdb_sku_override           = var.msdb_sku_override

  harness_api_key    = var.harness_api_key
  harness_account_id = var.harness_account_id
  harness_project_id = var.harness_project_id
  harness_org_id     = var.harness_org_id

  risingwave_license_key                     = var.risingwave_license_key
  risingwave_standard_tier_license_key       = var.risingwave_standard_tier_license_key
  risingwave_advanced_tier_license_key       = var.risingwave_advanced_tier_license_key
  risingwave_secret_store_key_encryption_key = var.risingwave_secret_store_key_encryption_key

  tags = {
    stack       = var.remote_workspace
    versiontag  = local.version_tag
    project     = local.project
    environment = local.env
  }
}
