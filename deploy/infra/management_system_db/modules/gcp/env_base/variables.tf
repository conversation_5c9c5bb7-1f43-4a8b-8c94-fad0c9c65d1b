variable "region" {
  type        = string
  description = "The region of the GCP cluster."
}

variable "env" {
  type = string

  validation {
    condition     = contains(["dev", "test", "rls", "canary", "prod"], var.env)
    error_message = "Allowed values for input_parameter are \"dev\", \"test\", \"rls\", \"canary\", or \"prod\"."
  }
}

variable "project_id" {
  description = "The project id of the GCP cluster."
  type        = string
}

variable "role_arn" {
  type        = string
  description = "The role arn of the AWS cluster."
}

variable "remote_workspace" {
  description = "Name of the workspace (dogfood, dev, etc..)."
  type        = string
}

variable "tf_cloud_organization" {
  description = "Name of Terraform Cloud Organization"
  type        = string
}

variable "config_center_workspace" {
  description = "The config center workspace name of the associated config center"
  type        = string
}

variable "network_workspace" {
  description = "The network workspace name of the associated network"
  type        = string
}

variable "db_name" {
  description = "The name of the database"
  type        = string
  default     = "msdb"
}

variable "msdb_port" {
  description = "The port of the management system database. Default is 5432."
  type        = number
  default     = 5432
}

variable "msdb_user" {
  description = "The user of the management system database"
  type        = string
  default     = "rwc_root"
}

variable "msdb_region" {
  description = "The region of the management system database"
  type        = string
}

variable "msdb_primary_zone" {
  description = "The primary zone of the management system database"
  type        = string
}

variable "msdb_replica_zones" {
  description = "The replica zones of the management system database"
  type        = list(string)
}

# https://cloud.google.com/compute/docs/labeling-resources
variable "labels" {
  description = "The labels used to tag the resources."
  type        = map(string)
}

variable "backup_retention_period" {
  description = "The number of days to retain msdb backups for"
  type        = number
  default     = 7
}

variable "management_system_workspace" {
  description = "The terraform workspace name of the associated management system"
  type        = string
}

variable "harness_api_key" {
  type      = string
  sensitive = true
}

variable "harness_account_id" {
  type = string
}

variable "harness_org_id" {
  description = "ID of the Harness org."
  type        = string
}

variable "harness_project_id" {
  description = "ID of the Harness project"
  type        = string
}

variable "enable_firewall_log" {
  type    = bool
  default = false
}

variable "risingwave_license_key" {
  description = "License key used by cloud tenants."
  type        = string
  sensitive   = true
}

variable "risingwave_standard_tier_license_key" {
  description = "License key used by cloud tenants with standard tier."
  type        = string
  sensitive   = true
}

variable "risingwave_advanced_tier_license_key" {
  description = "License key used by cloud tenants with advance tier."
  type        = string
  sensitive   = true
}

variable "risingwave_secret_store_key_encryption_key" {
  description = "key used for rw secret store key encryption/decryption."
  type        = string
  sensitive   = true
}
