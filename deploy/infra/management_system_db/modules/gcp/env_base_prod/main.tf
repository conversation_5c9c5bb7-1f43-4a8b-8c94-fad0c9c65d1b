module "canary_config" {
  source = "../../../../shared_modules/canary_config"
}

locals {
  version_tag = "20230620"
  project_id  = "rwcprod"

  role_arn = "arn:aws:iam::600598779918:role/deploy"

  config_center_workspace = "prod-useast2-config-center"

  project   = "risingwave-cloud"
  is_canary = contains(module.canary_config.canary_regions.gcp, var.region)
  env       = local.is_canary ? "canary" : "prod"
}

module "main" {
  tf_cloud_organization = "risingwave-cloud"
  source                = "../env_base"

  project_id = local.project_id
  region     = var.region
  env        = local.env

  role_arn = local.role_arn

  remote_workspace            = var.remote_workspace
  management_system_workspace = var.management_system_workspace
  config_center_workspace     = local.config_center_workspace
  network_workspace           = var.network_workspace

  msdb_region        = var.region
  msdb_primary_zone  = var.msdb_primary_zone
  msdb_replica_zones = var.msdb_replica_zones

  harness_api_key    = var.harness_api_key
  harness_account_id = var.harness_account_id
  harness_project_id = var.harness_project_id
  harness_org_id     = var.harness_org_id

  risingwave_license_key                     = var.risingwave_license_key
  risingwave_standard_tier_license_key       = var.risingwave_standard_tier_license_key
  risingwave_advanced_tier_license_key       = var.risingwave_advanced_tier_license_key
  risingwave_secret_store_key_encryption_key = var.risingwave_secret_store_key_encryption_key

  labels = {
    stack       = var.remote_workspace
    versiontag  = local.version_tag
    project     = local.project
    environment = local.env
    sprinto     = "prod"
  }
}
