variable "region" {
  description = "Region code of this cloud environment. Please refer to https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/using-regions-availability-zones.html#local-zones-available"
  type        = string
}

variable "msdb_region" {
  description = "The region of the management system database"
  type        = string
}

variable "msdb_primary_zone" {
  description = "The primary zone of the management system database"
  type        = string
}

variable "msdb_replica_zones" {
  description = "The replica zones of the management system database"
  type        = list(string)
}

variable "remote_workspace" {
  description = "Name of the workspace (dogfood, dev, etc..)."
  type        = string
}

variable "management_system_workspace" {
  description = "the terraform workspace name of the associated management system config"
  type        = string
}

variable "network_workspace" {
  description = "the terraform workspace name of the associated network config"
  type        = string
}

variable "harness_api_key" {
  type      = string
  sensitive = true
}

variable "harness_account_id" {
  type = string
}

variable "harness_org_id" {
  description = "ID of the Harness org."
  type        = string
}

variable "harness_project_id" {
  description = "ID of the Harness project"
  type        = string
}

variable "risingwave_license_key" {
  description = "License key used by cloud tenants."
  type        = string
  sensitive   = true
}

variable "risingwave_standard_tier_license_key" {
  description = "License key used by cloud tenants with standard tier."
  type        = string
  sensitive   = true
}

variable "risingwave_advanced_tier_license_key" {
  description = "License key used by cloud tenants with advanced tier."
  type        = string
  sensitive   = true
}

variable "risingwave_secret_store_key_encryption_key" {
  description = "key used for rw secret store key encryption/decryption."
  type        = string
  sensitive   = true
}
