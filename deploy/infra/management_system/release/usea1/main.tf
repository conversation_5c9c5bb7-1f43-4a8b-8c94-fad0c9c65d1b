locals {
  tf_cloud_organization = "risingwave-cloud"

  project = "risingwave-cloud"
  env     = "rls"

  region                         = "us-east-1"
  assume_role_arn                = "arn:aws:iam::************:role/deploy"
  assume_role_arn_network        = "arn:aws:iam::************:role/Route53FullAccess"
  remote_workspace               = "rls-usea1-mgmt"
  account_service_workspace      = "rls-apse1-acc"
  account_service_k8s_workspace  = "rls-apse1-acc-k8s"
  network_workspace              = "rls-usea1-mgmt-network"
  remote_config_center_workspace = "rls-apse1-config-center"
  asdb_remote_workspace          = "rls-apse1-acc-db"


  domain_name     = "risingwave-cloud.xyz"
  route53_zone_id = "Z024090513037FY7ZQVF0"

  version_tag = "*********"

  api_service_mode = "debug"

  harness_delegate_name = "rls-usea1-mgmt-harness-delegate"
}

module "mgmt_service" {
  source                  = "../../modules/aws/env_base"
  tf_cloud_organization   = local.tf_cloud_organization
  remote_workspace        = local.remote_workspace
  env                     = local.env
  region                  = local.region
  assume_role_arn         = local.assume_role_arn
  assume_role_arn_network = local.assume_role_arn_network
  tags = {
    Stack       = local.remote_workspace
    VersionTag  = local.version_tag
    Project     = local.project
    Environment = local.env
  }

  domain_name     = local.domain_name
  route53_zone_id = local.route53_zone_id

  account_service_workspace     = local.account_service_workspace
  account_service_k8s_workspace = local.account_service_k8s_workspace
  network_workspace             = local.network_workspace
  config_center_workspace       = local.remote_config_center_workspace
  asdb_remote_workspace         = local.asdb_remote_workspace

  api_service_mode = local.api_service_mode

  harness_delegate_name  = local.harness_delegate_name
  harness_account_id     = var.harness_account_id
  harness_delegate_token = var.harness_delegate_token
  harness_api_key        = var.harness_api_key
  harness_org_id         = var.harness_org_id
  harness_project_id     = var.harness_project_id

  eks_monitoring_ng_specs = []

  enable_intra_subnets = true
  use_node_subnets     = true
  use_cluster_subnets  = true

  msdb_major_engine_version = "16"

  # backups are required in order to create a replica
  backup_retention_period        = 7
  enable_msdb_replica            = true
  enable_forwarding_msdb_replica = true

  risingwave_license_key                     = var.risingwave_license_key
  risingwave_standard_tier_license_key       = var.risingwave_standard_tier_license_key
  risingwave_advanced_tier_license_key       = var.risingwave_advanced_tier_license_key
  risingwave_secret_store_key_encryption_key = var.risingwave_secret_store_key_encryption_key
}
