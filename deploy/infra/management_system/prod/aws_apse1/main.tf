locals {
  region                         = "ap-southeast-1"
  remote_workspace               = "prod-aws-apse1-mgmt"
  network_workspace              = "prod-aws-apse1-mgmt-network"
  remote_config_center_workspace = "prod-useast2-config-center"

  harness_delegate_name = "prod-aws-apse1-mgmt-harness-delegate"
}

module "mgmt_service_prod" {
  source           = "../../modules/aws/env_base_prod"
  remote_workspace = local.remote_workspace
  region           = local.region

  network_workspace       = local.network_workspace
  config_center_workspace = local.remote_config_center_workspace

  harness_delegate_name  = local.harness_delegate_name
  harness_account_id     = var.harness_account_id
  harness_delegate_token = var.harness_delegate_token
  harness_api_key        = var.harness_api_key
  harness_org_id         = var.harness_org_id
  harness_project_id     = var.harness_project_id

  enable_intra_subnets      = true
  msdb_major_engine_version = "17"

  risingwave_license_key                     = var.risingwave_license_key
  risingwave_standard_tier_license_key       = var.risingwave_standard_tier_license_key
  risingwave_advanced_tier_license_key       = var.risingwave_advanced_tier_license_key
  risingwave_secret_store_key_encryption_key = var.risingwave_secret_store_key_encryption_key
}

moved {
  from = module.mgmt_service
  to   = module.mgmt_service_prod.module.mgmt_service
}
