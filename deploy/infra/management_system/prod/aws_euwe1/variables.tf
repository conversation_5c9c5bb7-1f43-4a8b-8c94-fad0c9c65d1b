variable "harness_account_id" {
  description = "ID of the <PERSON><PERSON><PERSON> account."
  type        = string
}

variable "harness_delegate_token" {
  type        = string
  description = "Delegate token used by the bastion delegate."
  sensitive   = true
}

variable "harness_api_key" {
  type        = string
  description = "harness API key"
  sensitive   = true
}

variable "harness_org_id" {
  type        = string
  description = "harness organization id"
  sensitive   = true
}

variable "harness_project_id" {
  type        = string
  description = "harness project id"
  sensitive   = true
}

variable "risingwave_license_key" {
  description = "License key used by cloud tenants."
  type        = string
  sensitive   = true
}

variable "risingwave_advanced_tier_license_key" {
  description = "License key used by cloud tenants for advanced tier."
  type        = string
  sensitive   = true

}

variable "risingwave_standard_tier_license_key" {
  description = "License key used by cloud tenants for standard tier."
  type        = string
  sensitive   = true
}

variable "risingwave_secret_store_key_encryption_key" {
  description = "key used for rw secret store key encryption/decryption."
  type        = string
  sensitive   = true
}
