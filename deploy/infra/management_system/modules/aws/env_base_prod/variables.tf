variable "region" {
  description = "Region code of this cloud environment. Please refer to https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/using-regions-availability-zones.html#local-zones-available"
  type        = string
}

variable "remote_workspace" {
  description = "Name of the workspace (dogfood, dev, etc..)."
  type        = string
}

variable "network_workspace" {
  description = "the terraform workspace name of the associated network config"
  type        = string
}

variable "config_center_workspace" {
  description = "the config center workspace name of the associated config center"
  type        = string
}

variable "harness_delegate_name" {
  type        = string
  description = "Name of the delegate deployed in the bastion."
  sensitive   = true
}

variable "harness_account_id" {
  description = "ID of the Harness account."
  type        = string
}

variable "harness_delegate_token" {
  type        = string
  description = "Delegate token used by the bastion delegate."
  sensitive   = true
}

variable "harness_api_key" {
  type        = string
  description = "harness API key"
  sensitive   = true
}

variable "harness_org_id" {
  type        = string
  description = "harness organization id"
  sensitive   = true
}

variable "harness_project_id" {
  type        = string
  description = "harness project id"
  sensitive   = true
}

variable "use_node_subnets" {
  type        = bool
  default     = true
  description = "If true, use node subnet for EKS nodes"
}

variable "use_cluster_subnets" {
  type        = bool
  default     = true
  description = "If true, use cluster subnet for EKS API server"
}

variable "is_region_ready" {
  description = "Whether the region is ready to be used. Setting this will enable the region to be accessed from the frontend."
  type        = bool
  default     = true
}

variable "is_region_byoc_only" {
  description = "Whether the region is BYOC only, i.e. no hosted tenant cluster."
  type        = bool
  default     = false
}

variable "enable_intra_subnets" {
  type    = bool
  default = false
}

variable "msdb_major_engine_version" {
  type     = string
  nullable = true
  default  = null
}

variable "risingwave_license_key" {
  description = "License key used by cloud tenants."
  type        = string
  sensitive   = true
}

variable "risingwave_standard_tier_license_key" {
  description = "License key used by cloud tenants with standard tier."
  type        = string
  sensitive   = true
}

variable "risingwave_advanced_tier_license_key" {
  description = "License key used by cloud tenants with advanced tier."
  type        = string
  sensitive   = true
}

variable "risingwave_secret_store_key_encryption_key" {
  description = "key used for rw secret store key encryption/decryption."
  type        = string
  sensitive   = true
}
