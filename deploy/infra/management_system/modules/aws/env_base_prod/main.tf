module "canary_config" {
  source = "../../../../shared_modules/canary_config"
}

locals {
  is_canary = contains(module.canary_config.canary_regions.aws, var.region)

  tf_cloud_organization = "risingwave-cloud"

  project = "risingwave-cloud"
  env     = local.is_canary ? "canary" : "prod"

  assume_role_arn         = "arn:aws:iam::************:role/deploy"
  assume_role_arn_network = "arn:aws:iam::************:role/Route53FullAccess"

  domain_name     = "risingwave.cloud"
  route53_zone_id = "Z05732591W9T36S1GHFS"

  account_service_workspace     = "canary-useast2-acc"
  account_service_k8s_workspace = "canary-useast2-acc-k8s"
  asdb_remote_workspace         = "canary-useast2-acc-db"

  version_tag = "********"
  sns_tag     = "${var.region}-mgmt-cloud-topic"

  api_service_mode = "debug"

  backup_retention_period = 7
}

module "mgmt_service" {
  source                  = "../env_base"
  tf_cloud_organization   = local.tf_cloud_organization
  remote_workspace        = var.remote_workspace
  env                     = local.env
  region                  = var.region
  assume_role_arn         = local.assume_role_arn
  assume_role_arn_network = local.assume_role_arn_network
  tags = {
    Stack       = var.remote_workspace
    VersionTag  = local.version_tag
    Project     = local.project
    Environment = local.env
    sprinto     = "prod"
    SNS         = local.sns_tag
  }

  domain_name     = local.domain_name
  route53_zone_id = local.route53_zone_id

  account_service_workspace     = local.account_service_workspace
  account_service_k8s_workspace = local.account_service_k8s_workspace
  network_workspace             = var.network_workspace
  config_center_workspace       = var.config_center_workspace
  asdb_remote_workspace         = local.asdb_remote_workspace

  api_service_mode = local.api_service_mode

  backup_retention_period = local.backup_retention_period

  harness_delegate_name  = var.harness_delegate_name
  harness_account_id     = var.harness_account_id
  harness_delegate_token = var.harness_delegate_token
  harness_api_key        = var.harness_api_key
  harness_org_id         = var.harness_org_id
  harness_project_id     = var.harness_project_id

  eks_monitoring_ng_specs = []

  enable_alerting = true

  enable_msdb_replica            = true
  enable_forwarding_msdb_replica = true

  use_node_subnets    = false
  use_cluster_subnets = false

  is_region_ready     = var.is_region_ready
  is_region_byoc_only = var.is_region_byoc_only

  enable_intra_subnets = var.enable_intra_subnets

  msdb_major_engine_version = var.msdb_major_engine_version

  risingwave_license_key                     = var.risingwave_license_key
  risingwave_standard_tier_license_key       = var.risingwave_standard_tier_license_key
  risingwave_advanced_tier_license_key       = var.risingwave_advanced_tier_license_key
  risingwave_secret_store_key_encryption_key = var.risingwave_secret_store_key_encryption_key
}
