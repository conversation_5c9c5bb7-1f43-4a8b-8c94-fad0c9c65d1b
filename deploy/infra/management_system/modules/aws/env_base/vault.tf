locals {
  mgmt_kv_path = "rwc_mgmt_kv_${replace(module.basic_cluster.basic_cluster.cluster_id, "-", "_")}"
}

resource "vault_mount" "mgmt_kv" {
  path = local.mgmt_kv_path
  type = "kv-v2"
  options = {
    version = "2"
  }
  description = "This is the kv secret engine used by mgmt service."
}

resource "vault_kv_secret_v2" "msdb_credentials" {
  mount = local.mgmt_kv_path
  name  = "db_credential"
  data_json = jsonencode(
    {
      "user" : module.db.db_instance_username,
      "password" : random_password.msdb_master_password.result,
      "host" : module.db.db_instance_address,
      "port" : module.db.db_instance_port,
      "db" : module.db.db_instance_name,
      "sslmode" : "require"
    }
  )
  depends_on = [
    module.db,
    vault_mount.mgmt_kv
  ]
}

# The JSON data should match the Go struct:
# https://github.com/risingwavelabs/risingwave-cloud/blob/e9bbf40df1d3a8de2720c5a72464d8f7225365ee/account/config/config.go#L51-L56
resource "vault_kv_secret_v2" "region" {
  mount = local.account_kv_path
  # The prefix "region_" is used for regexp match. See: https://external-secrets.io/v0.7.2/provider/hashicorp-vault/#multiple-nested-values
  name = "region_aws_${replace(var.region, "-", "_")}"
  data_json = jsonencode(
    {
      "platform" : "aws",
      "region" : var.region,
      "isregionready" : var.is_region_ready,
      "isbyoconly" : var.is_region_byoc_only
      "userurl" : "https://${aws_route53_record.apisvc.fqdn}/api/v1",
      "userurlv2" : "https://${aws_route53_record.apisvc.fqdn}/api/v2",
      "adminurl" : "https://${aws_route53_record.api_service_admin.fqdn}/api/v1",
      "pgweburl" : "wss://${local.pgweb_dns}/ws"
      "isbyoconly" : var.is_region_byoc_only
    }
  )
}

resource "vault_kv_secret_v2" "app_tokens" {
  mount = local.mgmt_kv_path
  name  = "app_tokens"
  data_json = jsonencode(
    {
      "risingwavelicensekey" : var.risingwave_license_key,
      "risingwavestandardtierlicensekey" : var.risingwave_standard_tier_license_key,
      "risingwaveadvancedtierlicensekey" : var.risingwave_advanced_tier_license_key,
      "risingwavesecretstorekeyencryptionkey" : var.risingwave_secret_store_key_encryption_key
    }
  )
  depends_on = [
    vault_mount.mgmt_kv
  ]
}

moved {
  from = vault_kv_secret_v2.region[0]
  to   = vault_kv_secret_v2.region
}
