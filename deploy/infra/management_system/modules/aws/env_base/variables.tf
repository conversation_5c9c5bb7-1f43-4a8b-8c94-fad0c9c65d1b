variable "region" {
  description = "Region code of this cloud environment. Please refer to https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/using-regions-availability-zones.html#local-zones-available"
  type        = string
}

variable "env" {
  type = string

  validation {
    condition     = contains(["dev", "test", "rls", "canary", "prod"], var.env)
    error_message = "Allowed values for input_parameter are \"dev\", \"test\", \"rls\", \"canary\", or \"prod\"."
  }
}

variable "assume_role_arn" {
  description = "ARN of the deply role AWS provider runs as."
  type        = string
}

variable "assume_role_arn_network" {
  description = "ARN of the deploy role AWS provider runs as for DNS resources provisioning"
  type        = string
}

# This variable is just a prefix for the names of resources.
# It is not used as the name of workspace anymore.
variable "remote_workspace" {
  description = "Name of the workspace (dogfood, dev, etc..)."
  type        = string
}

variable "tf_cloud_organization" {
  description = "Name of Terraform Cloud Organization"
  type        = string
}

variable "tags" {
  description = "Tags to be applied to the this set of managed resources."
  type        = map(string)
  default     = {}
}

variable "route53_zone_id" {
  description = "The ID of the Route53 zone."
  type        = string
}

variable "domain_name" {
  description = "Domain name of the cluster."
  type        = string
}

variable "account_service_workspace" {
  description = "the terraform workspace name of the associated account service"
  type        = string
}

variable "account_service_k8s_workspace" {
  description = "the terraform workspace name of the associated account service"
  type        = string
}

variable "network_workspace" {
  description = "the terraform workspace name of the associated network config"
  type        = string
}

variable "config_center_workspace" {
  description = "the config center workspace name of the associated config center"
  type        = string
}

variable "api_service_mode" {
  description = "mode variables for gin framework: debug, release, test"
  type        = string
}

# TODO(junfeng): remove when issue #1023 is closed:
# https://github.com/risingwavelabs/risingwave-cloud/issues/1023
variable "increase_public_subnet_ips" {
  description = "If true, double the public subnets so more RW instances can be allocated."
  type        = bool
  default     = false
}

variable "harness_account_id" {
  description = "ID of the Harness account."
  type        = string
}

variable "harness_delegate_token" {
  type        = string
  description = "Delegate token used by the bastion delegate."
  sensitive   = true
}

variable "harness_delegate_name" {
  type        = string
  description = "Name of the delegate deployed in the bastion."
  sensitive   = true
}

variable "harness_api_key" {
  type        = string
  description = "harness API key"
  sensitive   = true
}

variable "harness_org_id" {
  type        = string
  description = "harness organization id"
  sensitive   = true
}

variable "harness_project_id" {
  type        = string
  description = "harness project id"
  sensitive   = true
}

variable "backup_retention_period" {
  type        = number
  description = "The days to retain backups for. Set to 0 to disable backups"
  default     = 0
}

variable "asdb_remote_workspace" {
  description = "Account service database remote workspace name."
  type        = string
}


variable "enable_alerting" {
  type        = bool
  default     = false
  description = "If true, enable AWS alerting on the CloudWatch"
}

variable "use_node_subnets" {
  type        = bool
  default     = true
  description = "If true, use node subnet for EKS nodes"
}

variable "use_cluster_subnets" {
  type        = bool
  default     = true
  description = "If true, use cluster subnet for EKS API server"
}

variable "eks_monitoring_ng_specs" {
  description = "Specifications of the EKS managed on demand node group of monitoring nodes."
  type = list(object({
    name         = string
    inst_type    = list(string)
    max_size     = number
    desired_size = number
  }))
  default = []
}

variable "enable_msdb_replica" {
  type        = bool
  default     = false
  description = "If true, enable a read-only msdb replica"
}

variable "enable_forwarding_msdb_replica" {
  type        = bool
  default     = false
  description = "Enable forwarding msdb read-only replica"
}

variable "use_gp3" {
  type        = bool
  default     = false
  description = "If true, use gp3 SSD for EKS nodes"
}

variable "is_region_ready" {
  description = "Whether the region is ready to be used. Setting this will enable the region to be accessed from the frontend."
  type        = bool
  default     = true
}

variable "is_region_byoc_only" {
  description = "Whether the region is BYOC only, i.e. no hosted tenant cluster."
  type        = bool
  default     = false
}

variable "enable_intra_subnets" {
  type    = bool
  default = false
}

variable "msdb_major_engine_version" {
  type     = string
  nullable = true
  default  = null
}

# RW Diagnosis Report
variable "rw_diag_bucket_expiration_days" {
  type        = number
  description = "Number of days after which bucket objects will be deleted."
  default     = 7
}

variable "risingwave_license_key" {
  description = "License key used by cloud tenants."
  type        = string
  sensitive   = true
}


variable "risingwave_standard_tier_license_key" {
  description = "License key used by cloud tenants with standard tier."
  type        = string
  sensitive   = true
}

variable "risingwave_advanced_tier_license_key" {
  description = "License key used by cloud tenants with advance tier."
  type        = string
  sensitive   = true
}

variable "risingwave_secret_store_key_encryption_key" {
  description = "key used for rw secret store key encryption/decryption."
  type        = string
  sensitive   = true
}
