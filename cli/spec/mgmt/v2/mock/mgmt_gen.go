// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/risingwave-cloud/cli/spec/mgmt/v2 (interfaces: ClientInterface)
//
// Generated by this command:
//
//	mockgen-v0.5.0 -package=mock -destination=spec/mgmt/v2/mock/mgmt_gen.go github.com/risingwavelabs/risingwave-cloud/cli/spec/mgmt/v2 ClientInterface
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	io "io"
	http "net/http"
	reflect "reflect"

	uuid "github.com/google/uuid"
	mgmt "github.com/risingwavelabs/risingwave-cloud/cli/spec/mgmt/v2"
	gomock "go.uber.org/mock/gomock"
)

// MockClientInterface is a mock of ClientInterface interface.
type MockClientInterface struct {
	ctrl     *gomock.Controller
	recorder *MockClientInterfaceMockRecorder
	isgomock struct{}
}

// MockClientInterfaceMockRecorder is the mock recorder for MockClientInterface.
type MockClientInterfaceMockRecorder struct {
	mock *MockClientInterface
}

// NewMockClientInterface creates a new mock instance.
func NewMockClientInterface(ctrl *gomock.Controller) *MockClientInterface {
	mock := &MockClientInterface{ctrl: ctrl}
	mock.recorder = &MockClientInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClientInterface) EXPECT() *MockClientInterfaceMockRecorder {
	return m.recorder
}

// DeleteByocClustersName mocks base method.
func (m *MockClientInterface) DeleteByocClustersName(ctx context.Context, name string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, name}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteByocClustersName", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteByocClustersName indicates an expected call of DeleteByocClustersName.
func (mr *MockClientInterfaceMockRecorder) DeleteByocClustersName(ctx, name any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, name}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByocClustersName", reflect.TypeOf((*MockClientInterface)(nil).DeleteByocClustersName), varargs...)
}

// DeleteTenantsNsId mocks base method.
func (m *MockClientInterface) DeleteTenantsNsId(ctx context.Context, nsId uuid.UUID, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteTenantsNsId", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTenantsNsId indicates an expected call of DeleteTenantsNsId.
func (mr *MockClientInterfaceMockRecorder) DeleteTenantsNsId(ctx, nsId any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTenantsNsId", reflect.TypeOf((*MockClientInterface)(nil).DeleteTenantsNsId), varargs...)
}

// DeleteTenantsNsIdBackupsSnapshotId mocks base method.
func (m *MockClientInterface) DeleteTenantsNsIdBackupsSnapshotId(ctx context.Context, nsId, snapshotId uuid.UUID, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, snapshotId}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteTenantsNsIdBackupsSnapshotId", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTenantsNsIdBackupsSnapshotId indicates an expected call of DeleteTenantsNsIdBackupsSnapshotId.
func (mr *MockClientInterfaceMockRecorder) DeleteTenantsNsIdBackupsSnapshotId(ctx, nsId, snapshotId any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, snapshotId}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTenantsNsIdBackupsSnapshotId", reflect.TypeOf((*MockClientInterface)(nil).DeleteTenantsNsIdBackupsSnapshotId), varargs...)
}

// DeleteTenantsNsIdDatabaseUsersDbuserName mocks base method.
func (m *MockClientInterface) DeleteTenantsNsIdDatabaseUsersDbuserName(ctx context.Context, nsId uuid.UUID, dbuserName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, dbuserName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteTenantsNsIdDatabaseUsersDbuserName", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTenantsNsIdDatabaseUsersDbuserName indicates an expected call of DeleteTenantsNsIdDatabaseUsersDbuserName.
func (mr *MockClientInterfaceMockRecorder) DeleteTenantsNsIdDatabaseUsersDbuserName(ctx, nsId, dbuserName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, dbuserName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTenantsNsIdDatabaseUsersDbuserName", reflect.TypeOf((*MockClientInterface)(nil).DeleteTenantsNsIdDatabaseUsersDbuserName), varargs...)
}

// DeleteTenantsNsIdDatabasesDatabaseNameMatviewsMatViewName mocks base method.
func (m *MockClientInterface) DeleteTenantsNsIdDatabasesDatabaseNameMatviewsMatViewName(ctx context.Context, nsId uuid.UUID, databaseName, matViewName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, matViewName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteTenantsNsIdDatabasesDatabaseNameMatviewsMatViewName", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTenantsNsIdDatabasesDatabaseNameMatviewsMatViewName indicates an expected call of DeleteTenantsNsIdDatabasesDatabaseNameMatviewsMatViewName.
func (mr *MockClientInterfaceMockRecorder) DeleteTenantsNsIdDatabasesDatabaseNameMatviewsMatViewName(ctx, nsId, databaseName, matViewName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, matViewName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTenantsNsIdDatabasesDatabaseNameMatviewsMatViewName", reflect.TypeOf((*MockClientInterface)(nil).DeleteTenantsNsIdDatabasesDatabaseNameMatviewsMatViewName), varargs...)
}

// DeleteTenantsNsIdDatabasesDatabaseNameRelationsRelName mocks base method.
func (m *MockClientInterface) DeleteTenantsNsIdDatabasesDatabaseNameRelationsRelName(ctx context.Context, nsId uuid.UUID, databaseName, relName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, relName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteTenantsNsIdDatabasesDatabaseNameRelationsRelName", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTenantsNsIdDatabasesDatabaseNameRelationsRelName indicates an expected call of DeleteTenantsNsIdDatabasesDatabaseNameRelationsRelName.
func (mr *MockClientInterfaceMockRecorder) DeleteTenantsNsIdDatabasesDatabaseNameRelationsRelName(ctx, nsId, databaseName, relName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, relName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTenantsNsIdDatabasesDatabaseNameRelationsRelName", reflect.TypeOf((*MockClientInterface)(nil).DeleteTenantsNsIdDatabasesDatabaseNameRelationsRelName), varargs...)
}

// DeleteTenantsNsIdDatabasesDatabaseNameSecretsName mocks base method.
func (m *MockClientInterface) DeleteTenantsNsIdDatabasesDatabaseNameSecretsName(ctx context.Context, nsId uuid.UUID, databaseName, name string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, name}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteTenantsNsIdDatabasesDatabaseNameSecretsName", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTenantsNsIdDatabasesDatabaseNameSecretsName indicates an expected call of DeleteTenantsNsIdDatabasesDatabaseNameSecretsName.
func (mr *MockClientInterfaceMockRecorder) DeleteTenantsNsIdDatabasesDatabaseNameSecretsName(ctx, nsId, databaseName, name any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, name}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTenantsNsIdDatabasesDatabaseNameSecretsName", reflect.TypeOf((*MockClientInterface)(nil).DeleteTenantsNsIdDatabasesDatabaseNameSecretsName), varargs...)
}

// DeleteTenantsNsIdDatabasesDatabaseNameSinksSinkName mocks base method.
func (m *MockClientInterface) DeleteTenantsNsIdDatabasesDatabaseNameSinksSinkName(ctx context.Context, nsId uuid.UUID, databaseName, sinkName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, sinkName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteTenantsNsIdDatabasesDatabaseNameSinksSinkName", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTenantsNsIdDatabasesDatabaseNameSinksSinkName indicates an expected call of DeleteTenantsNsIdDatabasesDatabaseNameSinksSinkName.
func (mr *MockClientInterfaceMockRecorder) DeleteTenantsNsIdDatabasesDatabaseNameSinksSinkName(ctx, nsId, databaseName, sinkName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, sinkName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTenantsNsIdDatabasesDatabaseNameSinksSinkName", reflect.TypeOf((*MockClientInterface)(nil).DeleteTenantsNsIdDatabasesDatabaseNameSinksSinkName), varargs...)
}

// DeleteTenantsNsIdDatabasesDatabaseNameSourcesSourceName mocks base method.
func (m *MockClientInterface) DeleteTenantsNsIdDatabasesDatabaseNameSourcesSourceName(ctx context.Context, nsId uuid.UUID, databaseName, sourceName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, sourceName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteTenantsNsIdDatabasesDatabaseNameSourcesSourceName", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTenantsNsIdDatabasesDatabaseNameSourcesSourceName indicates an expected call of DeleteTenantsNsIdDatabasesDatabaseNameSourcesSourceName.
func (mr *MockClientInterfaceMockRecorder) DeleteTenantsNsIdDatabasesDatabaseNameSourcesSourceName(ctx, nsId, databaseName, sourceName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, sourceName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTenantsNsIdDatabasesDatabaseNameSourcesSourceName", reflect.TypeOf((*MockClientInterface)(nil).DeleteTenantsNsIdDatabasesDatabaseNameSourcesSourceName), varargs...)
}

// DeleteTenantsNsIdDatabasesDatabaseNameTablesTableName mocks base method.
func (m *MockClientInterface) DeleteTenantsNsIdDatabasesDatabaseNameTablesTableName(ctx context.Context, nsId uuid.UUID, databaseName, tableName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, tableName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteTenantsNsIdDatabasesDatabaseNameTablesTableName", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTenantsNsIdDatabasesDatabaseNameTablesTableName indicates an expected call of DeleteTenantsNsIdDatabasesDatabaseNameTablesTableName.
func (mr *MockClientInterfaceMockRecorder) DeleteTenantsNsIdDatabasesDatabaseNameTablesTableName(ctx, nsId, databaseName, tableName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, tableName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTenantsNsIdDatabasesDatabaseNameTablesTableName", reflect.TypeOf((*MockClientInterface)(nil).DeleteTenantsNsIdDatabasesDatabaseNameTablesTableName), varargs...)
}

// DeleteTenantsNsIdExtensionsIcebergCompaction mocks base method.
func (m *MockClientInterface) DeleteTenantsNsIdExtensionsIcebergCompaction(ctx context.Context, nsId uuid.UUID, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteTenantsNsIdExtensionsIcebergCompaction", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTenantsNsIdExtensionsIcebergCompaction indicates an expected call of DeleteTenantsNsIdExtensionsIcebergCompaction.
func (mr *MockClientInterfaceMockRecorder) DeleteTenantsNsIdExtensionsIcebergCompaction(ctx, nsId any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTenantsNsIdExtensionsIcebergCompaction", reflect.TypeOf((*MockClientInterface)(nil).DeleteTenantsNsIdExtensionsIcebergCompaction), varargs...)
}

// DeleteTenantsNsIdPrivatelinksPrivateLinkId mocks base method.
func (m *MockClientInterface) DeleteTenantsNsIdPrivatelinksPrivateLinkId(ctx context.Context, nsId, privateLinkId uuid.UUID, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, privateLinkId}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteTenantsNsIdPrivatelinksPrivateLinkId", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTenantsNsIdPrivatelinksPrivateLinkId indicates an expected call of DeleteTenantsNsIdPrivatelinksPrivateLinkId.
func (mr *MockClientInterfaceMockRecorder) DeleteTenantsNsIdPrivatelinksPrivateLinkId(ctx, nsId, privateLinkId any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, privateLinkId}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTenantsNsIdPrivatelinksPrivateLinkId", reflect.TypeOf((*MockClientInterface)(nil).DeleteTenantsNsIdPrivatelinksPrivateLinkId), varargs...)
}

// DeleteTenantsNsIdResourceGroupsResourceGroup mocks base method.
func (m *MockClientInterface) DeleteTenantsNsIdResourceGroupsResourceGroup(ctx context.Context, nsId uuid.UUID, resourceGroup string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, resourceGroup}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteTenantsNsIdResourceGroupsResourceGroup", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTenantsNsIdResourceGroupsResourceGroup indicates an expected call of DeleteTenantsNsIdResourceGroupsResourceGroup.
func (mr *MockClientInterfaceMockRecorder) DeleteTenantsNsIdResourceGroupsResourceGroup(ctx, nsId, resourceGroup any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, resourceGroup}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTenantsNsIdResourceGroupsResourceGroup", reflect.TypeOf((*MockClientInterface)(nil).DeleteTenantsNsIdResourceGroupsResourceGroup), varargs...)
}

// GetByocClusters mocks base method.
func (m *MockClientInterface) GetByocClusters(ctx context.Context, params *mgmt.GetByocClustersParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByocClusters", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByocClusters indicates an expected call of GetByocClusters.
func (mr *MockClientInterfaceMockRecorder) GetByocClusters(ctx, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByocClusters", reflect.TypeOf((*MockClientInterface)(nil).GetByocClusters), varargs...)
}

// GetByocClustersName mocks base method.
func (m *MockClientInterface) GetByocClustersName(ctx context.Context, name string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, name}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByocClustersName", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByocClustersName indicates an expected call of GetByocClustersName.
func (mr *MockClientInterfaceMockRecorder) GetByocClustersName(ctx, name any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, name}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByocClustersName", reflect.TypeOf((*MockClientInterface)(nil).GetByocClustersName), varargs...)
}

// GetTenants mocks base method.
func (m *MockClientInterface) GetTenants(ctx context.Context, params *mgmt.GetTenantsParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenants", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenants indicates an expected call of GetTenants.
func (mr *MockClientInterfaceMockRecorder) GetTenants(ctx, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenants", reflect.TypeOf((*MockClientInterface)(nil).GetTenants), varargs...)
}

// GetTenantsNsId mocks base method.
func (m *MockClientInterface) GetTenantsNsId(ctx context.Context, nsId uuid.UUID, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsId", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsId indicates an expected call of GetTenantsNsId.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsId(ctx, nsId any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsId", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsId), varargs...)
}

// GetTenantsNsIdBackups mocks base method.
func (m *MockClientInterface) GetTenantsNsIdBackups(ctx context.Context, nsId uuid.UUID, params *mgmt.GetTenantsNsIdBackupsParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdBackups", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdBackups indicates an expected call of GetTenantsNsIdBackups.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdBackups(ctx, nsId, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdBackups", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdBackups), varargs...)
}

// GetTenantsNsIdCaCert mocks base method.
func (m *MockClientInterface) GetTenantsNsIdCaCert(ctx context.Context, nsId uuid.UUID, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdCaCert", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdCaCert indicates an expected call of GetTenantsNsIdCaCert.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdCaCert(ctx, nsId any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdCaCert", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdCaCert), varargs...)
}

// GetTenantsNsIdDatabaseUsers mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabaseUsers(ctx context.Context, nsId uuid.UUID, params *mgmt.GetTenantsNsIdDatabaseUsersParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabaseUsers", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabaseUsers indicates an expected call of GetTenantsNsIdDatabaseUsers.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabaseUsers(ctx, nsId, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabaseUsers", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabaseUsers), varargs...)
}

// GetTenantsNsIdDatabases mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabases(ctx context.Context, nsId uuid.UUID, params *mgmt.GetTenantsNsIdDatabasesParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabases", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabases indicates an expected call of GetTenantsNsIdDatabases.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabases(ctx, nsId, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabases", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabases), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameMatviews mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameMatviews(ctx context.Context, nsId uuid.UUID, databaseName string, params *mgmt.GetTenantsNsIdDatabasesDatabaseNameMatviewsParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameMatviews", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameMatviews indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameMatviews.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameMatviews(ctx, nsId, databaseName, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameMatviews", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameMatviews), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewName mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewName(ctx context.Context, nsId uuid.UUID, databaseName, matViewName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, matViewName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewName", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewName indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewName.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewName(ctx, nsId, databaseName, matViewName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, matViewName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewName", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewName), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameDataLatency mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameDataLatency(ctx context.Context, nsId uuid.UUID, databaseName, matViewName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, matViewName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameDataLatency", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameDataLatency indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameDataLatency.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameDataLatency(ctx, nsId, databaseName, matViewName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, matViewName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameDataLatency", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameDataLatency), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameDependencies mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameDependencies(ctx context.Context, nsId uuid.UUID, databaseName, matViewName string, params *mgmt.GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameDependenciesParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, matViewName, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameDependencies", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameDependencies indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameDependencies.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameDependencies(ctx, nsId, databaseName, matViewName, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, matViewName, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameDependencies", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameDependencies), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameMetrics mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameMetrics(ctx context.Context, nsId uuid.UUID, databaseName, matViewName string, params *mgmt.GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameMetricsParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, matViewName, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameMetrics", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameMetrics indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameMetrics.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameMetrics(ctx, nsId, databaseName, matViewName, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, matViewName, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameMetrics", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameMetrics), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameProgress mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameProgress(ctx context.Context, nsId uuid.UUID, databaseName, matViewName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, matViewName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameProgress", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameProgress indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameProgress.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameProgress(ctx, nsId, databaseName, matViewName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, matViewName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameProgress", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameProgress), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameRowCount mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameRowCount(ctx context.Context, nsId uuid.UUID, databaseName, matViewName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, matViewName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameRowCount", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameRowCount indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameRowCount.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameRowCount(ctx, nsId, databaseName, matViewName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, matViewName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameRowCount", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameRowCount), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameThroughput mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameThroughput(ctx context.Context, nsId uuid.UUID, databaseName, matViewName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, matViewName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameThroughput", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameThroughput indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameThroughput.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameThroughput(ctx, nsId, databaseName, matViewName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, matViewName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameThroughput", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameMatviewsMatViewNameThroughput), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameRelations mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameRelations(ctx context.Context, nsId uuid.UUID, databaseName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameRelations", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameRelations indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameRelations.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameRelations(ctx, nsId, databaseName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameRelations", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameRelations), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameSchemas mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameSchemas(ctx context.Context, nsId uuid.UUID, databaseName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameSchemas", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameSchemas indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameSchemas.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameSchemas(ctx, nsId, databaseName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameSchemas", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameSchemas), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameSecrets mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameSecrets(ctx context.Context, nsId uuid.UUID, databaseName string, params *mgmt.GetTenantsNsIdDatabasesDatabaseNameSecretsParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameSecrets", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameSecrets indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameSecrets.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameSecrets(ctx, nsId, databaseName, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameSecrets", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameSecrets), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameSecretsSecretNameReferences mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameSecretsSecretNameReferences(ctx context.Context, nsId uuid.UUID, databaseName, secretName string, params *mgmt.GetTenantsNsIdDatabasesDatabaseNameSecretsSecretNameReferencesParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, secretName, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameSecretsSecretNameReferences", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameSecretsSecretNameReferences indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameSecretsSecretNameReferences.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameSecretsSecretNameReferences(ctx, nsId, databaseName, secretName, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, secretName, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameSecretsSecretNameReferences", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameSecretsSecretNameReferences), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameSinks mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameSinks(ctx context.Context, nsId uuid.UUID, databaseName string, params *mgmt.GetTenantsNsIdDatabasesDatabaseNameSinksParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameSinks", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameSinks indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameSinks.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameSinks(ctx, nsId, databaseName, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameSinks", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameSinks), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameSinksSinkName mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameSinksSinkName(ctx context.Context, nsId uuid.UUID, databaseName, sinkName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, sinkName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameSinksSinkName", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameSinksSinkName indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameSinksSinkName.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameSinksSinkName(ctx, nsId, databaseName, sinkName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, sinkName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameSinksSinkName", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameSinksSinkName), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameDataLatency mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameDataLatency(ctx context.Context, nsId uuid.UUID, databaseName, sinkName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, sinkName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameDataLatency", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameDataLatency indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameDataLatency.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameDataLatency(ctx, nsId, databaseName, sinkName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, sinkName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameDataLatency", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameDataLatency), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameDependencies mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameDependencies(ctx context.Context, nsId uuid.UUID, databaseName, sinkName string, params *mgmt.GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameDependenciesParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, sinkName, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameDependencies", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameDependencies indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameDependencies.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameDependencies(ctx, nsId, databaseName, sinkName, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, sinkName, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameDependencies", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameDependencies), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameMetrics mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameMetrics(ctx context.Context, nsId uuid.UUID, databaseName, sinkName string, params *mgmt.GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameMetricsParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, sinkName, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameMetrics", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameMetrics indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameMetrics.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameMetrics(ctx, nsId, databaseName, sinkName, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, sinkName, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameMetrics", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameMetrics), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameProgress mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameProgress(ctx context.Context, nsId uuid.UUID, databaseName, sinkName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, sinkName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameProgress", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameProgress indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameProgress.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameProgress(ctx, nsId, databaseName, sinkName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, sinkName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameProgress", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameProgress), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameThroughput mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameThroughput(ctx context.Context, nsId uuid.UUID, databaseName, sinkName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, sinkName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameThroughput", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameThroughput indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameThroughput.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameThroughput(ctx, nsId, databaseName, sinkName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, sinkName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameThroughput", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameSinksSinkNameThroughput), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameSources mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameSources(ctx context.Context, nsId uuid.UUID, databaseName string, params *mgmt.GetTenantsNsIdDatabasesDatabaseNameSourcesParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameSources", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameSources indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameSources.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameSources(ctx, nsId, databaseName, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameSources", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameSources), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameSourcesSourceName mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameSourcesSourceName(ctx context.Context, nsId uuid.UUID, databaseName, sourceName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, sourceName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameSourcesSourceName", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameSourcesSourceName indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameSourcesSourceName.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameSourcesSourceName(ctx, nsId, databaseName, sourceName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, sourceName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameSourcesSourceName", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameSourcesSourceName), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameDependencies mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameDependencies(ctx context.Context, nsId uuid.UUID, databaseName, sourceName string, params *mgmt.GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameDependenciesParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, sourceName, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameDependencies", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameDependencies indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameDependencies.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameDependencies(ctx, nsId, databaseName, sourceName, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, sourceName, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameDependencies", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameDependencies), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameMetrics mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameMetrics(ctx context.Context, nsId uuid.UUID, databaseName, sourceName string, params *mgmt.GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameMetricsParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, sourceName, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameMetrics", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameMetrics indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameMetrics.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameMetrics(ctx, nsId, databaseName, sourceName, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, sourceName, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameMetrics", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameMetrics), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameThroughput mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameThroughput(ctx context.Context, nsId uuid.UUID, databaseName, sourceName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, sourceName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameThroughput", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameThroughput indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameThroughput.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameThroughput(ctx, nsId, databaseName, sourceName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, sourceName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameThroughput", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameSourcesSourceNameThroughput), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameTables mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameTables(ctx context.Context, nsId uuid.UUID, databaseName string, params *mgmt.GetTenantsNsIdDatabasesDatabaseNameTablesParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameTables", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameTables indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameTables.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameTables(ctx, nsId, databaseName, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameTables", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameTables), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameTablesTableName mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameTablesTableName(ctx context.Context, nsId uuid.UUID, databaseName, tableName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, tableName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameTablesTableName", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameTablesTableName indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameTablesTableName.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameTablesTableName(ctx, nsId, databaseName, tableName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, tableName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameTablesTableName", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameTablesTableName), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameTablesTableNameDataLatency mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameTablesTableNameDataLatency(ctx context.Context, nsId uuid.UUID, databaseName, tableName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, tableName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameTablesTableNameDataLatency", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameTablesTableNameDataLatency indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameTablesTableNameDataLatency.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameTablesTableNameDataLatency(ctx, nsId, databaseName, tableName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, tableName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameTablesTableNameDataLatency", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameTablesTableNameDataLatency), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameTablesTableNameDependencies mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameTablesTableNameDependencies(ctx context.Context, nsId uuid.UUID, databaseName, tableName string, params *mgmt.GetTenantsNsIdDatabasesDatabaseNameTablesTableNameDependenciesParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, tableName, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameTablesTableNameDependencies", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameTablesTableNameDependencies indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameTablesTableNameDependencies.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameTablesTableNameDependencies(ctx, nsId, databaseName, tableName, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, tableName, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameTablesTableNameDependencies", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameTablesTableNameDependencies), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameTablesTableNameMetrics mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameTablesTableNameMetrics(ctx context.Context, nsId uuid.UUID, databaseName, tableName string, params *mgmt.GetTenantsNsIdDatabasesDatabaseNameTablesTableNameMetricsParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, tableName, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameTablesTableNameMetrics", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameTablesTableNameMetrics indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameTablesTableNameMetrics.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameTablesTableNameMetrics(ctx, nsId, databaseName, tableName, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, tableName, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameTablesTableNameMetrics", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameTablesTableNameMetrics), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameTablesTableNameRowCount mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameTablesTableNameRowCount(ctx context.Context, nsId uuid.UUID, databaseName, tableName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, tableName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameTablesTableNameRowCount", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameTablesTableNameRowCount indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameTablesTableNameRowCount.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameTablesTableNameRowCount(ctx, nsId, databaseName, tableName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, tableName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameTablesTableNameRowCount", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameTablesTableNameRowCount), varargs...)
}

// GetTenantsNsIdDatabasesDatabaseNameTablesTableNameThroughput mocks base method.
func (m *MockClientInterface) GetTenantsNsIdDatabasesDatabaseNameTablesTableNameThroughput(ctx context.Context, nsId uuid.UUID, databaseName, tableName string, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, tableName}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdDatabasesDatabaseNameTablesTableNameThroughput", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdDatabasesDatabaseNameTablesTableNameThroughput indicates an expected call of GetTenantsNsIdDatabasesDatabaseNameTablesTableNameThroughput.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdDatabasesDatabaseNameTablesTableNameThroughput(ctx, nsId, databaseName, tableName any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, tableName}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdDatabasesDatabaseNameTablesTableNameThroughput", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdDatabasesDatabaseNameTablesTableNameThroughput), varargs...)
}

// GetTenantsNsIdEndpoint mocks base method.
func (m *MockClientInterface) GetTenantsNsIdEndpoint(ctx context.Context, nsId uuid.UUID, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdEndpoint", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdEndpoint indicates an expected call of GetTenantsNsIdEndpoint.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdEndpoint(ctx, nsId any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdEndpoint", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdEndpoint), varargs...)
}

// GetTenantsNsIdExtensionsIcebergCompaction mocks base method.
func (m *MockClientInterface) GetTenantsNsIdExtensionsIcebergCompaction(ctx context.Context, nsId uuid.UUID, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdExtensionsIcebergCompaction", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdExtensionsIcebergCompaction indicates an expected call of GetTenantsNsIdExtensionsIcebergCompaction.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdExtensionsIcebergCompaction(ctx, nsId any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdExtensionsIcebergCompaction", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdExtensionsIcebergCompaction), varargs...)
}

// GetTenantsNsIdMatviews mocks base method.
func (m *MockClientInterface) GetTenantsNsIdMatviews(ctx context.Context, nsId uuid.UUID, params *mgmt.GetTenantsNsIdMatviewsParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdMatviews", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdMatviews indicates an expected call of GetTenantsNsIdMatviews.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdMatviews(ctx, nsId, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdMatviews", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdMatviews), varargs...)
}

// GetTenantsNsIdMetrics mocks base method.
func (m *MockClientInterface) GetTenantsNsIdMetrics(ctx context.Context, nsId uuid.UUID, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdMetrics", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdMetrics indicates an expected call of GetTenantsNsIdMetrics.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdMetrics(ctx, nsId any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdMetrics", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdMetrics), varargs...)
}

// GetTenantsNsIdPrivatelinksPrivateLinkId mocks base method.
func (m *MockClientInterface) GetTenantsNsIdPrivatelinksPrivateLinkId(ctx context.Context, nsId, privateLinkId uuid.UUID, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, privateLinkId}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdPrivatelinksPrivateLinkId", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdPrivatelinksPrivateLinkId indicates an expected call of GetTenantsNsIdPrivatelinksPrivateLinkId.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdPrivatelinksPrivateLinkId(ctx, nsId, privateLinkId any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, privateLinkId}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdPrivatelinksPrivateLinkId", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdPrivatelinksPrivateLinkId), varargs...)
}

// GetTenantsNsIdPrometheusApiV1LabelLabelNameValues mocks base method.
func (m *MockClientInterface) GetTenantsNsIdPrometheusApiV1LabelLabelNameValues(ctx context.Context, nsId uuid.UUID, labelName string, params *mgmt.GetTenantsNsIdPrometheusApiV1LabelLabelNameValuesParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, labelName, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdPrometheusApiV1LabelLabelNameValues", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdPrometheusApiV1LabelLabelNameValues indicates an expected call of GetTenantsNsIdPrometheusApiV1LabelLabelNameValues.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdPrometheusApiV1LabelLabelNameValues(ctx, nsId, labelName, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, labelName, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdPrometheusApiV1LabelLabelNameValues", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdPrometheusApiV1LabelLabelNameValues), varargs...)
}

// GetTenantsNsIdPrometheusApiV1Labels mocks base method.
func (m *MockClientInterface) GetTenantsNsIdPrometheusApiV1Labels(ctx context.Context, nsId uuid.UUID, params *mgmt.GetTenantsNsIdPrometheusApiV1LabelsParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdPrometheusApiV1Labels", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdPrometheusApiV1Labels indicates an expected call of GetTenantsNsIdPrometheusApiV1Labels.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdPrometheusApiV1Labels(ctx, nsId, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdPrometheusApiV1Labels", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdPrometheusApiV1Labels), varargs...)
}

// GetTenantsNsIdPrometheusApiV1Query mocks base method.
func (m *MockClientInterface) GetTenantsNsIdPrometheusApiV1Query(ctx context.Context, nsId uuid.UUID, params *mgmt.GetTenantsNsIdPrometheusApiV1QueryParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdPrometheusApiV1Query", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdPrometheusApiV1Query indicates an expected call of GetTenantsNsIdPrometheusApiV1Query.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdPrometheusApiV1Query(ctx, nsId, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdPrometheusApiV1Query", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdPrometheusApiV1Query), varargs...)
}

// GetTenantsNsIdPrometheusApiV1QueryRange mocks base method.
func (m *MockClientInterface) GetTenantsNsIdPrometheusApiV1QueryRange(ctx context.Context, nsId uuid.UUID, params *mgmt.GetTenantsNsIdPrometheusApiV1QueryRangeParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdPrometheusApiV1QueryRange", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdPrometheusApiV1QueryRange indicates an expected call of GetTenantsNsIdPrometheusApiV1QueryRange.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdPrometheusApiV1QueryRange(ctx, nsId, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdPrometheusApiV1QueryRange", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdPrometheusApiV1QueryRange), varargs...)
}

// GetTenantsNsIdPrometheusApiV1Rules mocks base method.
func (m *MockClientInterface) GetTenantsNsIdPrometheusApiV1Rules(ctx context.Context, nsId uuid.UUID, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdPrometheusApiV1Rules", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdPrometheusApiV1Rules indicates an expected call of GetTenantsNsIdPrometheusApiV1Rules.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdPrometheusApiV1Rules(ctx, nsId any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdPrometheusApiV1Rules", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdPrometheusApiV1Rules), varargs...)
}

// GetTenantsNsIdPrometheusApiV1Series mocks base method.
func (m *MockClientInterface) GetTenantsNsIdPrometheusApiV1Series(ctx context.Context, nsId uuid.UUID, params *mgmt.GetTenantsNsIdPrometheusApiV1SeriesParams, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, params}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTenantsNsIdPrometheusApiV1Series", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTenantsNsIdPrometheusApiV1Series indicates an expected call of GetTenantsNsIdPrometheusApiV1Series.
func (mr *MockClientInterfaceMockRecorder) GetTenantsNsIdPrometheusApiV1Series(ctx, nsId, params any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, params}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTenantsNsIdPrometheusApiV1Series", reflect.TypeOf((*MockClientInterface)(nil).GetTenantsNsIdPrometheusApiV1Series), varargs...)
}

// PostByocClusters mocks base method.
func (m *MockClientInterface) PostByocClusters(ctx context.Context, body mgmt.PostByocClustersRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostByocClusters", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostByocClusters indicates an expected call of PostByocClusters.
func (mr *MockClientInterfaceMockRecorder) PostByocClusters(ctx, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostByocClusters", reflect.TypeOf((*MockClientInterface)(nil).PostByocClusters), varargs...)
}

// PostByocClustersNameManualUpdate mocks base method.
func (m *MockClientInterface) PostByocClustersNameManualUpdate(ctx context.Context, name string, body mgmt.PostByocClusterUpdateRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, name, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostByocClustersNameManualUpdate", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostByocClustersNameManualUpdate indicates an expected call of PostByocClustersNameManualUpdate.
func (mr *MockClientInterfaceMockRecorder) PostByocClustersNameManualUpdate(ctx, name, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, name, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostByocClustersNameManualUpdate", reflect.TypeOf((*MockClientInterface)(nil).PostByocClustersNameManualUpdate), varargs...)
}

// PostByocClustersNameManualUpdateWithBody mocks base method.
func (m *MockClientInterface) PostByocClustersNameManualUpdateWithBody(ctx context.Context, name, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, name, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostByocClustersNameManualUpdateWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostByocClustersNameManualUpdateWithBody indicates an expected call of PostByocClustersNameManualUpdateWithBody.
func (mr *MockClientInterfaceMockRecorder) PostByocClustersNameManualUpdateWithBody(ctx, name, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, name, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostByocClustersNameManualUpdateWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostByocClustersNameManualUpdateWithBody), varargs...)
}

// PostByocClustersNameUpdate mocks base method.
func (m *MockClientInterface) PostByocClustersNameUpdate(ctx context.Context, name string, body mgmt.PostByocClusterUpdateRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, name, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostByocClustersNameUpdate", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostByocClustersNameUpdate indicates an expected call of PostByocClustersNameUpdate.
func (mr *MockClientInterfaceMockRecorder) PostByocClustersNameUpdate(ctx, name, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, name, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostByocClustersNameUpdate", reflect.TypeOf((*MockClientInterface)(nil).PostByocClustersNameUpdate), varargs...)
}

// PostByocClustersNameUpdateWithBody mocks base method.
func (m *MockClientInterface) PostByocClustersNameUpdateWithBody(ctx context.Context, name, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, name, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostByocClustersNameUpdateWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostByocClustersNameUpdateWithBody indicates an expected call of PostByocClustersNameUpdateWithBody.
func (mr *MockClientInterfaceMockRecorder) PostByocClustersNameUpdateWithBody(ctx, name, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, name, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostByocClustersNameUpdateWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostByocClustersNameUpdateWithBody), varargs...)
}

// PostByocClustersWithBody mocks base method.
func (m *MockClientInterface) PostByocClustersWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostByocClustersWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostByocClustersWithBody indicates an expected call of PostByocClustersWithBody.
func (mr *MockClientInterfaceMockRecorder) PostByocClustersWithBody(ctx, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostByocClustersWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostByocClustersWithBody), varargs...)
}

// PostTenants mocks base method.
func (m *MockClientInterface) PostTenants(ctx context.Context, body mgmt.TenantRequestRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenants", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenants indicates an expected call of PostTenants.
func (mr *MockClientInterfaceMockRecorder) PostTenants(ctx, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenants", reflect.TypeOf((*MockClientInterface)(nil).PostTenants), varargs...)
}

// PostTenantsNsIdBackups mocks base method.
func (m *MockClientInterface) PostTenantsNsIdBackups(ctx context.Context, nsId uuid.UUID, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdBackups", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdBackups indicates an expected call of PostTenantsNsIdBackups.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdBackups(ctx, nsId any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdBackups", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdBackups), varargs...)
}

// PostTenantsNsIdBackupsSnapshotIdInPlaceRestore mocks base method.
func (m *MockClientInterface) PostTenantsNsIdBackupsSnapshotIdInPlaceRestore(ctx context.Context, nsId, snapshotId uuid.UUID, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, snapshotId}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdBackupsSnapshotIdInPlaceRestore", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdBackupsSnapshotIdInPlaceRestore indicates an expected call of PostTenantsNsIdBackupsSnapshotIdInPlaceRestore.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdBackupsSnapshotIdInPlaceRestore(ctx, nsId, snapshotId any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, snapshotId}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdBackupsSnapshotIdInPlaceRestore", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdBackupsSnapshotIdInPlaceRestore), varargs...)
}

// PostTenantsNsIdBackupsSnapshotIdRestore mocks base method.
func (m *MockClientInterface) PostTenantsNsIdBackupsSnapshotIdRestore(ctx context.Context, nsId, snapshotId uuid.UUID, body mgmt.PostTenantRestoreRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, snapshotId, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdBackupsSnapshotIdRestore", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdBackupsSnapshotIdRestore indicates an expected call of PostTenantsNsIdBackupsSnapshotIdRestore.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdBackupsSnapshotIdRestore(ctx, nsId, snapshotId, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, snapshotId, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdBackupsSnapshotIdRestore", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdBackupsSnapshotIdRestore), varargs...)
}

// PostTenantsNsIdBackupsSnapshotIdRestoreWithBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdBackupsSnapshotIdRestoreWithBody(ctx context.Context, nsId, snapshotId uuid.UUID, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, snapshotId, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdBackupsSnapshotIdRestoreWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdBackupsSnapshotIdRestoreWithBody indicates an expected call of PostTenantsNsIdBackupsSnapshotIdRestoreWithBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdBackupsSnapshotIdRestoreWithBody(ctx, nsId, snapshotId, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, snapshotId, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdBackupsSnapshotIdRestoreWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdBackupsSnapshotIdRestoreWithBody), varargs...)
}

// PostTenantsNsIdDatabaseUsers mocks base method.
func (m *MockClientInterface) PostTenantsNsIdDatabaseUsers(ctx context.Context, nsId uuid.UUID, body mgmt.CreateDBUserRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdDatabaseUsers", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdDatabaseUsers indicates an expected call of PostTenantsNsIdDatabaseUsers.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdDatabaseUsers(ctx, nsId, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdDatabaseUsers", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdDatabaseUsers), varargs...)
}

// PostTenantsNsIdDatabaseUsersWithBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdDatabaseUsersWithBody(ctx context.Context, nsId uuid.UUID, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdDatabaseUsersWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdDatabaseUsersWithBody indicates an expected call of PostTenantsNsIdDatabaseUsersWithBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdDatabaseUsersWithBody(ctx, nsId, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdDatabaseUsersWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdDatabaseUsersWithBody), varargs...)
}

// PostTenantsNsIdDatabasesDatabaseNameExecuteSQL mocks base method.
func (m *MockClientInterface) PostTenantsNsIdDatabasesDatabaseNameExecuteSQL(ctx context.Context, nsId uuid.UUID, databaseName string, body mgmt.SqlExecutionRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdDatabasesDatabaseNameExecuteSQL", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdDatabasesDatabaseNameExecuteSQL indicates an expected call of PostTenantsNsIdDatabasesDatabaseNameExecuteSQL.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdDatabasesDatabaseNameExecuteSQL(ctx, nsId, databaseName, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdDatabasesDatabaseNameExecuteSQL", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdDatabasesDatabaseNameExecuteSQL), varargs...)
}

// PostTenantsNsIdDatabasesDatabaseNameExecuteSQLWithBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdDatabasesDatabaseNameExecuteSQLWithBody(ctx context.Context, nsId uuid.UUID, databaseName, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdDatabasesDatabaseNameExecuteSQLWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdDatabasesDatabaseNameExecuteSQLWithBody indicates an expected call of PostTenantsNsIdDatabasesDatabaseNameExecuteSQLWithBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdDatabasesDatabaseNameExecuteSQLWithBody(ctx, nsId, databaseName, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdDatabasesDatabaseNameExecuteSQLWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdDatabasesDatabaseNameExecuteSQLWithBody), varargs...)
}

// PostTenantsNsIdDatabasesDatabaseNameQuerySQL mocks base method.
func (m *MockClientInterface) PostTenantsNsIdDatabasesDatabaseNameQuerySQL(ctx context.Context, nsId uuid.UUID, databaseName string, body mgmt.SqlQueryRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdDatabasesDatabaseNameQuerySQL", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdDatabasesDatabaseNameQuerySQL indicates an expected call of PostTenantsNsIdDatabasesDatabaseNameQuerySQL.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdDatabasesDatabaseNameQuerySQL(ctx, nsId, databaseName, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdDatabasesDatabaseNameQuerySQL", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdDatabasesDatabaseNameQuerySQL), varargs...)
}

// PostTenantsNsIdDatabasesDatabaseNameQuerySQLWithBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdDatabasesDatabaseNameQuerySQLWithBody(ctx context.Context, nsId uuid.UUID, databaseName, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdDatabasesDatabaseNameQuerySQLWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdDatabasesDatabaseNameQuerySQLWithBody indicates an expected call of PostTenantsNsIdDatabasesDatabaseNameQuerySQLWithBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdDatabasesDatabaseNameQuerySQLWithBody(ctx, nsId, databaseName, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdDatabasesDatabaseNameQuerySQLWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdDatabasesDatabaseNameQuerySQLWithBody), varargs...)
}

// PostTenantsNsIdDatabasesDatabaseNameSecrets mocks base method.
func (m *MockClientInterface) PostTenantsNsIdDatabasesDatabaseNameSecrets(ctx context.Context, nsId uuid.UUID, databaseName string, body mgmt.CreateSecretRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdDatabasesDatabaseNameSecrets", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdDatabasesDatabaseNameSecrets indicates an expected call of PostTenantsNsIdDatabasesDatabaseNameSecrets.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdDatabasesDatabaseNameSecrets(ctx, nsId, databaseName, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdDatabasesDatabaseNameSecrets", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdDatabasesDatabaseNameSecrets), varargs...)
}

// PostTenantsNsIdDatabasesDatabaseNameSecretsWithBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdDatabasesDatabaseNameSecretsWithBody(ctx context.Context, nsId uuid.UUID, databaseName, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, databaseName, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdDatabasesDatabaseNameSecretsWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdDatabasesDatabaseNameSecretsWithBody indicates an expected call of PostTenantsNsIdDatabasesDatabaseNameSecretsWithBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdDatabasesDatabaseNameSecretsWithBody(ctx, nsId, databaseName, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, databaseName, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdDatabasesDatabaseNameSecretsWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdDatabasesDatabaseNameSecretsWithBody), varargs...)
}

// PostTenantsNsIdExtensionsIcebergCompaction mocks base method.
func (m *MockClientInterface) PostTenantsNsIdExtensionsIcebergCompaction(ctx context.Context, nsId uuid.UUID, body mgmt.PostTenantsNsIdExtensionsIcebergCompactionJSONRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdExtensionsIcebergCompaction", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdExtensionsIcebergCompaction indicates an expected call of PostTenantsNsIdExtensionsIcebergCompaction.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdExtensionsIcebergCompaction(ctx, nsId, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdExtensionsIcebergCompaction", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdExtensionsIcebergCompaction), varargs...)
}

// PostTenantsNsIdExtensionsIcebergCompactionWithBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdExtensionsIcebergCompactionWithBody(ctx context.Context, nsId uuid.UUID, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdExtensionsIcebergCompactionWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdExtensionsIcebergCompactionWithBody indicates an expected call of PostTenantsNsIdExtensionsIcebergCompactionWithBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdExtensionsIcebergCompactionWithBody(ctx, nsId, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdExtensionsIcebergCompactionWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdExtensionsIcebergCompactionWithBody), varargs...)
}

// PostTenantsNsIdPrivatelinks mocks base method.
func (m *MockClientInterface) PostTenantsNsIdPrivatelinks(ctx context.Context, nsId uuid.UUID, body mgmt.PostPrivateLinkRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdPrivatelinks", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdPrivatelinks indicates an expected call of PostTenantsNsIdPrivatelinks.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdPrivatelinks(ctx, nsId, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdPrivatelinks", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdPrivatelinks), varargs...)
}

// PostTenantsNsIdPrivatelinksWithBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdPrivatelinksWithBody(ctx context.Context, nsId uuid.UUID, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdPrivatelinksWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdPrivatelinksWithBody indicates an expected call of PostTenantsNsIdPrivatelinksWithBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdPrivatelinksWithBody(ctx, nsId, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdPrivatelinksWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdPrivatelinksWithBody), varargs...)
}

// PostTenantsNsIdPrometheusApiV1LabelsWithBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdPrometheusApiV1LabelsWithBody(ctx context.Context, nsId uuid.UUID, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdPrometheusApiV1LabelsWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdPrometheusApiV1LabelsWithBody indicates an expected call of PostTenantsNsIdPrometheusApiV1LabelsWithBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdPrometheusApiV1LabelsWithBody(ctx, nsId, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdPrometheusApiV1LabelsWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdPrometheusApiV1LabelsWithBody), varargs...)
}

// PostTenantsNsIdPrometheusApiV1LabelsWithFormdataBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdPrometheusApiV1LabelsWithFormdataBody(ctx context.Context, nsId uuid.UUID, body mgmt.PostTenantsNsIdPrometheusApiV1LabelsFormdataRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdPrometheusApiV1LabelsWithFormdataBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdPrometheusApiV1LabelsWithFormdataBody indicates an expected call of PostTenantsNsIdPrometheusApiV1LabelsWithFormdataBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdPrometheusApiV1LabelsWithFormdataBody(ctx, nsId, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdPrometheusApiV1LabelsWithFormdataBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdPrometheusApiV1LabelsWithFormdataBody), varargs...)
}

// PostTenantsNsIdPrometheusApiV1QueryRangeWithBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdPrometheusApiV1QueryRangeWithBody(ctx context.Context, nsId uuid.UUID, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdPrometheusApiV1QueryRangeWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdPrometheusApiV1QueryRangeWithBody indicates an expected call of PostTenantsNsIdPrometheusApiV1QueryRangeWithBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdPrometheusApiV1QueryRangeWithBody(ctx, nsId, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdPrometheusApiV1QueryRangeWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdPrometheusApiV1QueryRangeWithBody), varargs...)
}

// PostTenantsNsIdPrometheusApiV1QueryRangeWithFormdataBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdPrometheusApiV1QueryRangeWithFormdataBody(ctx context.Context, nsId uuid.UUID, body mgmt.PostTenantsNsIdPrometheusApiV1QueryRangeFormdataRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdPrometheusApiV1QueryRangeWithFormdataBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdPrometheusApiV1QueryRangeWithFormdataBody indicates an expected call of PostTenantsNsIdPrometheusApiV1QueryRangeWithFormdataBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdPrometheusApiV1QueryRangeWithFormdataBody(ctx, nsId, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdPrometheusApiV1QueryRangeWithFormdataBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdPrometheusApiV1QueryRangeWithFormdataBody), varargs...)
}

// PostTenantsNsIdPrometheusApiV1QueryWithBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdPrometheusApiV1QueryWithBody(ctx context.Context, nsId uuid.UUID, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdPrometheusApiV1QueryWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdPrometheusApiV1QueryWithBody indicates an expected call of PostTenantsNsIdPrometheusApiV1QueryWithBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdPrometheusApiV1QueryWithBody(ctx, nsId, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdPrometheusApiV1QueryWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdPrometheusApiV1QueryWithBody), varargs...)
}

// PostTenantsNsIdPrometheusApiV1QueryWithFormdataBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdPrometheusApiV1QueryWithFormdataBody(ctx context.Context, nsId uuid.UUID, body mgmt.PostTenantsNsIdPrometheusApiV1QueryFormdataRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdPrometheusApiV1QueryWithFormdataBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdPrometheusApiV1QueryWithFormdataBody indicates an expected call of PostTenantsNsIdPrometheusApiV1QueryWithFormdataBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdPrometheusApiV1QueryWithFormdataBody(ctx, nsId, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdPrometheusApiV1QueryWithFormdataBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdPrometheusApiV1QueryWithFormdataBody), varargs...)
}

// PostTenantsNsIdPrometheusApiV1SeriesWithBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdPrometheusApiV1SeriesWithBody(ctx context.Context, nsId uuid.UUID, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdPrometheusApiV1SeriesWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdPrometheusApiV1SeriesWithBody indicates an expected call of PostTenantsNsIdPrometheusApiV1SeriesWithBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdPrometheusApiV1SeriesWithBody(ctx, nsId, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdPrometheusApiV1SeriesWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdPrometheusApiV1SeriesWithBody), varargs...)
}

// PostTenantsNsIdPrometheusApiV1SeriesWithFormdataBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdPrometheusApiV1SeriesWithFormdataBody(ctx context.Context, nsId uuid.UUID, body mgmt.PostTenantsNsIdPrometheusApiV1SeriesFormdataRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdPrometheusApiV1SeriesWithFormdataBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdPrometheusApiV1SeriesWithFormdataBody indicates an expected call of PostTenantsNsIdPrometheusApiV1SeriesWithFormdataBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdPrometheusApiV1SeriesWithFormdataBody(ctx, nsId, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdPrometheusApiV1SeriesWithFormdataBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdPrometheusApiV1SeriesWithFormdataBody), varargs...)
}

// PostTenantsNsIdResourceGroups mocks base method.
func (m *MockClientInterface) PostTenantsNsIdResourceGroups(ctx context.Context, nsId uuid.UUID, body mgmt.CreateResourceGroupsRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdResourceGroups", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdResourceGroups indicates an expected call of PostTenantsNsIdResourceGroups.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdResourceGroups(ctx, nsId, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdResourceGroups", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdResourceGroups), varargs...)
}

// PostTenantsNsIdResourceGroupsResourceGroup mocks base method.
func (m *MockClientInterface) PostTenantsNsIdResourceGroupsResourceGroup(ctx context.Context, nsId uuid.UUID, resourceGroup string, body mgmt.UpdateResourceGroupsRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, resourceGroup, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdResourceGroupsResourceGroup", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdResourceGroupsResourceGroup indicates an expected call of PostTenantsNsIdResourceGroupsResourceGroup.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdResourceGroupsResourceGroup(ctx, nsId, resourceGroup, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, resourceGroup, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdResourceGroupsResourceGroup", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdResourceGroupsResourceGroup), varargs...)
}

// PostTenantsNsIdResourceGroupsResourceGroupWithBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdResourceGroupsResourceGroupWithBody(ctx context.Context, nsId uuid.UUID, resourceGroup, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, resourceGroup, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdResourceGroupsResourceGroupWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdResourceGroupsResourceGroupWithBody indicates an expected call of PostTenantsNsIdResourceGroupsResourceGroupWithBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdResourceGroupsResourceGroupWithBody(ctx, nsId, resourceGroup, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, resourceGroup, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdResourceGroupsResourceGroupWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdResourceGroupsResourceGroupWithBody), varargs...)
}

// PostTenantsNsIdResourceGroupsWithBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdResourceGroupsWithBody(ctx context.Context, nsId uuid.UUID, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdResourceGroupsWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdResourceGroupsWithBody indicates an expected call of PostTenantsNsIdResourceGroupsWithBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdResourceGroupsWithBody(ctx, nsId, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdResourceGroupsWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdResourceGroupsWithBody), varargs...)
}

// PostTenantsNsIdRestart mocks base method.
func (m *MockClientInterface) PostTenantsNsIdRestart(ctx context.Context, nsId uuid.UUID, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdRestart", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdRestart indicates an expected call of PostTenantsNsIdRestart.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdRestart(ctx, nsId any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdRestart", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdRestart), varargs...)
}

// PostTenantsNsIdSourcesFetchKafkaSchema mocks base method.
func (m *MockClientInterface) PostTenantsNsIdSourcesFetchKafkaSchema(ctx context.Context, nsId uuid.UUID, body mgmt.PostSourcesFetchKafkaSchemaRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdSourcesFetchKafkaSchema", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdSourcesFetchKafkaSchema indicates an expected call of PostTenantsNsIdSourcesFetchKafkaSchema.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdSourcesFetchKafkaSchema(ctx, nsId, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdSourcesFetchKafkaSchema", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdSourcesFetchKafkaSchema), varargs...)
}

// PostTenantsNsIdSourcesFetchKafkaSchemaWithBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdSourcesFetchKafkaSchemaWithBody(ctx context.Context, nsId uuid.UUID, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdSourcesFetchKafkaSchemaWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdSourcesFetchKafkaSchemaWithBody indicates an expected call of PostTenantsNsIdSourcesFetchKafkaSchemaWithBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdSourcesFetchKafkaSchemaWithBody(ctx, nsId, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdSourcesFetchKafkaSchemaWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdSourcesFetchKafkaSchemaWithBody), varargs...)
}

// PostTenantsNsIdSourcesFetchSchema mocks base method.
func (m *MockClientInterface) PostTenantsNsIdSourcesFetchSchema(ctx context.Context, nsId uuid.UUID, body mgmt.PostSourcesFetchSchemaRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdSourcesFetchSchema", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdSourcesFetchSchema indicates an expected call of PostTenantsNsIdSourcesFetchSchema.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdSourcesFetchSchema(ctx, nsId, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdSourcesFetchSchema", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdSourcesFetchSchema), varargs...)
}

// PostTenantsNsIdSourcesFetchSchemaWithBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdSourcesFetchSchemaWithBody(ctx context.Context, nsId uuid.UUID, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdSourcesFetchSchemaWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdSourcesFetchSchemaWithBody indicates an expected call of PostTenantsNsIdSourcesFetchSchemaWithBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdSourcesFetchSchemaWithBody(ctx, nsId, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdSourcesFetchSchemaWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdSourcesFetchSchemaWithBody), varargs...)
}

// PostTenantsNsIdSourcesPing mocks base method.
func (m *MockClientInterface) PostTenantsNsIdSourcesPing(ctx context.Context, nsId uuid.UUID, body mgmt.PostSourcesPingRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdSourcesPing", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdSourcesPing indicates an expected call of PostTenantsNsIdSourcesPing.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdSourcesPing(ctx, nsId, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdSourcesPing", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdSourcesPing), varargs...)
}

// PostTenantsNsIdSourcesPingWithBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdSourcesPingWithBody(ctx context.Context, nsId uuid.UUID, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdSourcesPingWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdSourcesPingWithBody indicates an expected call of PostTenantsNsIdSourcesPingWithBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdSourcesPingWithBody(ctx, nsId, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdSourcesPingWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdSourcesPingWithBody), varargs...)
}

// PostTenantsNsIdStart mocks base method.
func (m *MockClientInterface) PostTenantsNsIdStart(ctx context.Context, nsId uuid.UUID, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdStart", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdStart indicates an expected call of PostTenantsNsIdStart.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdStart(ctx, nsId any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdStart", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdStart), varargs...)
}

// PostTenantsNsIdStop mocks base method.
func (m *MockClientInterface) PostTenantsNsIdStop(ctx context.Context, nsId uuid.UUID, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdStop", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdStop indicates an expected call of PostTenantsNsIdStop.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdStop(ctx, nsId any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdStop", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdStop), varargs...)
}

// PostTenantsNsIdUpdateResource mocks base method.
func (m *MockClientInterface) PostTenantsNsIdUpdateResource(ctx context.Context, nsId uuid.UUID, body mgmt.PostTenantResourcesRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdUpdateResource", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdUpdateResource indicates an expected call of PostTenantsNsIdUpdateResource.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdUpdateResource(ctx, nsId, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdUpdateResource", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdUpdateResource), varargs...)
}

// PostTenantsNsIdUpdateResourceWithBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdUpdateResourceWithBody(ctx context.Context, nsId uuid.UUID, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdUpdateResourceWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdUpdateResourceWithBody indicates an expected call of PostTenantsNsIdUpdateResourceWithBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdUpdateResourceWithBody(ctx, nsId, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdUpdateResourceWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdUpdateResourceWithBody), varargs...)
}

// PostTenantsNsIdUpdateVersion mocks base method.
func (m *MockClientInterface) PostTenantsNsIdUpdateVersion(ctx context.Context, nsId uuid.UUID, body mgmt.PostTenantUpdateVersionRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdUpdateVersion", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdUpdateVersion indicates an expected call of PostTenantsNsIdUpdateVersion.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdUpdateVersion(ctx, nsId, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdUpdateVersion", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdUpdateVersion), varargs...)
}

// PostTenantsNsIdUpdateVersionWithBody mocks base method.
func (m *MockClientInterface) PostTenantsNsIdUpdateVersionWithBody(ctx context.Context, nsId uuid.UUID, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsNsIdUpdateVersionWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsNsIdUpdateVersionWithBody indicates an expected call of PostTenantsNsIdUpdateVersionWithBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsNsIdUpdateVersionWithBody(ctx, nsId, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsNsIdUpdateVersionWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsNsIdUpdateVersionWithBody), varargs...)
}

// PostTenantsWithBody mocks base method.
func (m *MockClientInterface) PostTenantsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PostTenantsWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PostTenantsWithBody indicates an expected call of PostTenantsWithBody.
func (mr *MockClientInterfaceMockRecorder) PostTenantsWithBody(ctx, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostTenantsWithBody", reflect.TypeOf((*MockClientInterface)(nil).PostTenantsWithBody), varargs...)
}

// PutByocClustersName mocks base method.
func (m *MockClientInterface) PutByocClustersName(ctx context.Context, name string, body mgmt.PutByocClusterRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, name, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PutByocClustersName", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PutByocClustersName indicates an expected call of PutByocClustersName.
func (mr *MockClientInterfaceMockRecorder) PutByocClustersName(ctx, name, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, name, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutByocClustersName", reflect.TypeOf((*MockClientInterface)(nil).PutByocClustersName), varargs...)
}

// PutByocClustersNameWithBody mocks base method.
func (m *MockClientInterface) PutByocClustersNameWithBody(ctx context.Context, name, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, name, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PutByocClustersNameWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PutByocClustersNameWithBody indicates an expected call of PutByocClustersNameWithBody.
func (mr *MockClientInterfaceMockRecorder) PutByocClustersNameWithBody(ctx, name, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, name, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutByocClustersNameWithBody", reflect.TypeOf((*MockClientInterface)(nil).PutByocClustersNameWithBody), varargs...)
}

// PutTenantsNsIdDatabaseUsersDbuserName mocks base method.
func (m *MockClientInterface) PutTenantsNsIdDatabaseUsersDbuserName(ctx context.Context, nsId uuid.UUID, dbuserName string, body mgmt.UpdateDBUserRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, dbuserName, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PutTenantsNsIdDatabaseUsersDbuserName", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PutTenantsNsIdDatabaseUsersDbuserName indicates an expected call of PutTenantsNsIdDatabaseUsersDbuserName.
func (mr *MockClientInterfaceMockRecorder) PutTenantsNsIdDatabaseUsersDbuserName(ctx, nsId, dbuserName, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, dbuserName, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutTenantsNsIdDatabaseUsersDbuserName", reflect.TypeOf((*MockClientInterface)(nil).PutTenantsNsIdDatabaseUsersDbuserName), varargs...)
}

// PutTenantsNsIdDatabaseUsersDbuserNameWithBody mocks base method.
func (m *MockClientInterface) PutTenantsNsIdDatabaseUsersDbuserNameWithBody(ctx context.Context, nsId uuid.UUID, dbuserName, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, dbuserName, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PutTenantsNsIdDatabaseUsersDbuserNameWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PutTenantsNsIdDatabaseUsersDbuserNameWithBody indicates an expected call of PutTenantsNsIdDatabaseUsersDbuserNameWithBody.
func (mr *MockClientInterfaceMockRecorder) PutTenantsNsIdDatabaseUsersDbuserNameWithBody(ctx, nsId, dbuserName, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, dbuserName, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutTenantsNsIdDatabaseUsersDbuserNameWithBody", reflect.TypeOf((*MockClientInterface)(nil).PutTenantsNsIdDatabaseUsersDbuserNameWithBody), varargs...)
}

// PutTenantsNsIdExtensionsIcebergCompaction mocks base method.
func (m *MockClientInterface) PutTenantsNsIdExtensionsIcebergCompaction(ctx context.Context, nsId uuid.UUID, body mgmt.PutTenantsNsIdExtensionsIcebergCompactionJSONRequestBody, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PutTenantsNsIdExtensionsIcebergCompaction", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PutTenantsNsIdExtensionsIcebergCompaction indicates an expected call of PutTenantsNsIdExtensionsIcebergCompaction.
func (mr *MockClientInterfaceMockRecorder) PutTenantsNsIdExtensionsIcebergCompaction(ctx, nsId, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutTenantsNsIdExtensionsIcebergCompaction", reflect.TypeOf((*MockClientInterface)(nil).PutTenantsNsIdExtensionsIcebergCompaction), varargs...)
}

// PutTenantsNsIdExtensionsIcebergCompactionWithBody mocks base method.
func (m *MockClientInterface) PutTenantsNsIdExtensionsIcebergCompactionWithBody(ctx context.Context, nsId uuid.UUID, contentType string, body io.Reader, reqEditors ...mgmt.RequestEditorFn) (*http.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, nsId, contentType, body}
	for _, a := range reqEditors {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PutTenantsNsIdExtensionsIcebergCompactionWithBody", varargs...)
	ret0, _ := ret[0].(*http.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PutTenantsNsIdExtensionsIcebergCompactionWithBody indicates an expected call of PutTenantsNsIdExtensionsIcebergCompactionWithBody.
func (mr *MockClientInterfaceMockRecorder) PutTenantsNsIdExtensionsIcebergCompactionWithBody(ctx, nsId, contentType, body any, reqEditors ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, nsId, contentType, body}, reqEditors...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutTenantsNsIdExtensionsIcebergCompactionWithBody", reflect.TypeOf((*MockClientInterface)(nil).PutTenantsNsIdExtensionsIcebergCompactionWithBody), varargs...)
}
