// Package mgmt provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package mgmt

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

const (
	ApiKeyAuthScopes = "ApiKeyAuth.Scopes"
	BearerAuthScopes = "BearerAuth.Scopes"
)

// Defines values for ClusterStatus.
const (
	ClusterStatusDeleted                 ClusterStatus = "Deleted"
	ClusterStatusFailed                  ClusterStatus = "Failed"
	ClusterStatusPendingResourceDeletion ClusterStatus = "PendingResourceDeletion"
	ClusterStatusProvisioned             ClusterStatus = "Provisioned"
	ClusterStatusReady                   ClusterStatus = "Ready"
	ClusterStatusTerminating             ClusterStatus = "Terminating"
	ClusterStatusUninitialized           ClusterStatus = "Uninitialized"
	ClusterStatusUpdating                ClusterStatus = "Updating"
)

// Defines values for KafkaConfigSaslMechanisms.
const (
	GSSAPI      KafkaConfigSaslMechanisms = "GSSAPI"
	OAUTHBEARER KafkaConfigSaslMechanisms = "OAUTHBEARER"
	PLAIN       KafkaConfigSaslMechanisms = "PLAIN"
	SCRAMSHA256 KafkaConfigSaslMechanisms = "SCRAM-SHA-256"
	SCRAMSHA512 KafkaConfigSaslMechanisms = "SCRAM-SHA-512"
)

// Defines values for KafkaConfigSecurityProtocol.
const (
	SASLPLAINTEXT KafkaConfigSecurityProtocol = "SASL_PLAINTEXT"
	SASLSSL       KafkaConfigSecurityProtocol = "SASL_SSL"
	SSL           KafkaConfigSecurityProtocol = "SSL"
)

// Defines values for MetaStoreType.
const (
	AwsRds      MetaStoreType = "aws_rds"
	AzrPostgres MetaStoreType = "azr_postgres"
	Etcd        MetaStoreType = "etcd"
	GcpCloudsql MetaStoreType = "gcp_cloudsql"
	Postgresql  MetaStoreType = "postgresql"
	SharingPg   MetaStoreType = "sharing_pg"
)

// Defines values for PostSourceRequestBodyType.
const (
	Kafka PostSourceRequestBodyType = "kafka"
)

// Defines values for PrivateLinkConnectionState.
const (
	ACCEPTED          PrivateLinkConnectionState = "ACCEPTED"
	CLOSED            PrivateLinkConnectionState = "CLOSED"
	PENDING           PrivateLinkConnectionState = "PENDING"
	REJECTED          PrivateLinkConnectionState = "REJECTED"
	STATUSUNSPECIFIED PrivateLinkConnectionState = "STATUS_UNSPECIFIED"
)

// Defines values for PrivateLinkStatus.
const (
	CREATED  PrivateLinkStatus = "CREATED"
	CREATING PrivateLinkStatus = "CREATING"
	DELETING PrivateLinkStatus = "DELETING"
	ERROR    PrivateLinkStatus = "ERROR"
	UNKNOWN  PrivateLinkStatus = "UNKNOWN"
)

// Defines values for TenantHealthStatus.
const (
	Healthy   TenantHealthStatus = "Healthy"
	Unhealthy TenantHealthStatus = "Unhealthy"
	Unknown   TenantHealthStatus = "Unknown"
)

// Defines values for TenantStatus.
const (
	TenantStatusConfigUpdating                       TenantStatus = "ConfigUpdating"
	TenantStatusCreating                             TenantStatus = "Creating"
	TenantStatusDeleting                             TenantStatus = "Deleting"
	TenantStatusExpired                              TenantStatus = "Expired"
	TenantStatusExtensionCompactionDisabling         TenantStatus = "ExtensionCompactionDisabling"
	TenantStatusExtensionCompactionEnabling          TenantStatus = "ExtensionCompactionEnabling"
	TenantStatusExtensionServerlessBackfillDisabling TenantStatus = "ExtensionServerlessBackfillDisabling"
	TenantStatusExtensionServerlessBackfillEnabling  TenantStatus = "ExtensionServerlessBackfillEnabling"
	TenantStatusExtensionServerlessBackfillUpdate    TenantStatus = "ExtensionServerlessBackfillUpdate"
	TenantStatusFailed                               TenantStatus = "Failed"
	TenantStatusMetaMigrating                        TenantStatus = "MetaMigrating"
	TenantStatusRestoring                            TenantStatus = "Restoring"
	TenantStatusRunning                              TenantStatus = "Running"
	TenantStatusSnapshotting                         TenantStatus = "Snapshotting"
	TenantStatusStarting                             TenantStatus = "Starting"
	TenantStatusStopped                              TenantStatus = "Stopped"
	TenantStatusStopping                             TenantStatus = "Stopping"
	TenantStatusUpdating                             TenantStatus = "Updating"
	TenantStatusUpgrading                            TenantStatus = "Upgrading"
)

// Defines values for TenantUsageType.
const (
	TenantUsageTypeGeneral  TenantUsageType = "general"
	TenantUsageTypePipeline TenantUsageType = "pipeline"
)

// Defines values for TenantRequestRequestBodyUsageType.
const (
	TenantRequestRequestBodyUsageTypeGeneral  TenantRequestRequestBodyUsageType = "general"
	TenantRequestRequestBodyUsageTypePipeline TenantRequestRequestBodyUsageType = "pipeline"
)

// Defines values for TierId.
const (
	BYOC           TierId = "BYOC"
	Benchmark      TierId = "Benchmark"
	DeveloperBasic TierId = "Developer-Basic"
	DeveloperFree  TierId = "Developer-Free"
	DeveloperTest  TierId = "Developer-Test"
	Free           TierId = "Free"
	Invited        TierId = "Invited"
	Standard       TierId = "Standard"
	Test           TierId = "Test"
)

// Defines values for QueryErrLogParamsTarget.
const (
	Message QueryErrLogParamsTarget = "message"
	Name    QueryErrLogParamsTarget = "name"
	Sink    QueryErrLogParamsTarget = "sink"
	Source  QueryErrLogParamsTarget = "source"
	Table   QueryErrLogParamsTarget = "table"
	Target  QueryErrLogParamsTarget = "target"
)

// Defines values for QueryErrLogParamsDirection.
const (
	Backward QueryErrLogParamsDirection = "backward"
	Forward  QueryErrLogParamsDirection = "forward"
)

// AvailableComponentType defines model for AvailableComponentType.
type AvailableComponentType struct {
	Cpu     string `json:"cpu"`
	Id      string `json:"id"`
	Maximum int    `json:"maximum"`
	Memory  string `json:"memory"`
}

// AvailableMetaStore defines model for AvailableMetaStore.
type AvailableMetaStore struct {
	AwsRds      *AvailableMetaStoreAwsRds      `json:"aws_rds,omitempty"`
	AzrPostgres *AvailableMetaStoreAzrPostgres `json:"azr_postgres,omitempty"`
	Etcd        *AvailableMetaStoreEtcd        `json:"etcd,omitempty"`
	GcpCloudsql *AvailableMetaStoreGcpCloudSql `json:"gcp_cloudsql,omitempty"`
	Postgresql  *AvailableMetaStorePostgreSql  `json:"postgresql,omitempty"`
	SharingPg   *AvailableMetaStoreSharingPg   `json:"sharing_pg,omitempty"`
}

// AvailableMetaStoreAwsRds defines model for AvailableMetaStoreAwsRds.
type AvailableMetaStoreAwsRds struct {
	Enabled *bool `json:"enabled,omitempty"`
}

// AvailableMetaStoreAzrPostgres defines model for AvailableMetaStoreAzrPostgres.
type AvailableMetaStoreAzrPostgres struct {
	Enabled *bool `json:"enabled,omitempty"`
}

// AvailableMetaStoreEtcd defines model for AvailableMetaStoreEtcd.
type AvailableMetaStoreEtcd struct {
	MaximumSizeGiB int                      `json:"maximumSizeGiB"`
	Nodes          []AvailableComponentType `json:"nodes"`
}

// AvailableMetaStoreGcpCloudSql defines model for AvailableMetaStoreGcpCloudSql.
type AvailableMetaStoreGcpCloudSql struct {
	Enabled *bool `json:"enabled,omitempty"`
}

// AvailableMetaStorePostgreSql defines model for AvailableMetaStorePostgreSql.
type AvailableMetaStorePostgreSql struct {
	MaximumSizeGiB int                      `json:"maximumSizeGiB"`
	Nodes          []AvailableComponentType `json:"nodes"`
}

// AvailableMetaStoreSharingPg defines model for AvailableMetaStoreSharingPg.
type AvailableMetaStoreSharingPg struct {
	Enabled *bool `json:"enabled,omitempty"`
}

// BackupSnapshotItem defines model for BackupSnapshotItem.
type BackupSnapshotItem struct {
	CreatedAtUnixMills int64              `json:"created_at_unix_mills"`
	Id                 openapi_types.UUID `json:"id"`
	MetaSnapshotId     *int64             `json:"meta_snapshot_id,omitempty"`
	RwVersion          *string            `json:"rw_version,omitempty"`
	Status             string             `json:"status"`
}

// BackupSnapshotsSizePage defines model for BackupSnapshotsSizePage.
type BackupSnapshotsSizePage struct {
	Items  []BackupSnapshotItem `json:"items"`
	Limit  uint64               `json:"limit"`
	Offset uint64               `json:"offset"`
	Size   uint64               `json:"size"`
}

// ClusterStatus defines model for ClusterStatus.
type ClusterStatus string

// ComponentResource defines model for ComponentResource.
type ComponentResource struct {
	ComponentTypeId string `json:"componentTypeId"`
	Cpu             string `json:"cpu"`
	Memory          string `json:"memory"`
	Replica         int    `json:"replica"`
}

// ComponentResourceRequest defines model for ComponentResourceRequest.
type ComponentResourceRequest struct {
	ComponentTypeId string `json:"componentTypeId"`
	Replica         int    `json:"replica"`
}

// CreateDBUserRequestBody defines model for CreateDBUserRequestBody.
type CreateDBUserRequestBody struct {
	Createdb   bool   `json:"createdb"`
	Createuser *bool  `json:"createuser,omitempty"`
	Password   string `json:"password"`
	Superuser  bool   `json:"superuser"`
	TenantId   uint64 `json:"tenantId"`
	Username   string `json:"username"`
}

// CreateTenantResponseBody defines model for CreateTenantResponseBody.
type CreateTenantResponseBody struct {
	TenantId   uint64 `json:"tenantId"`
	TenantName string `json:"tenantName"`
}

// DBUser defines model for DBUser.
type DBUser struct {
	Canlogin      bool   `json:"canlogin"`
	Usecreatedb   bool   `json:"usecreatedb"`
	Usecreateuser bool   `json:"usecreateuser"`
	Username      string `json:"username"`
	Usesuper      bool   `json:"usesuper"`
	Usesysid      uint64 `json:"usesysid"`
}

// DBUserArray defines model for DBUserArray.
type DBUserArray = []DBUser

// DBUsers defines model for DBUsers.
type DBUsers struct {
	Dbusers *DBUserArray `json:"dbusers,omitempty"`
}

// DropTenantRelationBody defines model for DropTenantRelationBody.
type DropTenantRelationBody struct {
	Database string `json:"database"`
	Password string `json:"password"`
	Relname  string `json:"relname"`
	Username string `json:"username"`
}

// Endpoint defines model for Endpoint.
type Endpoint struct {
	Database     string `json:"database"`
	Host         string `json:"host"`
	Id           int64  `json:"id"`
	InternalHost string `json:"internalHost"`
	InternalPort int    `json:"internalPort"`
	Options      string `json:"options"`
	Port         int    `json:"port"`
	TenantId     int64  `json:"tenantId"`
}

// ErrLogQueryResult defines model for ErrLogQueryResult.
type ErrLogQueryResult struct {
	Status string     `json:"status"`
	Values [][]string `json:"values"`
}

// GetDatabasesResponseBody defines model for GetDatabasesResponseBody.
type GetDatabasesResponseBody struct {
	Databases []string `json:"databases"`
}

// GetImageTagResponse defines model for GetImageTagResponse.
type GetImageTagResponse struct {
	ImageTag string `json:"imageTag"`
}

// KafkaConfig defines model for KafkaConfig.
type KafkaConfig struct {
	CaCertificate    *string                      `json:"caCertificate,omitempty"`
	SaslMechanisms   *KafkaConfigSaslMechanisms   `json:"saslMechanisms,omitempty"`
	SaslPassword     *string                      `json:"saslPassword,omitempty"`
	SaslUsername     *string                      `json:"saslUsername,omitempty"`
	SecurityProtocol *KafkaConfigSecurityProtocol `json:"securityProtocol,omitempty"`
	Server           string                       `json:"server"`
}

// KafkaConfigSaslMechanisms defines model for KafkaConfig.SaslMechanisms.
type KafkaConfigSaslMechanisms string

// KafkaConfigSecurityProtocol defines model for KafkaConfig.SecurityProtocol.
type KafkaConfigSecurityProtocol string

// LabelItem defines model for LabelItem.
type LabelItem struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

// ManagedCluster defines model for ManagedCluster.
type ManagedCluster struct {
	ClusterServiceAccount string             `json:"cluster_service_account"`
	Id                    uint64             `json:"id"`
	MasterUrl             string             `json:"master_url"`
	Name                  string             `json:"name"`
	Org                   openapi_types.UUID `json:"org"`
	ServingType           string             `json:"serving_type"`
	Settings              map[string]string  `json:"settings"`
	Status                ClusterStatus      `json:"status"`
	Token                 string             `json:"token"`
}

// ManagedClustersSizePage defines model for ManagedClustersSizePage.
type ManagedClustersSizePage struct {
	Clusters []ManagedCluster `json:"clusters"`
	Limit    uint64           `json:"limit"`
	Offset   uint64           `json:"offset"`
	Size     uint64           `json:"size"`
}

// MetaStoreAwsRds defines model for MetaStoreAwsRds.
type MetaStoreAwsRds struct {
	InstanceClass string `json:"instanceClass"`
	SizeGb        int    `json:"sizeGb"`
}

// MetaStoreAzrPostgres defines model for MetaStoreAzrPostgres.
type MetaStoreAzrPostgres struct {
	SizeGb int    `json:"sizeGb"`
	Sku    string `json:"sku"`
}

// MetaStoreEtcd defines model for MetaStoreEtcd.
type MetaStoreEtcd struct {
	Resource ComponentResource `json:"resource"`
	SizeGb   int               `json:"sizeGb"`
}

// MetaStoreGcpCloudSql defines model for MetaStoreGcpCloudSql.
type MetaStoreGcpCloudSql struct {
	SizeGb int    `json:"sizeGb"`
	Tier   string `json:"tier"`
}

// MetaStorePostgreSql defines model for MetaStorePostgreSql.
type MetaStorePostgreSql struct {
	Resource ComponentResource `json:"resource"`
	SizeGb   int               `json:"sizeGb"`
}

// MetaStoreSharingPg defines model for MetaStoreSharingPg.
type MetaStoreSharingPg struct {
	InstanceId string `json:"instanceId"`
}

// MetaStoreType defines model for MetaStoreType.
type MetaStoreType string

// MetricItem defines model for MetricItem.
type MetricItem struct {
	Labels []LabelItem   `json:"labels"`
	Values []MetricPoint `json:"values"`
}

// MetricPoint defines model for MetricPoint.
type MetricPoint struct {
	Timestamp time.Time `json:"timestamp"`
	Value     float64   `json:"value"`
}

// Metrics defines model for Metrics.
type Metrics struct {
	Items []MetricItem `json:"items"`
	Name  string       `json:"name"`
}

// Page defines model for Page.
type Page struct {
	Limit  uint64 `json:"limit"`
	Offset uint64 `json:"offset"`
}

// PostByocClusterUpdateRequestBody defines model for PostByocClusterUpdateRequestBody.
type PostByocClusterUpdateRequestBody struct {
	// CustomSettings base64 encoded custom settings
	CustomSettings *string `json:"customSettings,omitempty"`
	Version        *string `json:"version,omitempty"`
}

// PostByocClustersRequestBody defines model for PostByocClustersRequestBody.
type PostByocClustersRequestBody struct {
	Name     string            `json:"name"`
	Settings map[string]string `json:"settings"`
	Version  *string           `json:"version,omitempty"`
}

// PostPrivateLinkRequestBody defines model for PostPrivateLinkRequestBody.
type PostPrivateLinkRequestBody struct {
	ConnectionName string `json:"connectionName"`
	Target         string `json:"target"`
}

// PostPrivateLinkResponseBody defines model for PostPrivateLinkResponseBody.
type PostPrivateLinkResponseBody struct {
	ConnectionName string             `json:"connectionName"`
	Id             openapi_types.UUID `json:"id"`
}

// PostSnapshotResponseBody defines model for PostSnapshotResponseBody.
type PostSnapshotResponseBody struct {
	SnapshotId openapi_types.UUID `json:"snapshotId"`
	WorkflowId openapi_types.UUID `json:"workflowId"`
}

// PostSourceRequestBody defines model for PostSourceRequestBody.
type PostSourceRequestBody struct {
	Config PostSourceRequestBody_Config `json:"config"`
	Type   PostSourceRequestBodyType    `json:"type"`
}

// PostSourceRequestBody_Config defines model for PostSourceRequestBody.Config.
type PostSourceRequestBody_Config struct {
	union json.RawMessage
}

// PostSourceRequestBodyType defines model for PostSourceRequestBody.Type.
type PostSourceRequestBodyType string

// PostSourcesFetchKafkaTopicRequestBody defines model for PostSourcesFetchKafkaTopicRequestBody.
type PostSourcesFetchKafkaTopicRequestBody struct {
	KafkaConfig KafkaConfig `json:"kafka_config"`
}

// PostSourcesFetchKafkaTopicResponseBody defines model for PostSourcesFetchKafkaTopicResponseBody.
type PostSourcesFetchKafkaTopicResponseBody struct {
	Topics []string `json:"topics"`
}

// PostTenantResourcesRequestBody defines model for PostTenantResourcesRequestBody.
type PostTenantResourcesRequestBody struct {
	Compactor  *ComponentResourceRequest `json:"compactor,omitempty"`
	Compute    *ComponentResourceRequest `json:"compute,omitempty"`
	Frontend   *ComponentResourceRequest `json:"frontend,omitempty"`
	Meta       *ComponentResourceRequest `json:"meta,omitempty"`
	Standalone *ComponentResourceRequest `json:"standalone,omitempty"`
}

// PrivateLink defines model for PrivateLink.
type PrivateLink struct {
	ConnectionName  string                     `json:"connectionName"`
	ConnectionState PrivateLinkConnectionState `json:"connectionState"`
	Endpoint        *string                    `json:"endpoint,omitempty"`
	Id              openapi_types.UUID         `json:"id"`
	Status          PrivateLinkStatus          `json:"status"`
	Target          *string                    `json:"target,omitempty"`
	TenantId        int64                      `json:"tenantId"`
}

// PrivateLinkConnectionState defines model for PrivateLink.ConnectionState.
type PrivateLinkConnectionState string

// PrivateLinkStatus defines model for PrivateLink.Status.
type PrivateLinkStatus string

// PutByocClusterRequestBody defines model for PutByocClusterRequestBody.
type PutByocClusterRequestBody struct {
	Name     string            `json:"name"`
	Settings map[string]string `json:"settings"`
	Status   ClusterStatus     `json:"status"`
}

// RelationCounts defines model for RelationCounts.
type RelationCounts struct {
	MaterializedView uint32 `json:"materialized_view"`
	Sink             uint32 `json:"sink"`
	Source           uint32 `json:"source"`
	Table            uint32 `json:"table"`
}

// Size defines model for Size.
type Size struct {
	Size uint64 `json:"size"`
}

// SqlExecutionRequestBody defines model for SqlExecutionRequestBody.
type SqlExecutionRequestBody struct {
	Database string `json:"database"`
	Password string `json:"password"`
	Query    string `json:"query"`

	// Timeout timeout in ms
	Timeout  *int   `json:"timeout,omitempty"`
	Username string `json:"username"`
}

// Tenant defines model for Tenant.
type Tenant struct {
	ClusterName          *string            `json:"clusterName,omitempty"`
	CreatedAt            time.Time          `json:"createdAt"`
	EtcdConfig           string             `json:"etcd_config"`
	HealthStatus         TenantHealthStatus `json:"health_status"`
	Id                   uint64             `json:"id"`
	ImageTag             string             `json:"imageTag"`
	LatestImageTag       string             `json:"latestImageTag"`
	NsId                 openapi_types.UUID `json:"nsId"`
	OrgId                openapi_types.UUID `json:"orgId"`
	Region               string             `json:"region"`
	Resources            TenantResource     `json:"resources"`
	RwConfig             string             `json:"rw_config"`
	Status               TenantStatus       `json:"status"`
	TenantName           string             `json:"tenantName"`
	Tier                 TierId             `json:"tier"`
	UpcomingSnapshotTime *time.Time         `json:"upcomingSnapshotTime,omitempty"`
	UpdatedAt            time.Time          `json:"updatedAt"`
	UsageType            TenantUsageType    `json:"usageType"`
	UserId               uint64             `json:"userId"`
}

// TenantHealthStatus defines model for Tenant.HealthStatus.
type TenantHealthStatus string

// TenantStatus defines model for Tenant.Status.
type TenantStatus string

// TenantUsageType defines model for Tenant.UsageType.
type TenantUsageType string

// TenantArray defines model for TenantArray.
type TenantArray = []Tenant

// TenantClusterInfoResponseBody defines model for TenantClusterInfoResponseBody.
type TenantClusterInfoResponseBody struct {
	ClusterInfo string `json:"clusterInfo"`
	TenantId    uint64 `json:"tenantId"`
	TenantName  string `json:"tenantName"`
}

// TenantRequestRequestBody defines model for TenantRequestRequestBody.
type TenantRequestRequestBody struct {
	ClusterName *string                `json:"clusterName,omitempty"`
	ConfigId    *openapi_types.UUID    `json:"configId,omitempty"`
	EtcdConfig  *string                `json:"etcdConfig,omitempty"`
	ImageTag    *string                `json:"imageTag,omitempty"`
	Resources   *TenantResourceRequest `json:"resources,omitempty"`

	// RwConfig if config ID is not provided, use this config. currently used in tf plugin
	RwConfig   *string                            `json:"rwConfig,omitempty"`
	Sku        *string                            `json:"sku,omitempty"`
	TenantName string                             `json:"tenantName"`
	Tier       *TierId                            `json:"tier,omitempty"`
	UsageType  *TenantRequestRequestBodyUsageType `json:"usageType,omitempty"`
}

// TenantRequestRequestBodyUsageType defines model for TenantRequestRequestBody.UsageType.
type TenantRequestRequestBodyUsageType string

// TenantResource defines model for TenantResource.
type TenantResource struct {
	Components        TenantResourceComponents   `json:"components"`
	ComputeCache      TenantResourceComputeCache `json:"computeCache"`
	EtcdVolumeSizeGiB *int                       `json:"etcdVolumeSizeGiB,omitempty"`
	MetaStore         *TenantResourceMetaStore   `json:"metaStore,omitempty"`
}

// TenantResourceComponents defines model for TenantResourceComponents.
type TenantResourceComponents struct {
	Compactor  *ComponentResource `json:"compactor,omitempty"`
	Compute    *ComponentResource `json:"compute,omitempty"`
	Etcd       *ComponentResource `json:"etcd,omitempty"`
	Frontend   *ComponentResource `json:"frontend,omitempty"`
	Meta       *ComponentResource `json:"meta,omitempty"`
	Standalone *ComponentResource `json:"standalone,omitempty"`
}

// TenantResourceComputeCache defines model for TenantResourceComputeCache.
type TenantResourceComputeCache struct {
	SizeGb int `json:"sizeGb"`
}

// TenantResourceMetaStore defines model for TenantResourceMetaStore.
type TenantResourceMetaStore struct {
	AwsRds      *MetaStoreAwsRds      `json:"aws_rds,omitempty"`
	AzrPostgres *MetaStoreAzrPostgres `json:"azr_postgres,omitempty"`
	Etcd        *MetaStoreEtcd        `json:"etcd,omitempty"`
	GcpCloudsql *MetaStoreGcpCloudSql `json:"gcp_cloudsql,omitempty"`
	Postgresql  *MetaStorePostgreSql  `json:"postgresql,omitempty"`
	Rwu         string                `json:"rwu"`
	SharingPg   *MetaStoreSharingPg   `json:"sharing_pg,omitempty"`
	Type        MetaStoreType         `json:"type"`
}

// TenantResourceRequest defines model for TenantResourceRequest.
type TenantResourceRequest struct {
	Components              TenantResourceRequestComponents `json:"components"`
	ComputeFileCacheSizeGiB int                             `json:"computeFileCacheSizeGiB"`
	EtcdVolumeSizeGiB       *int                            `json:"etcdVolumeSizeGiB,omitempty"`
	MetaStore               *TenantResourceRequestMetaStore `json:"metaStore,omitempty"`
}

// TenantResourceRequestComponents defines model for TenantResourceRequestComponents.
type TenantResourceRequestComponents struct {
	Compactor  *ComponentResourceRequest `json:"compactor,omitempty"`
	Compute    *ComponentResourceRequest `json:"compute,omitempty"`
	Etcd       *ComponentResourceRequest `json:"etcd,omitempty"`
	Frontend   *ComponentResourceRequest `json:"frontend,omitempty"`
	Meta       *ComponentResourceRequest `json:"meta,omitempty"`
	Standalone *ComponentResourceRequest `json:"standalone,omitempty"`
}

// TenantResourceRequestMetaStore defines model for TenantResourceRequestMetaStore.
type TenantResourceRequestMetaStore struct {
	AwsRds     *TenantResourceRequestMetaStoreAwsRds     `json:"aws_rds,omitempty"`
	Etcd       *TenantResourceRequestMetaStoreEtcd       `json:"etcd,omitempty"`
	Postgresql *TenantResourceRequestMetaStorePostgreSql `json:"postgresql,omitempty"`
	Type       MetaStoreType                             `json:"type"`
}

// TenantResourceRequestMetaStoreAwsRds defines model for TenantResourceRequestMetaStoreAwsRds.
type TenantResourceRequestMetaStoreAwsRds struct {
	InstanceClass string `json:"instanceClass"`
	SizeGb        int    `json:"sizeGb"`
}

// TenantResourceRequestMetaStoreEtcd defines model for TenantResourceRequestMetaStoreEtcd.
type TenantResourceRequestMetaStoreEtcd struct {
	ComponentTypeId string `json:"componentTypeId"`
	Replica         int    `json:"replica"`
	SizeGb          int    `json:"sizeGb"`
}

// TenantResourceRequestMetaStorePostgreSql defines model for TenantResourceRequestMetaStorePostgreSql.
type TenantResourceRequestMetaStorePostgreSql struct {
	ComponentTypeId string `json:"componentTypeId"`
	Replica         int    `json:"replica"`
	SizeGb          int    `json:"sizeGb"`
}

// TenantSizePage defines model for TenantSizePage.
type TenantSizePage struct {
	Limit   uint64      `json:"limit"`
	Offset  uint64      `json:"offset"`
	Size    uint64      `json:"size"`
	Tenants TenantArray `json:"tenants"`
}

// TenantStatusCount defines model for TenantStatusCount.
type TenantStatusCount struct {
	Count  int64  `json:"count"`
	Status string `json:"status"`
}

// TenantStatusCountArray defines model for TenantStatusCountArray.
type TenantStatusCountArray = []TenantStatusCount

// TenantStatusCounts defines model for TenantStatusCounts.
type TenantStatusCounts struct {
	Status TenantStatusCountArray `json:"status"`
}

// Tier defines model for Tier.
type Tier struct {
	AvailableCompactorNodes            []AvailableComponentType `json:"availableCompactorNodes"`
	AvailableComputeNodes              []AvailableComponentType `json:"availableComputeNodes"`
	AvailableFrontendNodes             []AvailableComponentType `json:"availableFrontendNodes"`
	AvailableMetaNodes                 []AvailableComponentType `json:"availableMetaNodes"`
	AvailableMetaStore                 *AvailableMetaStore      `json:"availableMetaStore,omitempty"`
	AvailableStandaloneNodes           []AvailableComponentType `json:"availableStandaloneNodes"`
	Id                                 *TierId                  `json:"id,omitempty"`
	MaximumComputeNodeFileCacheSizeGiB int                      `json:"maximumComputeNodeFileCacheSizeGiB"`
	RetentionPeriod                    int                      `json:"retentionPeriod"`
	ValidityPeriod                     int                      `json:"validityPeriod"`
}

// TierArray defines model for TierArray.
type TierArray = []Tier

// TierId defines model for TierId.
type TierId string

// Tiers defines model for Tiers.
type Tiers struct {
	Tiers TierArray `json:"tiers"`
}

// UpdateDBUserRequestBody defines model for UpdateDBUserRequestBody.
type UpdateDBUserRequestBody struct {
	Password string `json:"password"`
	TenantId uint64 `json:"tenantId"`
	Username string `json:"username"`
}

// WorkflowIdResponseBody defines model for WorkflowIdResponseBody.
type WorkflowIdResponseBody struct {
	WorkflowId openapi_types.UUID `json:"workflowId"`
}

// AvailabilityResponse defines model for AvailabilityResponse.
type AvailabilityResponse struct {
	Available bool   `json:"available"`
	Msg       string `json:"msg"`
}

// BadRequestResponse defines model for BadRequestResponse.
type BadRequestResponse struct {
	Msg string `json:"msg"`
}

// DefaultResponse defines model for DefaultResponse.
type DefaultResponse struct {
	Msg string `json:"msg"`
}

// FailedPreconditionResponse defines model for FailedPreconditionResponse.
type FailedPreconditionResponse struct {
	Msg string `json:"msg"`
}

// NotFoundResponse defines model for NotFoundResponse.
type NotFoundResponse struct {
	Msg string `json:"msg"`
}

// RangeNotSatisfiable defines model for RangeNotSatisfiable.
type RangeNotSatisfiable struct {
	Msg string `json:"msg"`
}

// ServiceUnavailableResponse defines model for ServiceUnavailableResponse.
type ServiceUnavailableResponse struct {
	Msg string `json:"msg"`
}

// GetAvailabilityParams defines parameters for GetAvailability.
type GetAvailabilityParams struct {
	TenantName string `form:"tenantName" json:"tenantName"`
}

// GetByocClustersParams defines parameters for GetByocClusters.
type GetByocClustersParams struct {
	Offset *uint64 `form:"offset,omitempty" json:"offset,omitempty"`
	Limit  *uint64 `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetEndpointsParams defines parameters for GetEndpoints.
type GetEndpointsParams struct {
	TenantName *string `form:"tenantName,omitempty" json:"tenantName,omitempty"`
	TenantId   *uint64 `form:"tenantId,omitempty" json:"tenantId,omitempty"`
}

// QueryErrLogParams defines parameters for QueryErrLog.
type QueryErrLogParams struct {
	TenantId  uint64                      `form:"tenantId" json:"tenantId"`
	Target    QueryErrLogParamsTarget     `form:"target" json:"target"`
	TargetId  string                      `form:"targetId" json:"targetId"`
	Start     *time.Time                  `form:"start,omitempty" json:"start,omitempty"`
	End       *time.Time                  `form:"end,omitempty" json:"end,omitempty"`
	Direction *QueryErrLogParamsDirection `form:"direction,omitempty" json:"direction,omitempty"`
	Limit     *uint64                     `form:"limit,omitempty" json:"limit,omitempty"`
}

// QueryErrLogParamsTarget defines parameters for QueryErrLog.
type QueryErrLogParamsTarget string

// QueryErrLogParamsDirection defines parameters for QueryErrLog.
type QueryErrLogParamsDirection string

// GetMetricsParams defines parameters for GetMetrics.
type GetMetricsParams struct {
	Metric    string     `form:"metric" json:"metric"`
	TenantId  uint64     `form:"tenantId" json:"tenantId"`
	Component *string    `form:"component,omitempty" json:"component,omitempty"`
	StartTime *time.Time `form:"startTime,omitempty" json:"startTime,omitempty"`
	EndTime   *time.Time `form:"endTime,omitempty" json:"endTime,omitempty"`
	Period    *uint64    `form:"period,omitempty" json:"period,omitempty"`
}

// DeleteTenantParams defines parameters for DeleteTenant.
type DeleteTenantParams struct {
	TenantId   *uint64 `form:"tenantId,omitempty" json:"tenantId,omitempty"`
	TenantName *string `form:"tenantName,omitempty" json:"tenantName,omitempty"`
}

// GetTenantParams defines parameters for GetTenant.
type GetTenantParams struct {
	TenantId   *uint64 `form:"tenantId,omitempty" json:"tenantId,omitempty"`
	TenantName *string `form:"tenantName,omitempty" json:"tenantName,omitempty"`
}

// DeleteTenantDbusersParams defines parameters for DeleteTenantDbusers.
type DeleteTenantDbusersParams struct {
	TenantId uint64 `form:"tenantId" json:"tenantId"`
	Username string `form:"username" json:"username"`
}

// GetTenantDbusersParams defines parameters for GetTenantDbusers.
type GetTenantDbusersParams struct {
	TenantId uint64 `form:"tenantId" json:"tenantId"`
}

// GetTenantTenantIdBackupParams defines parameters for GetTenantTenantIdBackup.
type GetTenantTenantIdBackupParams struct {
	Offset *uint64 `form:"offset,omitempty" json:"offset,omitempty"`
	Limit  *uint64 `form:"limit,omitempty" json:"limit,omitempty"`
}

// PutTenantTenantIdConfigEtcdTextBody defines parameters for PutTenantTenantIdConfigEtcd.
type PutTenantTenantIdConfigEtcdTextBody = string

// PutTenantTenantIdConfigRisingwaveTextBody defines parameters for PutTenantTenantIdConfigRisingwave.
type PutTenantTenantIdConfigRisingwaveTextBody = string

// PostTenantTenantIdExtensionsServerlessbackfillVersionTextBody defines parameters for PostTenantTenantIdExtensionsServerlessbackfillVersion.
type PostTenantTenantIdExtensionsServerlessbackfillVersionTextBody = string

// PostTenantTenantIdUpdateVersionJSONBody defines parameters for PostTenantTenantIdUpdateVersion.
type PostTenantTenantIdUpdateVersionJSONBody struct {
	Version *string `json:"version,omitempty"`
}

// GetTenantsParams defines parameters for GetTenants.
type GetTenantsParams struct {
	Offset *uint64 `form:"offset,omitempty" json:"offset,omitempty"`
	Limit  *uint64 `form:"limit,omitempty" json:"limit,omitempty"`
}

// PutByocClusterNameJSONRequestBody defines body for PutByocClusterName for application/json ContentType.
type PutByocClusterNameJSONRequestBody = PutByocClusterRequestBody

// PostByocClusterNameManualUpdateJSONRequestBody defines body for PostByocClusterNameManualUpdate for application/json ContentType.
type PostByocClusterNameManualUpdateJSONRequestBody = PostByocClusterUpdateRequestBody

// PostByocClusterNameUpdateJSONRequestBody defines body for PostByocClusterNameUpdate for application/json ContentType.
type PostByocClusterNameUpdateJSONRequestBody = PostByocClusterUpdateRequestBody

// PostByocClustersJSONRequestBody defines body for PostByocClusters for application/json ContentType.
type PostByocClustersJSONRequestBody = PostByocClustersRequestBody

// PostSourcePingJSONRequestBody defines body for PostSourcePing for application/json ContentType.
type PostSourcePingJSONRequestBody = PostSourceRequestBody

// PostTenantDbusersJSONRequestBody defines body for PostTenantDbusers for application/json ContentType.
type PostTenantDbusersJSONRequestBody = CreateDBUserRequestBody

// PutTenantDbusersJSONRequestBody defines body for PutTenantDbusers for application/json ContentType.
type PutTenantDbusersJSONRequestBody = UpdateDBUserRequestBody

// PutTenantTenantIdConfigEtcdTextRequestBody defines body for PutTenantTenantIdConfigEtcd for text/plain ContentType.
type PutTenantTenantIdConfigEtcdTextRequestBody = PutTenantTenantIdConfigEtcdTextBody

// PutTenantTenantIdConfigRisingwaveTextRequestBody defines body for PutTenantTenantIdConfigRisingwave for text/plain ContentType.
type PutTenantTenantIdConfigRisingwaveTextRequestBody = PutTenantTenantIdConfigRisingwaveTextBody

// PostTenantTenantIdExecutionJSONRequestBody defines body for PostTenantTenantIdExecution for application/json ContentType.
type PostTenantTenantIdExecutionJSONRequestBody = SqlExecutionRequestBody

// PostTenantTenantIdExtensionsServerlessbackfillVersionTextRequestBody defines body for PostTenantTenantIdExtensionsServerlessbackfillVersion for text/plain ContentType.
type PostTenantTenantIdExtensionsServerlessbackfillVersionTextRequestBody = PostTenantTenantIdExtensionsServerlessbackfillVersionTextBody

// PostTenantTenantIdPrivatelinksJSONRequestBody defines body for PostTenantTenantIdPrivatelinks for application/json ContentType.
type PostTenantTenantIdPrivatelinksJSONRequestBody = PostPrivateLinkRequestBody

// DeleteTenantTenantIdRelationJSONRequestBody defines body for DeleteTenantTenantIdRelation for application/json ContentType.
type DeleteTenantTenantIdRelationJSONRequestBody = DropTenantRelationBody

// PostTenantTenantIdResourceJSONRequestBody defines body for PostTenantTenantIdResource for application/json ContentType.
type PostTenantTenantIdResourceJSONRequestBody = PostTenantResourcesRequestBody

// PostTenantTenantIdSourcesFetchKafkaTopicJSONRequestBody defines body for PostTenantTenantIdSourcesFetchKafkaTopic for application/json ContentType.
type PostTenantTenantIdSourcesFetchKafkaTopicJSONRequestBody = PostSourcesFetchKafkaTopicRequestBody

// PostTenantTenantIdSourcesPingJSONRequestBody defines body for PostTenantTenantIdSourcesPing for application/json ContentType.
type PostTenantTenantIdSourcesPingJSONRequestBody = PostSourceRequestBody

// PostTenantTenantIdUpdateVersionJSONRequestBody defines body for PostTenantTenantIdUpdateVersion for application/json ContentType.
type PostTenantTenantIdUpdateVersionJSONRequestBody PostTenantTenantIdUpdateVersionJSONBody

// PostTenantsJSONRequestBody defines body for PostTenants for application/json ContentType.
type PostTenantsJSONRequestBody = TenantRequestRequestBody

// AsKafkaConfig returns the union data inside the PostSourceRequestBody_Config as a KafkaConfig
func (t PostSourceRequestBody_Config) AsKafkaConfig() (KafkaConfig, error) {
	var body KafkaConfig
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromKafkaConfig overwrites any union data inside the PostSourceRequestBody_Config as the provided KafkaConfig
func (t *PostSourceRequestBody_Config) FromKafkaConfig(v KafkaConfig) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeKafkaConfig performs a merge with any union data inside the PostSourceRequestBody_Config, using the provided KafkaConfig
func (t *PostSourceRequestBody_Config) MergeKafkaConfig(v KafkaConfig) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t PostSourceRequestBody_Config) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *PostSourceRequestBody_Config) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}

// RequestEditorFn  is the function signature for the RequestEditor callback function
type RequestEditorFn func(ctx context.Context, req *http.Request) error

// Doer performs HTTP requests.
//
// The standard http.Client implements this interface.
type HttpRequestDoer interface {
	Do(req *http.Request) (*http.Response, error)
}

// Client which conforms to the OpenAPI3 specification for this service.
type Client struct {
	// The endpoint of the server conforming to this interface, with scheme,
	// https://api.deepmap.com for example. This can contain a path relative
	// to the server, such as https://api.deepmap.com/dev-test, and all the
	// paths in the swagger spec will be appended to the server.
	Server string

	// Doer for performing requests, typically a *http.Client with any
	// customized settings, such as certificate chains.
	Client HttpRequestDoer

	// A list of callbacks for modifying requests which are generated before sending over
	// the network.
	RequestEditors []RequestEditorFn
}

// ClientOption allows setting custom parameters during construction
type ClientOption func(*Client) error

// Creates a new Client, with reasonable defaults
func NewClient(server string, opts ...ClientOption) (*Client, error) {
	// create a client with sane default values
	client := Client{
		Server: server,
	}
	// mutate client and add all optional params
	for _, o := range opts {
		if err := o(&client); err != nil {
			return nil, err
		}
	}
	// ensure the server URL always has a trailing slash
	if !strings.HasSuffix(client.Server, "/") {
		client.Server += "/"
	}
	// create httpClient, if not already present
	if client.Client == nil {
		client.Client = &http.Client{}
	}
	return &client, nil
}

// WithHTTPClient allows overriding the default Doer, which is
// automatically created using http.Client. This is useful for tests.
func WithHTTPClient(doer HttpRequestDoer) ClientOption {
	return func(c *Client) error {
		c.Client = doer
		return nil
	}
}

// WithRequestEditorFn allows setting up a callback function, which will be
// called right before sending the request. This can be used to mutate the request.
func WithRequestEditorFn(fn RequestEditorFn) ClientOption {
	return func(c *Client) error {
		c.RequestEditors = append(c.RequestEditors, fn)
		return nil
	}
}

// The interface specification for the client above.
type ClientInterface interface {
	// Get request
	Get(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetAvailability request
	GetAvailability(ctx context.Context, params *GetAvailabilityParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteByocClusterName request
	DeleteByocClusterName(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetByocClusterName request
	GetByocClusterName(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutByocClusterNameWithBody request with any body
	PutByocClusterNameWithBody(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutByocClusterName(ctx context.Context, name string, body PutByocClusterNameJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostByocClusterNameManualUpdateWithBody request with any body
	PostByocClusterNameManualUpdateWithBody(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostByocClusterNameManualUpdate(ctx context.Context, name string, body PostByocClusterNameManualUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostByocClusterNameUpdateWithBody request with any body
	PostByocClusterNameUpdateWithBody(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostByocClusterNameUpdate(ctx context.Context, name string, body PostByocClusterNameUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetByocClusters request
	GetByocClusters(ctx context.Context, params *GetByocClustersParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostByocClustersWithBody request with any body
	PostByocClustersWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostByocClusters(ctx context.Context, body PostByocClustersJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetEndpoints request
	GetEndpoints(ctx context.Context, params *GetEndpointsParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// QueryErrLog request
	QueryErrLog(ctx context.Context, params *QueryErrLogParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetMetrics request
	GetMetrics(ctx context.Context, params *GetMetricsParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetRootca request
	GetRootca(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostSourcePingWithBody request with any body
	PostSourcePingWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostSourcePing(ctx context.Context, body PostSourcePingJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteTenant request
	DeleteTenant(ctx context.Context, params *DeleteTenantParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenant request
	GetTenant(ctx context.Context, params *GetTenantParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteTenantDbusers request
	DeleteTenantDbusers(ctx context.Context, params *DeleteTenantDbusersParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantDbusers request
	GetTenantDbusers(ctx context.Context, params *GetTenantDbusersParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantDbusersWithBody request with any body
	PostTenantDbusersWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantDbusers(ctx context.Context, body PostTenantDbusersJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutTenantDbusersWithBody request with any body
	PutTenantDbusersWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutTenantDbusers(ctx context.Context, body PutTenantDbusersJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantTags request
	GetTenantTags(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantTenantIdBackup request
	GetTenantTenantIdBackup(ctx context.Context, tenantId uint64, params *GetTenantTenantIdBackupParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdBackup request
	PostTenantTenantIdBackup(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteTenantTenantIdBackupSnapshotId request
	DeleteTenantTenantIdBackupSnapshotId(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdBackupSnapshotIdRestore request
	PostTenantTenantIdBackupSnapshotIdRestore(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutTenantTenantIdConfigEtcdWithBody request with any body
	PutTenantTenantIdConfigEtcdWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutTenantTenantIdConfigEtcdWithTextBody(ctx context.Context, tenantId uint64, body PutTenantTenantIdConfigEtcdTextRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutTenantTenantIdConfigRisingwaveWithBody request with any body
	PutTenantTenantIdConfigRisingwaveWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutTenantTenantIdConfigRisingwaveWithTextBody(ctx context.Context, tenantId uint64, body PutTenantTenantIdConfigRisingwaveTextRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantTenantIdDatabases request
	GetTenantTenantIdDatabases(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdExecutionWithBody request with any body
	PostTenantTenantIdExecutionWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdExecution(ctx context.Context, tenantId uint64, body PostTenantTenantIdExecutionJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdExtensionsServerlessbackfillDisable request
	PostTenantTenantIdExtensionsServerlessbackfillDisable(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdExtensionsServerlessbackfillEnable request
	PostTenantTenantIdExtensionsServerlessbackfillEnable(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdExtensionsServerlessbackfillVersionWithBody request with any body
	PostTenantTenantIdExtensionsServerlessbackfillVersionWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdExtensionsServerlessbackfillVersionWithTextBody(ctx context.Context, tenantId uint64, body PostTenantTenantIdExtensionsServerlessbackfillVersionTextRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantTenantIdInfo request
	GetTenantTenantIdInfo(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantTenantIdMetrics request
	GetTenantTenantIdMetrics(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteTenantTenantIdPrivatelinkPrivateLinkId request
	DeleteTenantTenantIdPrivatelinkPrivateLinkId(ctx context.Context, tenantId uint64, privateLinkId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantTenantIdPrivatelinkPrivateLinkId request
	GetTenantTenantIdPrivatelinkPrivateLinkId(ctx context.Context, tenantId uint64, privateLinkId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdPrivatelinksWithBody request with any body
	PostTenantTenantIdPrivatelinksWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdPrivatelinks(ctx context.Context, tenantId uint64, body PostTenantTenantIdPrivatelinksJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteTenantTenantIdRelationWithBody request with any body
	DeleteTenantTenantIdRelationWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	DeleteTenantTenantIdRelation(ctx context.Context, tenantId uint64, body DeleteTenantTenantIdRelationJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantTenantIdRelationsGetCounts request
	GetTenantTenantIdRelationsGetCounts(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdResourceWithBody request with any body
	PostTenantTenantIdResourceWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdResource(ctx context.Context, tenantId uint64, body PostTenantTenantIdResourceJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdSourcesFetchKafkaTopicWithBody request with any body
	PostTenantTenantIdSourcesFetchKafkaTopicWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdSourcesFetchKafkaTopic(ctx context.Context, tenantId uint64, body PostTenantTenantIdSourcesFetchKafkaTopicJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdSourcesPingWithBody request with any body
	PostTenantTenantIdSourcesPingWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdSourcesPing(ctx context.Context, tenantId uint64, body PostTenantTenantIdSourcesPingJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutTenantTenantIdStart request
	PutTenantTenantIdStart(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutTenantTenantIdStop request
	PutTenantTenantIdStop(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutTenantTenantIdUpdateCfgConfigId request
	PutTenantTenantIdUpdateCfgConfigId(ctx context.Context, tenantId uint64, configId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantTenantIdUpdateVersionWithBody request with any body
	PostTenantTenantIdUpdateVersionWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenantTenantIdUpdateVersion(ctx context.Context, tenantId uint64, body PostTenantTenantIdUpdateVersionJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenants request
	GetTenants(ctx context.Context, params *GetTenantsParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostTenantsWithBody request with any body
	PostTenantsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostTenants(ctx context.Context, body PostTenantsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTenantsStatus request
	GetTenantsStatus(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTiers request
	GetTiers(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetVersion request
	GetVersion(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)
}

func (c *Client) Get(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetAvailability(ctx context.Context, params *GetAvailabilityParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetAvailabilityRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteByocClusterName(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteByocClusterNameRequest(c.Server, name)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetByocClusterName(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetByocClusterNameRequest(c.Server, name)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutByocClusterNameWithBody(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutByocClusterNameRequestWithBody(c.Server, name, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutByocClusterName(ctx context.Context, name string, body PutByocClusterNameJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutByocClusterNameRequest(c.Server, name, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostByocClusterNameManualUpdateWithBody(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostByocClusterNameManualUpdateRequestWithBody(c.Server, name, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostByocClusterNameManualUpdate(ctx context.Context, name string, body PostByocClusterNameManualUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostByocClusterNameManualUpdateRequest(c.Server, name, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostByocClusterNameUpdateWithBody(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostByocClusterNameUpdateRequestWithBody(c.Server, name, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostByocClusterNameUpdate(ctx context.Context, name string, body PostByocClusterNameUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostByocClusterNameUpdateRequest(c.Server, name, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetByocClusters(ctx context.Context, params *GetByocClustersParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetByocClustersRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostByocClustersWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostByocClustersRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostByocClusters(ctx context.Context, body PostByocClustersJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostByocClustersRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetEndpoints(ctx context.Context, params *GetEndpointsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetEndpointsRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) QueryErrLog(ctx context.Context, params *QueryErrLogParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewQueryErrLogRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetMetrics(ctx context.Context, params *GetMetricsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetMetricsRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetRootca(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetRootcaRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostSourcePingWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostSourcePingRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostSourcePing(ctx context.Context, body PostSourcePingJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostSourcePingRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteTenant(ctx context.Context, params *DeleteTenantParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteTenantRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenant(ctx context.Context, params *GetTenantParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteTenantDbusers(ctx context.Context, params *DeleteTenantDbusersParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteTenantDbusersRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantDbusers(ctx context.Context, params *GetTenantDbusersParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantDbusersRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantDbusersWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantDbusersRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantDbusers(ctx context.Context, body PostTenantDbusersJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantDbusersRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantDbusersWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantDbusersRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantDbusers(ctx context.Context, body PutTenantDbusersJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantDbusersRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantTags(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantTagsRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantTenantIdBackup(ctx context.Context, tenantId uint64, params *GetTenantTenantIdBackupParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantTenantIdBackupRequest(c.Server, tenantId, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdBackup(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdBackupRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteTenantTenantIdBackupSnapshotId(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteTenantTenantIdBackupSnapshotIdRequest(c.Server, tenantId, snapshotId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdBackupSnapshotIdRestore(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdBackupSnapshotIdRestoreRequest(c.Server, tenantId, snapshotId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantTenantIdConfigEtcdWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantTenantIdConfigEtcdRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantTenantIdConfigEtcdWithTextBody(ctx context.Context, tenantId uint64, body PutTenantTenantIdConfigEtcdTextRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantTenantIdConfigEtcdRequestWithTextBody(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantTenantIdConfigRisingwaveWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantTenantIdConfigRisingwaveRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantTenantIdConfigRisingwaveWithTextBody(ctx context.Context, tenantId uint64, body PutTenantTenantIdConfigRisingwaveTextRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantTenantIdConfigRisingwaveRequestWithTextBody(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantTenantIdDatabases(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantTenantIdDatabasesRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExecutionWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExecutionRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExecution(ctx context.Context, tenantId uint64, body PostTenantTenantIdExecutionJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExecutionRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExtensionsServerlessbackfillDisable(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExtensionsServerlessbackfillDisableRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExtensionsServerlessbackfillEnable(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExtensionsServerlessbackfillEnableRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExtensionsServerlessbackfillVersionWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExtensionsServerlessbackfillVersionRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdExtensionsServerlessbackfillVersionWithTextBody(ctx context.Context, tenantId uint64, body PostTenantTenantIdExtensionsServerlessbackfillVersionTextRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdExtensionsServerlessbackfillVersionRequestWithTextBody(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantTenantIdInfo(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantTenantIdInfoRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantTenantIdMetrics(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantTenantIdMetricsRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteTenantTenantIdPrivatelinkPrivateLinkId(ctx context.Context, tenantId uint64, privateLinkId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteTenantTenantIdPrivatelinkPrivateLinkIdRequest(c.Server, tenantId, privateLinkId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantTenantIdPrivatelinkPrivateLinkId(ctx context.Context, tenantId uint64, privateLinkId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantTenantIdPrivatelinkPrivateLinkIdRequest(c.Server, tenantId, privateLinkId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdPrivatelinksWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdPrivatelinksRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdPrivatelinks(ctx context.Context, tenantId uint64, body PostTenantTenantIdPrivatelinksJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdPrivatelinksRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteTenantTenantIdRelationWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteTenantTenantIdRelationRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteTenantTenantIdRelation(ctx context.Context, tenantId uint64, body DeleteTenantTenantIdRelationJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteTenantTenantIdRelationRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantTenantIdRelationsGetCounts(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantTenantIdRelationsGetCountsRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdResourceWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdResourceRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdResource(ctx context.Context, tenantId uint64, body PostTenantTenantIdResourceJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdResourceRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdSourcesFetchKafkaTopicWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdSourcesFetchKafkaTopicRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdSourcesFetchKafkaTopic(ctx context.Context, tenantId uint64, body PostTenantTenantIdSourcesFetchKafkaTopicJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdSourcesFetchKafkaTopicRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdSourcesPingWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdSourcesPingRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdSourcesPing(ctx context.Context, tenantId uint64, body PostTenantTenantIdSourcesPingJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdSourcesPingRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantTenantIdStart(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantTenantIdStartRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantTenantIdStop(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantTenantIdStopRequest(c.Server, tenantId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutTenantTenantIdUpdateCfgConfigId(ctx context.Context, tenantId uint64, configId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutTenantTenantIdUpdateCfgConfigIdRequest(c.Server, tenantId, configId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdUpdateVersionWithBody(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdUpdateVersionRequestWithBody(c.Server, tenantId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantTenantIdUpdateVersion(ctx context.Context, tenantId uint64, body PostTenantTenantIdUpdateVersionJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantTenantIdUpdateVersionRequest(c.Server, tenantId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenants(ctx context.Context, params *GetTenantsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantsRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenantsWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantsRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostTenants(ctx context.Context, body PostTenantsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostTenantsRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTenantsStatus(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTenantsStatusRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTiers(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTiersRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetVersion(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetVersionRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

// NewGetRequest generates requests for Get
func NewGetRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetAvailabilityRequest generates requests for GetAvailability
func NewGetAvailabilityRequest(server string, params *GetAvailabilityParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/availability")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantName", runtime.ParamLocationQuery, params.TenantName); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewDeleteByocClusterNameRequest generates requests for DeleteByocClusterName
func NewDeleteByocClusterNameRequest(server string, name string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "name", runtime.ParamLocationPath, name)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/byoc-cluster/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetByocClusterNameRequest generates requests for GetByocClusterName
func NewGetByocClusterNameRequest(server string, name string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "name", runtime.ParamLocationPath, name)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/byoc-cluster/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutByocClusterNameRequest calls the generic PutByocClusterName builder with application/json body
func NewPutByocClusterNameRequest(server string, name string, body PutByocClusterNameJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPutByocClusterNameRequestWithBody(server, name, "application/json", bodyReader)
}

// NewPutByocClusterNameRequestWithBody generates requests for PutByocClusterName with any type of body
func NewPutByocClusterNameRequestWithBody(server string, name string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "name", runtime.ParamLocationPath, name)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/byoc-cluster/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostByocClusterNameManualUpdateRequest calls the generic PostByocClusterNameManualUpdate builder with application/json body
func NewPostByocClusterNameManualUpdateRequest(server string, name string, body PostByocClusterNameManualUpdateJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostByocClusterNameManualUpdateRequestWithBody(server, name, "application/json", bodyReader)
}

// NewPostByocClusterNameManualUpdateRequestWithBody generates requests for PostByocClusterNameManualUpdate with any type of body
func NewPostByocClusterNameManualUpdateRequestWithBody(server string, name string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "name", runtime.ParamLocationPath, name)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/byoc-cluster/%s/manualUpdate", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostByocClusterNameUpdateRequest calls the generic PostByocClusterNameUpdate builder with application/json body
func NewPostByocClusterNameUpdateRequest(server string, name string, body PostByocClusterNameUpdateJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostByocClusterNameUpdateRequestWithBody(server, name, "application/json", bodyReader)
}

// NewPostByocClusterNameUpdateRequestWithBody generates requests for PostByocClusterNameUpdate with any type of body
func NewPostByocClusterNameUpdateRequestWithBody(server string, name string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "name", runtime.ParamLocationPath, name)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/byoc-cluster/%s/update", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetByocClustersRequest generates requests for GetByocClusters
func NewGetByocClustersRequest(server string, params *GetByocClustersParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/byoc-clusters")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostByocClustersRequest calls the generic PostByocClusters builder with application/json body
func NewPostByocClustersRequest(server string, body PostByocClustersJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostByocClustersRequestWithBody(server, "application/json", bodyReader)
}

// NewPostByocClustersRequestWithBody generates requests for PostByocClusters with any type of body
func NewPostByocClustersRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/byoc-clusters")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetEndpointsRequest generates requests for GetEndpoints
func NewGetEndpointsRequest(server string, params *GetEndpointsParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/endpoints")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.TenantName != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantName", runtime.ParamLocationQuery, *params.TenantName); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.TenantId != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantId", runtime.ParamLocationQuery, *params.TenantId); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewQueryErrLogRequest generates requests for QueryErrLog
func NewQueryErrLogRequest(server string, params *QueryErrLogParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/log/queryError")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantId", runtime.ParamLocationQuery, params.TenantId); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "target", runtime.ParamLocationQuery, params.Target); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "targetId", runtime.ParamLocationQuery, params.TargetId); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if params.Start != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "start", runtime.ParamLocationQuery, *params.Start); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.End != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "end", runtime.ParamLocationQuery, *params.End); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Direction != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "direction", runtime.ParamLocationQuery, *params.Direction); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetMetricsRequest generates requests for GetMetrics
func NewGetMetricsRequest(server string, params *GetMetricsParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/metrics")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "metric", runtime.ParamLocationQuery, params.Metric); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantId", runtime.ParamLocationQuery, params.TenantId); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if params.Component != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "component", runtime.ParamLocationQuery, *params.Component); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.StartTime != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "startTime", runtime.ParamLocationQuery, *params.StartTime); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.EndTime != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "endTime", runtime.ParamLocationQuery, *params.EndTime); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Period != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "period", runtime.ParamLocationQuery, *params.Period); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetRootcaRequest generates requests for GetRootca
func NewGetRootcaRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/rootca")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostSourcePingRequest calls the generic PostSourcePing builder with application/json body
func NewPostSourcePingRequest(server string, body PostSourcePingJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostSourcePingRequestWithBody(server, "application/json", bodyReader)
}

// NewPostSourcePingRequestWithBody generates requests for PostSourcePing with any type of body
func NewPostSourcePingRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/source/ping")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteTenantRequest generates requests for DeleteTenant
func NewDeleteTenantRequest(server string, params *DeleteTenantParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.TenantId != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantId", runtime.ParamLocationQuery, *params.TenantId); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.TenantName != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantName", runtime.ParamLocationQuery, *params.TenantName); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetTenantRequest generates requests for GetTenant
func NewGetTenantRequest(server string, params *GetTenantParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.TenantId != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantId", runtime.ParamLocationQuery, *params.TenantId); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.TenantName != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantName", runtime.ParamLocationQuery, *params.TenantName); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewDeleteTenantDbusersRequest generates requests for DeleteTenantDbusers
func NewDeleteTenantDbusersRequest(server string, params *DeleteTenantDbusersParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/dbusers")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantId", runtime.ParamLocationQuery, params.TenantId); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "username", runtime.ParamLocationQuery, params.Username); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetTenantDbusersRequest generates requests for GetTenantDbusers
func NewGetTenantDbusersRequest(server string, params *GetTenantDbusersParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/dbusers")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tenantId", runtime.ParamLocationQuery, params.TenantId); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantDbusersRequest calls the generic PostTenantDbusers builder with application/json body
func NewPostTenantDbusersRequest(server string, body PostTenantDbusersJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantDbusersRequestWithBody(server, "application/json", bodyReader)
}

// NewPostTenantDbusersRequestWithBody generates requests for PostTenantDbusers with any type of body
func NewPostTenantDbusersRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/dbusers")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPutTenantDbusersRequest calls the generic PutTenantDbusers builder with application/json body
func NewPutTenantDbusersRequest(server string, body PutTenantDbusersJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPutTenantDbusersRequestWithBody(server, "application/json", bodyReader)
}

// NewPutTenantDbusersRequestWithBody generates requests for PutTenantDbusers with any type of body
func NewPutTenantDbusersRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/dbusers")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetTenantTagsRequest generates requests for GetTenantTags
func NewGetTenantTagsRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/tags")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetTenantTenantIdBackupRequest generates requests for GetTenantTenantIdBackup
func NewGetTenantTenantIdBackupRequest(server string, tenantId uint64, params *GetTenantTenantIdBackupParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/backup", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdBackupRequest generates requests for PostTenantTenantIdBackup
func NewPostTenantTenantIdBackupRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/backup", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewDeleteTenantTenantIdBackupSnapshotIdRequest generates requests for DeleteTenantTenantIdBackupSnapshotId
func NewDeleteTenantTenantIdBackupSnapshotIdRequest(server string, tenantId uint64, snapshotId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "snapshotId", runtime.ParamLocationPath, snapshotId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/backup/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdBackupSnapshotIdRestoreRequest generates requests for PostTenantTenantIdBackupSnapshotIdRestore
func NewPostTenantTenantIdBackupSnapshotIdRestoreRequest(server string, tenantId uint64, snapshotId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "snapshotId", runtime.ParamLocationPath, snapshotId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/backup/%s/restore", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutTenantTenantIdConfigEtcdRequestWithTextBody calls the generic PutTenantTenantIdConfigEtcd builder with text/plain body
func NewPutTenantTenantIdConfigEtcdRequestWithTextBody(server string, tenantId uint64, body PutTenantTenantIdConfigEtcdTextRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	bodyReader = strings.NewReader(string(body))
	return NewPutTenantTenantIdConfigEtcdRequestWithBody(server, tenantId, "text/plain", bodyReader)
}

// NewPutTenantTenantIdConfigEtcdRequestWithBody generates requests for PutTenantTenantIdConfigEtcd with any type of body
func NewPutTenantTenantIdConfigEtcdRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/config/etcd", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPutTenantTenantIdConfigRisingwaveRequestWithTextBody calls the generic PutTenantTenantIdConfigRisingwave builder with text/plain body
func NewPutTenantTenantIdConfigRisingwaveRequestWithTextBody(server string, tenantId uint64, body PutTenantTenantIdConfigRisingwaveTextRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	bodyReader = strings.NewReader(string(body))
	return NewPutTenantTenantIdConfigRisingwaveRequestWithBody(server, tenantId, "text/plain", bodyReader)
}

// NewPutTenantTenantIdConfigRisingwaveRequestWithBody generates requests for PutTenantTenantIdConfigRisingwave with any type of body
func NewPutTenantTenantIdConfigRisingwaveRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/config/risingwave", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetTenantTenantIdDatabasesRequest generates requests for GetTenantTenantIdDatabases
func NewGetTenantTenantIdDatabasesRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/databases", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdExecutionRequest calls the generic PostTenantTenantIdExecution builder with application/json body
func NewPostTenantTenantIdExecutionRequest(server string, tenantId uint64, body PostTenantTenantIdExecutionJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdExecutionRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantTenantIdExecutionRequestWithBody generates requests for PostTenantTenantIdExecution with any type of body
func NewPostTenantTenantIdExecutionRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/execution", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostTenantTenantIdExtensionsServerlessbackfillDisableRequest generates requests for PostTenantTenantIdExtensionsServerlessbackfillDisable
func NewPostTenantTenantIdExtensionsServerlessbackfillDisableRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/extensions/serverlessbackfill/disable", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdExtensionsServerlessbackfillEnableRequest generates requests for PostTenantTenantIdExtensionsServerlessbackfillEnable
func NewPostTenantTenantIdExtensionsServerlessbackfillEnableRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/extensions/serverlessbackfill/enable", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdExtensionsServerlessbackfillVersionRequestWithTextBody calls the generic PostTenantTenantIdExtensionsServerlessbackfillVersion builder with text/plain body
func NewPostTenantTenantIdExtensionsServerlessbackfillVersionRequestWithTextBody(server string, tenantId uint64, body PostTenantTenantIdExtensionsServerlessbackfillVersionTextRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	bodyReader = strings.NewReader(string(body))
	return NewPostTenantTenantIdExtensionsServerlessbackfillVersionRequestWithBody(server, tenantId, "text/plain", bodyReader)
}

// NewPostTenantTenantIdExtensionsServerlessbackfillVersionRequestWithBody generates requests for PostTenantTenantIdExtensionsServerlessbackfillVersion with any type of body
func NewPostTenantTenantIdExtensionsServerlessbackfillVersionRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/extensions/serverlessbackfill/version", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetTenantTenantIdInfoRequest generates requests for GetTenantTenantIdInfo
func NewGetTenantTenantIdInfoRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/info", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetTenantTenantIdMetricsRequest generates requests for GetTenantTenantIdMetrics
func NewGetTenantTenantIdMetricsRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/metrics", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewDeleteTenantTenantIdPrivatelinkPrivateLinkIdRequest generates requests for DeleteTenantTenantIdPrivatelinkPrivateLinkId
func NewDeleteTenantTenantIdPrivatelinkPrivateLinkIdRequest(server string, tenantId uint64, privateLinkId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "privateLinkId", runtime.ParamLocationPath, privateLinkId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/privatelink/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetTenantTenantIdPrivatelinkPrivateLinkIdRequest generates requests for GetTenantTenantIdPrivatelinkPrivateLinkId
func NewGetTenantTenantIdPrivatelinkPrivateLinkIdRequest(server string, tenantId uint64, privateLinkId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "privateLinkId", runtime.ParamLocationPath, privateLinkId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/privatelink/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdPrivatelinksRequest calls the generic PostTenantTenantIdPrivatelinks builder with application/json body
func NewPostTenantTenantIdPrivatelinksRequest(server string, tenantId uint64, body PostTenantTenantIdPrivatelinksJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdPrivatelinksRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantTenantIdPrivatelinksRequestWithBody generates requests for PostTenantTenantIdPrivatelinks with any type of body
func NewPostTenantTenantIdPrivatelinksRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/privatelinks", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteTenantTenantIdRelationRequest calls the generic DeleteTenantTenantIdRelation builder with application/json body
func NewDeleteTenantTenantIdRelationRequest(server string, tenantId uint64, body DeleteTenantTenantIdRelationJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewDeleteTenantTenantIdRelationRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewDeleteTenantTenantIdRelationRequestWithBody generates requests for DeleteTenantTenantIdRelation with any type of body
func NewDeleteTenantTenantIdRelationRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/relation", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetTenantTenantIdRelationsGetCountsRequest generates requests for GetTenantTenantIdRelationsGetCounts
func NewGetTenantTenantIdRelationsGetCountsRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/relations/getCounts", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdResourceRequest calls the generic PostTenantTenantIdResource builder with application/json body
func NewPostTenantTenantIdResourceRequest(server string, tenantId uint64, body PostTenantTenantIdResourceJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdResourceRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantTenantIdResourceRequestWithBody generates requests for PostTenantTenantIdResource with any type of body
func NewPostTenantTenantIdResourceRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/resource", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostTenantTenantIdSourcesFetchKafkaTopicRequest calls the generic PostTenantTenantIdSourcesFetchKafkaTopic builder with application/json body
func NewPostTenantTenantIdSourcesFetchKafkaTopicRequest(server string, tenantId uint64, body PostTenantTenantIdSourcesFetchKafkaTopicJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdSourcesFetchKafkaTopicRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantTenantIdSourcesFetchKafkaTopicRequestWithBody generates requests for PostTenantTenantIdSourcesFetchKafkaTopic with any type of body
func NewPostTenantTenantIdSourcesFetchKafkaTopicRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/sources/fetchKafkaTopic", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostTenantTenantIdSourcesPingRequest calls the generic PostTenantTenantIdSourcesPing builder with application/json body
func NewPostTenantTenantIdSourcesPingRequest(server string, tenantId uint64, body PostTenantTenantIdSourcesPingJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdSourcesPingRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantTenantIdSourcesPingRequestWithBody generates requests for PostTenantTenantIdSourcesPing with any type of body
func NewPostTenantTenantIdSourcesPingRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/sources/ping", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPutTenantTenantIdStartRequest generates requests for PutTenantTenantIdStart
func NewPutTenantTenantIdStartRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/start", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutTenantTenantIdStopRequest generates requests for PutTenantTenantIdStop
func NewPutTenantTenantIdStopRequest(server string, tenantId uint64) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/stop", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutTenantTenantIdUpdateCfgConfigIdRequest generates requests for PutTenantTenantIdUpdateCfgConfigId
func NewPutTenantTenantIdUpdateCfgConfigIdRequest(server string, tenantId uint64, configId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "configId", runtime.ParamLocationPath, configId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/update-cfg/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantTenantIdUpdateVersionRequest calls the generic PostTenantTenantIdUpdateVersion builder with application/json body
func NewPostTenantTenantIdUpdateVersionRequest(server string, tenantId uint64, body PostTenantTenantIdUpdateVersionJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantTenantIdUpdateVersionRequestWithBody(server, tenantId, "application/json", bodyReader)
}

// NewPostTenantTenantIdUpdateVersionRequestWithBody generates requests for PostTenantTenantIdUpdateVersion with any type of body
func NewPostTenantTenantIdUpdateVersionRequestWithBody(server string, tenantId uint64, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "tenantId", runtime.ParamLocationPath, tenantId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenant/%s/updateVersion", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetTenantsRequest generates requests for GetTenants
func NewGetTenantsRequest(server string, params *GetTenantsParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenants")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostTenantsRequest calls the generic PostTenants builder with application/json body
func NewPostTenantsRequest(server string, body PostTenantsJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostTenantsRequestWithBody(server, "application/json", bodyReader)
}

// NewPostTenantsRequestWithBody generates requests for PostTenants with any type of body
func NewPostTenantsRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenants")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetTenantsStatusRequest generates requests for GetTenantsStatus
func NewGetTenantsStatusRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tenants/status")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetTiersRequest generates requests for GetTiers
func NewGetTiersRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/tiers")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetVersionRequest generates requests for GetVersion
func NewGetVersionRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/version")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

func (c *Client) applyEditors(ctx context.Context, req *http.Request, additionalEditors []RequestEditorFn) error {
	for _, r := range c.RequestEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	for _, r := range additionalEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	return nil
}

// ClientWithResponses builds on ClientInterface to offer response payloads
type ClientWithResponses struct {
	ClientInterface
}

// NewClientWithResponses creates a new ClientWithResponses, which wraps
// Client with return type handling
func NewClientWithResponses(server string, opts ...ClientOption) (*ClientWithResponses, error) {
	client, err := NewClient(server, opts...)
	if err != nil {
		return nil, err
	}
	return &ClientWithResponses{client}, nil
}

// WithBaseURL overrides the baseURL.
func WithBaseURL(baseURL string) ClientOption {
	return func(c *Client) error {
		newBaseURL, err := url.Parse(baseURL)
		if err != nil {
			return err
		}
		c.Server = newBaseURL.String()
		return nil
	}
}

// ClientWithResponsesInterface is the interface specification for the client with responses above.
type ClientWithResponsesInterface interface {
	// GetWithResponse request
	GetWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetResponse, error)

	// GetAvailabilityWithResponse request
	GetAvailabilityWithResponse(ctx context.Context, params *GetAvailabilityParams, reqEditors ...RequestEditorFn) (*GetAvailabilityResponse, error)

	// DeleteByocClusterNameWithResponse request
	DeleteByocClusterNameWithResponse(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*DeleteByocClusterNameResponse, error)

	// GetByocClusterNameWithResponse request
	GetByocClusterNameWithResponse(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*GetByocClusterNameResponse, error)

	// PutByocClusterNameWithBodyWithResponse request with any body
	PutByocClusterNameWithBodyWithResponse(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutByocClusterNameResponse, error)

	PutByocClusterNameWithResponse(ctx context.Context, name string, body PutByocClusterNameJSONRequestBody, reqEditors ...RequestEditorFn) (*PutByocClusterNameResponse, error)

	// PostByocClusterNameManualUpdateWithBodyWithResponse request with any body
	PostByocClusterNameManualUpdateWithBodyWithResponse(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostByocClusterNameManualUpdateResponse, error)

	PostByocClusterNameManualUpdateWithResponse(ctx context.Context, name string, body PostByocClusterNameManualUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*PostByocClusterNameManualUpdateResponse, error)

	// PostByocClusterNameUpdateWithBodyWithResponse request with any body
	PostByocClusterNameUpdateWithBodyWithResponse(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostByocClusterNameUpdateResponse, error)

	PostByocClusterNameUpdateWithResponse(ctx context.Context, name string, body PostByocClusterNameUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*PostByocClusterNameUpdateResponse, error)

	// GetByocClustersWithResponse request
	GetByocClustersWithResponse(ctx context.Context, params *GetByocClustersParams, reqEditors ...RequestEditorFn) (*GetByocClustersResponse, error)

	// PostByocClustersWithBodyWithResponse request with any body
	PostByocClustersWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostByocClustersResponse, error)

	PostByocClustersWithResponse(ctx context.Context, body PostByocClustersJSONRequestBody, reqEditors ...RequestEditorFn) (*PostByocClustersResponse, error)

	// GetEndpointsWithResponse request
	GetEndpointsWithResponse(ctx context.Context, params *GetEndpointsParams, reqEditors ...RequestEditorFn) (*GetEndpointsResponse, error)

	// QueryErrLogWithResponse request
	QueryErrLogWithResponse(ctx context.Context, params *QueryErrLogParams, reqEditors ...RequestEditorFn) (*QueryErrLogResponse, error)

	// GetMetricsWithResponse request
	GetMetricsWithResponse(ctx context.Context, params *GetMetricsParams, reqEditors ...RequestEditorFn) (*GetMetricsResponse, error)

	// GetRootcaWithResponse request
	GetRootcaWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetRootcaResponse, error)

	// PostSourcePingWithBodyWithResponse request with any body
	PostSourcePingWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostSourcePingResponse, error)

	PostSourcePingWithResponse(ctx context.Context, body PostSourcePingJSONRequestBody, reqEditors ...RequestEditorFn) (*PostSourcePingResponse, error)

	// DeleteTenantWithResponse request
	DeleteTenantWithResponse(ctx context.Context, params *DeleteTenantParams, reqEditors ...RequestEditorFn) (*DeleteTenantResponse, error)

	// GetTenantWithResponse request
	GetTenantWithResponse(ctx context.Context, params *GetTenantParams, reqEditors ...RequestEditorFn) (*GetTenantResponse, error)

	// DeleteTenantDbusersWithResponse request
	DeleteTenantDbusersWithResponse(ctx context.Context, params *DeleteTenantDbusersParams, reqEditors ...RequestEditorFn) (*DeleteTenantDbusersResponse, error)

	// GetTenantDbusersWithResponse request
	GetTenantDbusersWithResponse(ctx context.Context, params *GetTenantDbusersParams, reqEditors ...RequestEditorFn) (*GetTenantDbusersResponse, error)

	// PostTenantDbusersWithBodyWithResponse request with any body
	PostTenantDbusersWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantDbusersResponse, error)

	PostTenantDbusersWithResponse(ctx context.Context, body PostTenantDbusersJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantDbusersResponse, error)

	// PutTenantDbusersWithBodyWithResponse request with any body
	PutTenantDbusersWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutTenantDbusersResponse, error)

	PutTenantDbusersWithResponse(ctx context.Context, body PutTenantDbusersJSONRequestBody, reqEditors ...RequestEditorFn) (*PutTenantDbusersResponse, error)

	// GetTenantTagsWithResponse request
	GetTenantTagsWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetTenantTagsResponse, error)

	// GetTenantTenantIdBackupWithResponse request
	GetTenantTenantIdBackupWithResponse(ctx context.Context, tenantId uint64, params *GetTenantTenantIdBackupParams, reqEditors ...RequestEditorFn) (*GetTenantTenantIdBackupResponse, error)

	// PostTenantTenantIdBackupWithResponse request
	PostTenantTenantIdBackupWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdBackupResponse, error)

	// DeleteTenantTenantIdBackupSnapshotIdWithResponse request
	DeleteTenantTenantIdBackupSnapshotIdWithResponse(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdBackupSnapshotIdResponse, error)

	// PostTenantTenantIdBackupSnapshotIdRestoreWithResponse request
	PostTenantTenantIdBackupSnapshotIdRestoreWithResponse(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*PostTenantTenantIdBackupSnapshotIdRestoreResponse, error)

	// PutTenantTenantIdConfigEtcdWithBodyWithResponse request with any body
	PutTenantTenantIdConfigEtcdWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigEtcdResponse, error)

	PutTenantTenantIdConfigEtcdWithTextBodyWithResponse(ctx context.Context, tenantId uint64, body PutTenantTenantIdConfigEtcdTextRequestBody, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigEtcdResponse, error)

	// PutTenantTenantIdConfigRisingwaveWithBodyWithResponse request with any body
	PutTenantTenantIdConfigRisingwaveWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigRisingwaveResponse, error)

	PutTenantTenantIdConfigRisingwaveWithTextBodyWithResponse(ctx context.Context, tenantId uint64, body PutTenantTenantIdConfigRisingwaveTextRequestBody, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigRisingwaveResponse, error)

	// GetTenantTenantIdDatabasesWithResponse request
	GetTenantTenantIdDatabasesWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdDatabasesResponse, error)

	// PostTenantTenantIdExecutionWithBodyWithResponse request with any body
	PostTenantTenantIdExecutionWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExecutionResponse, error)

	PostTenantTenantIdExecutionWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdExecutionJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExecutionResponse, error)

	// PostTenantTenantIdExtensionsServerlessbackfillDisableWithResponse request
	PostTenantTenantIdExtensionsServerlessbackfillDisableWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillDisableResponse, error)

	// PostTenantTenantIdExtensionsServerlessbackfillEnableWithResponse request
	PostTenantTenantIdExtensionsServerlessbackfillEnableWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillEnableResponse, error)

	// PostTenantTenantIdExtensionsServerlessbackfillVersionWithBodyWithResponse request with any body
	PostTenantTenantIdExtensionsServerlessbackfillVersionWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillVersionResponse, error)

	PostTenantTenantIdExtensionsServerlessbackfillVersionWithTextBodyWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdExtensionsServerlessbackfillVersionTextRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillVersionResponse, error)

	// GetTenantTenantIdInfoWithResponse request
	GetTenantTenantIdInfoWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdInfoResponse, error)

	// GetTenantTenantIdMetricsWithResponse request
	GetTenantTenantIdMetricsWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdMetricsResponse, error)

	// DeleteTenantTenantIdPrivatelinkPrivateLinkIdWithResponse request
	DeleteTenantTenantIdPrivatelinkPrivateLinkIdWithResponse(ctx context.Context, tenantId uint64, privateLinkId openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse, error)

	// GetTenantTenantIdPrivatelinkPrivateLinkIdWithResponse request
	GetTenantTenantIdPrivatelinkPrivateLinkIdWithResponse(ctx context.Context, tenantId uint64, privateLinkId openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetTenantTenantIdPrivatelinkPrivateLinkIdResponse, error)

	// PostTenantTenantIdPrivatelinksWithBodyWithResponse request with any body
	PostTenantTenantIdPrivatelinksWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdPrivatelinksResponse, error)

	PostTenantTenantIdPrivatelinksWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdPrivatelinksJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdPrivatelinksResponse, error)

	// DeleteTenantTenantIdRelationWithBodyWithResponse request with any body
	DeleteTenantTenantIdRelationWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdRelationResponse, error)

	DeleteTenantTenantIdRelationWithResponse(ctx context.Context, tenantId uint64, body DeleteTenantTenantIdRelationJSONRequestBody, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdRelationResponse, error)

	// GetTenantTenantIdRelationsGetCountsWithResponse request
	GetTenantTenantIdRelationsGetCountsWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdRelationsGetCountsResponse, error)

	// PostTenantTenantIdResourceWithBodyWithResponse request with any body
	PostTenantTenantIdResourceWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdResourceResponse, error)

	PostTenantTenantIdResourceWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdResourceJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdResourceResponse, error)

	// PostTenantTenantIdSourcesFetchKafkaTopicWithBodyWithResponse request with any body
	PostTenantTenantIdSourcesFetchKafkaTopicWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdSourcesFetchKafkaTopicResponse, error)

	PostTenantTenantIdSourcesFetchKafkaTopicWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdSourcesFetchKafkaTopicJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdSourcesFetchKafkaTopicResponse, error)

	// PostTenantTenantIdSourcesPingWithBodyWithResponse request with any body
	PostTenantTenantIdSourcesPingWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdSourcesPingResponse, error)

	PostTenantTenantIdSourcesPingWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdSourcesPingJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdSourcesPingResponse, error)

	// PutTenantTenantIdStartWithResponse request
	PutTenantTenantIdStartWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PutTenantTenantIdStartResponse, error)

	// PutTenantTenantIdStopWithResponse request
	PutTenantTenantIdStopWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PutTenantTenantIdStopResponse, error)

	// PutTenantTenantIdUpdateCfgConfigIdWithResponse request
	PutTenantTenantIdUpdateCfgConfigIdWithResponse(ctx context.Context, tenantId uint64, configId openapi_types.UUID, reqEditors ...RequestEditorFn) (*PutTenantTenantIdUpdateCfgConfigIdResponse, error)

	// PostTenantTenantIdUpdateVersionWithBodyWithResponse request with any body
	PostTenantTenantIdUpdateVersionWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdUpdateVersionResponse, error)

	PostTenantTenantIdUpdateVersionWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdUpdateVersionJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdUpdateVersionResponse, error)

	// GetTenantsWithResponse request
	GetTenantsWithResponse(ctx context.Context, params *GetTenantsParams, reqEditors ...RequestEditorFn) (*GetTenantsResponse, error)

	// PostTenantsWithBodyWithResponse request with any body
	PostTenantsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantsResponse, error)

	PostTenantsWithResponse(ctx context.Context, body PostTenantsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantsResponse, error)

	// GetTenantsStatusWithResponse request
	GetTenantsStatusWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetTenantsStatusResponse, error)

	// GetTiersWithResponse request
	GetTiersWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetTiersResponse, error)

	// GetVersionWithResponse request
	GetVersionWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetVersionResponse, error)
}

type GetResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r GetResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetAvailabilityResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *AvailabilityResponse
	JSON422      *AvailabilityResponse
}

// Status returns HTTPResponse.Status
func (r GetAvailabilityResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetAvailabilityResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteByocClusterNameResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON404      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r DeleteByocClusterNameResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteByocClusterNameResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetByocClusterNameResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ManagedCluster
	JSON404      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r GetByocClusterNameResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetByocClusterNameResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutByocClusterNameResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON404      *DefaultResponse
	JSON409      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PutByocClusterNameResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutByocClusterNameResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostByocClusterNameManualUpdateResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *DefaultResponse
	JSON400      *DefaultResponse
	JSON404      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PostByocClusterNameManualUpdateResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostByocClusterNameManualUpdateResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostByocClusterNameUpdateResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *DefaultResponse
	JSON400      *DefaultResponse
	JSON404      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PostByocClusterNameUpdateResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostByocClusterNameUpdateResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetByocClustersResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ManagedClustersSizePage
}

// Status returns HTTPResponse.Status
func (r GetByocClustersResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetByocClustersResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostByocClustersResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON409      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PostByocClustersResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostByocClustersResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetEndpointsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Endpoint
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r GetEndpointsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetEndpointsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type QueryErrLogResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ErrLogQueryResult
	JSON400      *BadRequestResponse
}

// Status returns HTTPResponse.Status
func (r QueryErrLogResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r QueryErrLogResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetMetricsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Metrics
	JSON404      *NotFoundResponse
	JSON416      *RangeNotSatisfiable
}

// Status returns HTTPResponse.Status
func (r GetMetricsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetMetricsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetRootcaResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r GetRootcaResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetRootcaResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostSourcePingResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *AvailabilityResponse
	JSON400      *BadRequestResponse
}

// Status returns HTTPResponse.Status
func (r PostSourcePingResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostSourcePingResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteTenantResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *DefaultResponse
	JSON403      *DefaultResponse
	JSON404      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r DeleteTenantResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteTenantResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Tenant
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r GetTenantResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteTenantDbusersResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r DeleteTenantDbusersResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteTenantDbusersResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantDbusersResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DBUsers
	JSON404      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r GetTenantDbusersResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantDbusersResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantDbusersResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DBUser
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantDbusersResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantDbusersResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutTenantDbusersResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PutTenantDbusersResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutTenantDbusersResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantTagsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *GetImageTagResponse
}

// Status returns HTTPResponse.Status
func (r GetTenantTagsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantTagsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantTenantIdBackupResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *BackupSnapshotsSizePage
}

// Status returns HTTPResponse.Status
func (r GetTenantTenantIdBackupResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantTenantIdBackupResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdBackupResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *PostSnapshotResponseBody
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdBackupResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdBackupResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteTenantTenantIdBackupSnapshotIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r DeleteTenantTenantIdBackupSnapshotIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteTenantTenantIdBackupSnapshotIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdBackupSnapshotIdRestoreResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdBackupSnapshotIdRestoreResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdBackupSnapshotIdRestoreResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutTenantTenantIdConfigEtcdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON404      *NotFoundResponse
	JSON409      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PutTenantTenantIdConfigEtcdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutTenantTenantIdConfigEtcdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutTenantTenantIdConfigRisingwaveResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON404      *NotFoundResponse
	JSON409      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PutTenantTenantIdConfigRisingwaveResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutTenantTenantIdConfigRisingwaveResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantTenantIdDatabasesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *GetDatabasesResponseBody
}

// Status returns HTTPResponse.Status
func (r GetTenantTenantIdDatabasesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantTenantIdDatabasesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdExecutionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdExecutionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdExecutionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdExtensionsServerlessbackfillDisableResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdExtensionsServerlessbackfillDisableResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdExtensionsServerlessbackfillDisableResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdExtensionsServerlessbackfillEnableResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdExtensionsServerlessbackfillEnableResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdExtensionsServerlessbackfillEnableResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdExtensionsServerlessbackfillVersionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *WorkflowIdResponseBody
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdExtensionsServerlessbackfillVersionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdExtensionsServerlessbackfillVersionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantTenantIdInfoResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *TenantClusterInfoResponseBody
}

// Status returns HTTPResponse.Status
func (r GetTenantTenantIdInfoResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantTenantIdInfoResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantTenantIdMetricsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r GetTenantTenantIdMetricsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantTenantIdMetricsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *DefaultResponse
	JSON400      *BadRequestResponse
}

// Status returns HTTPResponse.Status
func (r DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantTenantIdPrivatelinkPrivateLinkIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *PrivateLink
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r GetTenantTenantIdPrivatelinkPrivateLinkIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantTenantIdPrivatelinkPrivateLinkIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdPrivatelinksResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *PostPrivateLinkResponseBody
	JSON400      *BadRequestResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdPrivatelinksResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdPrivatelinksResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteTenantTenantIdRelationResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
	JSON400      *FailedPreconditionResponse
	JSON404      *NotFoundResponse
}

// Status returns HTTPResponse.Status
func (r DeleteTenantTenantIdRelationResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteTenantTenantIdRelationResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantTenantIdRelationsGetCountsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *RelationCounts
}

// Status returns HTTPResponse.Status
func (r GetTenantTenantIdRelationsGetCountsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantTenantIdRelationsGetCountsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdResourceResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *DefaultResponse
	JSON400      *BadRequestResponse
	JSON422      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdResourceResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdResourceResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdSourcesFetchKafkaTopicResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *PostSourcesFetchKafkaTopicResponseBody
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdSourcesFetchKafkaTopicResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdSourcesFetchKafkaTopicResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdSourcesPingResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *AvailabilityResponse
	JSON400      *BadRequestResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdSourcesPingResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdSourcesPingResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutTenantTenantIdStartResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON404      *NotFoundResponse
	JSON409      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PutTenantTenantIdStartResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutTenantTenantIdStartResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutTenantTenantIdStopResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *DefaultResponse
	JSON404      *NotFoundResponse
	JSON409      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PutTenantTenantIdStopResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutTenantTenantIdStopResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutTenantTenantIdUpdateCfgConfigIdResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON404      *NotFoundResponse
	JSON409      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PutTenantTenantIdUpdateCfgConfigIdResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutTenantTenantIdUpdateCfgConfigIdResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantTenantIdUpdateVersionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *DefaultResponse
	JSON404      *DefaultResponse
	JSON409      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantTenantIdUpdateVersionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantTenantIdUpdateVersionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *TenantSizePage
}

// Status returns HTTPResponse.Status
func (r GetTenantsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostTenantsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON202      *CreateTenantResponseBody
	JSON400      *BadRequestResponse
	JSON422      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r PostTenantsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostTenantsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTenantsStatusResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *TenantStatusCounts
}

// Status returns HTTPResponse.Status
func (r GetTenantsStatusResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTenantsStatusResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTiersResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *Tiers
}

// Status returns HTTPResponse.Status
func (r GetTiersResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTiersResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetVersionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DefaultResponse
}

// Status returns HTTPResponse.Status
func (r GetVersionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetVersionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

// GetWithResponse request returning *GetResponse
func (c *ClientWithResponses) GetWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetResponse, error) {
	rsp, err := c.Get(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetResponse(rsp)
}

// GetAvailabilityWithResponse request returning *GetAvailabilityResponse
func (c *ClientWithResponses) GetAvailabilityWithResponse(ctx context.Context, params *GetAvailabilityParams, reqEditors ...RequestEditorFn) (*GetAvailabilityResponse, error) {
	rsp, err := c.GetAvailability(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetAvailabilityResponse(rsp)
}

// DeleteByocClusterNameWithResponse request returning *DeleteByocClusterNameResponse
func (c *ClientWithResponses) DeleteByocClusterNameWithResponse(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*DeleteByocClusterNameResponse, error) {
	rsp, err := c.DeleteByocClusterName(ctx, name, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteByocClusterNameResponse(rsp)
}

// GetByocClusterNameWithResponse request returning *GetByocClusterNameResponse
func (c *ClientWithResponses) GetByocClusterNameWithResponse(ctx context.Context, name string, reqEditors ...RequestEditorFn) (*GetByocClusterNameResponse, error) {
	rsp, err := c.GetByocClusterName(ctx, name, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetByocClusterNameResponse(rsp)
}

// PutByocClusterNameWithBodyWithResponse request with arbitrary body returning *PutByocClusterNameResponse
func (c *ClientWithResponses) PutByocClusterNameWithBodyWithResponse(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutByocClusterNameResponse, error) {
	rsp, err := c.PutByocClusterNameWithBody(ctx, name, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutByocClusterNameResponse(rsp)
}

func (c *ClientWithResponses) PutByocClusterNameWithResponse(ctx context.Context, name string, body PutByocClusterNameJSONRequestBody, reqEditors ...RequestEditorFn) (*PutByocClusterNameResponse, error) {
	rsp, err := c.PutByocClusterName(ctx, name, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutByocClusterNameResponse(rsp)
}

// PostByocClusterNameManualUpdateWithBodyWithResponse request with arbitrary body returning *PostByocClusterNameManualUpdateResponse
func (c *ClientWithResponses) PostByocClusterNameManualUpdateWithBodyWithResponse(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostByocClusterNameManualUpdateResponse, error) {
	rsp, err := c.PostByocClusterNameManualUpdateWithBody(ctx, name, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostByocClusterNameManualUpdateResponse(rsp)
}

func (c *ClientWithResponses) PostByocClusterNameManualUpdateWithResponse(ctx context.Context, name string, body PostByocClusterNameManualUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*PostByocClusterNameManualUpdateResponse, error) {
	rsp, err := c.PostByocClusterNameManualUpdate(ctx, name, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostByocClusterNameManualUpdateResponse(rsp)
}

// PostByocClusterNameUpdateWithBodyWithResponse request with arbitrary body returning *PostByocClusterNameUpdateResponse
func (c *ClientWithResponses) PostByocClusterNameUpdateWithBodyWithResponse(ctx context.Context, name string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostByocClusterNameUpdateResponse, error) {
	rsp, err := c.PostByocClusterNameUpdateWithBody(ctx, name, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostByocClusterNameUpdateResponse(rsp)
}

func (c *ClientWithResponses) PostByocClusterNameUpdateWithResponse(ctx context.Context, name string, body PostByocClusterNameUpdateJSONRequestBody, reqEditors ...RequestEditorFn) (*PostByocClusterNameUpdateResponse, error) {
	rsp, err := c.PostByocClusterNameUpdate(ctx, name, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostByocClusterNameUpdateResponse(rsp)
}

// GetByocClustersWithResponse request returning *GetByocClustersResponse
func (c *ClientWithResponses) GetByocClustersWithResponse(ctx context.Context, params *GetByocClustersParams, reqEditors ...RequestEditorFn) (*GetByocClustersResponse, error) {
	rsp, err := c.GetByocClusters(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetByocClustersResponse(rsp)
}

// PostByocClustersWithBodyWithResponse request with arbitrary body returning *PostByocClustersResponse
func (c *ClientWithResponses) PostByocClustersWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostByocClustersResponse, error) {
	rsp, err := c.PostByocClustersWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostByocClustersResponse(rsp)
}

func (c *ClientWithResponses) PostByocClustersWithResponse(ctx context.Context, body PostByocClustersJSONRequestBody, reqEditors ...RequestEditorFn) (*PostByocClustersResponse, error) {
	rsp, err := c.PostByocClusters(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostByocClustersResponse(rsp)
}

// GetEndpointsWithResponse request returning *GetEndpointsResponse
func (c *ClientWithResponses) GetEndpointsWithResponse(ctx context.Context, params *GetEndpointsParams, reqEditors ...RequestEditorFn) (*GetEndpointsResponse, error) {
	rsp, err := c.GetEndpoints(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetEndpointsResponse(rsp)
}

// QueryErrLogWithResponse request returning *QueryErrLogResponse
func (c *ClientWithResponses) QueryErrLogWithResponse(ctx context.Context, params *QueryErrLogParams, reqEditors ...RequestEditorFn) (*QueryErrLogResponse, error) {
	rsp, err := c.QueryErrLog(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseQueryErrLogResponse(rsp)
}

// GetMetricsWithResponse request returning *GetMetricsResponse
func (c *ClientWithResponses) GetMetricsWithResponse(ctx context.Context, params *GetMetricsParams, reqEditors ...RequestEditorFn) (*GetMetricsResponse, error) {
	rsp, err := c.GetMetrics(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetMetricsResponse(rsp)
}

// GetRootcaWithResponse request returning *GetRootcaResponse
func (c *ClientWithResponses) GetRootcaWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetRootcaResponse, error) {
	rsp, err := c.GetRootca(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetRootcaResponse(rsp)
}

// PostSourcePingWithBodyWithResponse request with arbitrary body returning *PostSourcePingResponse
func (c *ClientWithResponses) PostSourcePingWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostSourcePingResponse, error) {
	rsp, err := c.PostSourcePingWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostSourcePingResponse(rsp)
}

func (c *ClientWithResponses) PostSourcePingWithResponse(ctx context.Context, body PostSourcePingJSONRequestBody, reqEditors ...RequestEditorFn) (*PostSourcePingResponse, error) {
	rsp, err := c.PostSourcePing(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostSourcePingResponse(rsp)
}

// DeleteTenantWithResponse request returning *DeleteTenantResponse
func (c *ClientWithResponses) DeleteTenantWithResponse(ctx context.Context, params *DeleteTenantParams, reqEditors ...RequestEditorFn) (*DeleteTenantResponse, error) {
	rsp, err := c.DeleteTenant(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteTenantResponse(rsp)
}

// GetTenantWithResponse request returning *GetTenantResponse
func (c *ClientWithResponses) GetTenantWithResponse(ctx context.Context, params *GetTenantParams, reqEditors ...RequestEditorFn) (*GetTenantResponse, error) {
	rsp, err := c.GetTenant(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantResponse(rsp)
}

// DeleteTenantDbusersWithResponse request returning *DeleteTenantDbusersResponse
func (c *ClientWithResponses) DeleteTenantDbusersWithResponse(ctx context.Context, params *DeleteTenantDbusersParams, reqEditors ...RequestEditorFn) (*DeleteTenantDbusersResponse, error) {
	rsp, err := c.DeleteTenantDbusers(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteTenantDbusersResponse(rsp)
}

// GetTenantDbusersWithResponse request returning *GetTenantDbusersResponse
func (c *ClientWithResponses) GetTenantDbusersWithResponse(ctx context.Context, params *GetTenantDbusersParams, reqEditors ...RequestEditorFn) (*GetTenantDbusersResponse, error) {
	rsp, err := c.GetTenantDbusers(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantDbusersResponse(rsp)
}

// PostTenantDbusersWithBodyWithResponse request with arbitrary body returning *PostTenantDbusersResponse
func (c *ClientWithResponses) PostTenantDbusersWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantDbusersResponse, error) {
	rsp, err := c.PostTenantDbusersWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantDbusersResponse(rsp)
}

func (c *ClientWithResponses) PostTenantDbusersWithResponse(ctx context.Context, body PostTenantDbusersJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantDbusersResponse, error) {
	rsp, err := c.PostTenantDbusers(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantDbusersResponse(rsp)
}

// PutTenantDbusersWithBodyWithResponse request with arbitrary body returning *PutTenantDbusersResponse
func (c *ClientWithResponses) PutTenantDbusersWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutTenantDbusersResponse, error) {
	rsp, err := c.PutTenantDbusersWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantDbusersResponse(rsp)
}

func (c *ClientWithResponses) PutTenantDbusersWithResponse(ctx context.Context, body PutTenantDbusersJSONRequestBody, reqEditors ...RequestEditorFn) (*PutTenantDbusersResponse, error) {
	rsp, err := c.PutTenantDbusers(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantDbusersResponse(rsp)
}

// GetTenantTagsWithResponse request returning *GetTenantTagsResponse
func (c *ClientWithResponses) GetTenantTagsWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetTenantTagsResponse, error) {
	rsp, err := c.GetTenantTags(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantTagsResponse(rsp)
}

// GetTenantTenantIdBackupWithResponse request returning *GetTenantTenantIdBackupResponse
func (c *ClientWithResponses) GetTenantTenantIdBackupWithResponse(ctx context.Context, tenantId uint64, params *GetTenantTenantIdBackupParams, reqEditors ...RequestEditorFn) (*GetTenantTenantIdBackupResponse, error) {
	rsp, err := c.GetTenantTenantIdBackup(ctx, tenantId, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantTenantIdBackupResponse(rsp)
}

// PostTenantTenantIdBackupWithResponse request returning *PostTenantTenantIdBackupResponse
func (c *ClientWithResponses) PostTenantTenantIdBackupWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdBackupResponse, error) {
	rsp, err := c.PostTenantTenantIdBackup(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdBackupResponse(rsp)
}

// DeleteTenantTenantIdBackupSnapshotIdWithResponse request returning *DeleteTenantTenantIdBackupSnapshotIdResponse
func (c *ClientWithResponses) DeleteTenantTenantIdBackupSnapshotIdWithResponse(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdBackupSnapshotIdResponse, error) {
	rsp, err := c.DeleteTenantTenantIdBackupSnapshotId(ctx, tenantId, snapshotId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteTenantTenantIdBackupSnapshotIdResponse(rsp)
}

// PostTenantTenantIdBackupSnapshotIdRestoreWithResponse request returning *PostTenantTenantIdBackupSnapshotIdRestoreResponse
func (c *ClientWithResponses) PostTenantTenantIdBackupSnapshotIdRestoreWithResponse(ctx context.Context, tenantId uint64, snapshotId openapi_types.UUID, reqEditors ...RequestEditorFn) (*PostTenantTenantIdBackupSnapshotIdRestoreResponse, error) {
	rsp, err := c.PostTenantTenantIdBackupSnapshotIdRestore(ctx, tenantId, snapshotId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdBackupSnapshotIdRestoreResponse(rsp)
}

// PutTenantTenantIdConfigEtcdWithBodyWithResponse request with arbitrary body returning *PutTenantTenantIdConfigEtcdResponse
func (c *ClientWithResponses) PutTenantTenantIdConfigEtcdWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigEtcdResponse, error) {
	rsp, err := c.PutTenantTenantIdConfigEtcdWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantTenantIdConfigEtcdResponse(rsp)
}

func (c *ClientWithResponses) PutTenantTenantIdConfigEtcdWithTextBodyWithResponse(ctx context.Context, tenantId uint64, body PutTenantTenantIdConfigEtcdTextRequestBody, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigEtcdResponse, error) {
	rsp, err := c.PutTenantTenantIdConfigEtcdWithTextBody(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantTenantIdConfigEtcdResponse(rsp)
}

// PutTenantTenantIdConfigRisingwaveWithBodyWithResponse request with arbitrary body returning *PutTenantTenantIdConfigRisingwaveResponse
func (c *ClientWithResponses) PutTenantTenantIdConfigRisingwaveWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigRisingwaveResponse, error) {
	rsp, err := c.PutTenantTenantIdConfigRisingwaveWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantTenantIdConfigRisingwaveResponse(rsp)
}

func (c *ClientWithResponses) PutTenantTenantIdConfigRisingwaveWithTextBodyWithResponse(ctx context.Context, tenantId uint64, body PutTenantTenantIdConfigRisingwaveTextRequestBody, reqEditors ...RequestEditorFn) (*PutTenantTenantIdConfigRisingwaveResponse, error) {
	rsp, err := c.PutTenantTenantIdConfigRisingwaveWithTextBody(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantTenantIdConfigRisingwaveResponse(rsp)
}

// GetTenantTenantIdDatabasesWithResponse request returning *GetTenantTenantIdDatabasesResponse
func (c *ClientWithResponses) GetTenantTenantIdDatabasesWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdDatabasesResponse, error) {
	rsp, err := c.GetTenantTenantIdDatabases(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantTenantIdDatabasesResponse(rsp)
}

// PostTenantTenantIdExecutionWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdExecutionResponse
func (c *ClientWithResponses) PostTenantTenantIdExecutionWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExecutionResponse, error) {
	rsp, err := c.PostTenantTenantIdExecutionWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExecutionResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdExecutionWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdExecutionJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExecutionResponse, error) {
	rsp, err := c.PostTenantTenantIdExecution(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExecutionResponse(rsp)
}

// PostTenantTenantIdExtensionsServerlessbackfillDisableWithResponse request returning *PostTenantTenantIdExtensionsServerlessbackfillDisableResponse
func (c *ClientWithResponses) PostTenantTenantIdExtensionsServerlessbackfillDisableWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillDisableResponse, error) {
	rsp, err := c.PostTenantTenantIdExtensionsServerlessbackfillDisable(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExtensionsServerlessbackfillDisableResponse(rsp)
}

// PostTenantTenantIdExtensionsServerlessbackfillEnableWithResponse request returning *PostTenantTenantIdExtensionsServerlessbackfillEnableResponse
func (c *ClientWithResponses) PostTenantTenantIdExtensionsServerlessbackfillEnableWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillEnableResponse, error) {
	rsp, err := c.PostTenantTenantIdExtensionsServerlessbackfillEnable(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExtensionsServerlessbackfillEnableResponse(rsp)
}

// PostTenantTenantIdExtensionsServerlessbackfillVersionWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdExtensionsServerlessbackfillVersionResponse
func (c *ClientWithResponses) PostTenantTenantIdExtensionsServerlessbackfillVersionWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillVersionResponse, error) {
	rsp, err := c.PostTenantTenantIdExtensionsServerlessbackfillVersionWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExtensionsServerlessbackfillVersionResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdExtensionsServerlessbackfillVersionWithTextBodyWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdExtensionsServerlessbackfillVersionTextRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdExtensionsServerlessbackfillVersionResponse, error) {
	rsp, err := c.PostTenantTenantIdExtensionsServerlessbackfillVersionWithTextBody(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdExtensionsServerlessbackfillVersionResponse(rsp)
}

// GetTenantTenantIdInfoWithResponse request returning *GetTenantTenantIdInfoResponse
func (c *ClientWithResponses) GetTenantTenantIdInfoWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdInfoResponse, error) {
	rsp, err := c.GetTenantTenantIdInfo(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantTenantIdInfoResponse(rsp)
}

// GetTenantTenantIdMetricsWithResponse request returning *GetTenantTenantIdMetricsResponse
func (c *ClientWithResponses) GetTenantTenantIdMetricsWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdMetricsResponse, error) {
	rsp, err := c.GetTenantTenantIdMetrics(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantTenantIdMetricsResponse(rsp)
}

// DeleteTenantTenantIdPrivatelinkPrivateLinkIdWithResponse request returning *DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse
func (c *ClientWithResponses) DeleteTenantTenantIdPrivatelinkPrivateLinkIdWithResponse(ctx context.Context, tenantId uint64, privateLinkId openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse, error) {
	rsp, err := c.DeleteTenantTenantIdPrivatelinkPrivateLinkId(ctx, tenantId, privateLinkId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse(rsp)
}

// GetTenantTenantIdPrivatelinkPrivateLinkIdWithResponse request returning *GetTenantTenantIdPrivatelinkPrivateLinkIdResponse
func (c *ClientWithResponses) GetTenantTenantIdPrivatelinkPrivateLinkIdWithResponse(ctx context.Context, tenantId uint64, privateLinkId openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetTenantTenantIdPrivatelinkPrivateLinkIdResponse, error) {
	rsp, err := c.GetTenantTenantIdPrivatelinkPrivateLinkId(ctx, tenantId, privateLinkId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantTenantIdPrivatelinkPrivateLinkIdResponse(rsp)
}

// PostTenantTenantIdPrivatelinksWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdPrivatelinksResponse
func (c *ClientWithResponses) PostTenantTenantIdPrivatelinksWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdPrivatelinksResponse, error) {
	rsp, err := c.PostTenantTenantIdPrivatelinksWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdPrivatelinksResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdPrivatelinksWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdPrivatelinksJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdPrivatelinksResponse, error) {
	rsp, err := c.PostTenantTenantIdPrivatelinks(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdPrivatelinksResponse(rsp)
}

// DeleteTenantTenantIdRelationWithBodyWithResponse request with arbitrary body returning *DeleteTenantTenantIdRelationResponse
func (c *ClientWithResponses) DeleteTenantTenantIdRelationWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdRelationResponse, error) {
	rsp, err := c.DeleteTenantTenantIdRelationWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteTenantTenantIdRelationResponse(rsp)
}

func (c *ClientWithResponses) DeleteTenantTenantIdRelationWithResponse(ctx context.Context, tenantId uint64, body DeleteTenantTenantIdRelationJSONRequestBody, reqEditors ...RequestEditorFn) (*DeleteTenantTenantIdRelationResponse, error) {
	rsp, err := c.DeleteTenantTenantIdRelation(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteTenantTenantIdRelationResponse(rsp)
}

// GetTenantTenantIdRelationsGetCountsWithResponse request returning *GetTenantTenantIdRelationsGetCountsResponse
func (c *ClientWithResponses) GetTenantTenantIdRelationsGetCountsWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*GetTenantTenantIdRelationsGetCountsResponse, error) {
	rsp, err := c.GetTenantTenantIdRelationsGetCounts(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantTenantIdRelationsGetCountsResponse(rsp)
}

// PostTenantTenantIdResourceWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdResourceResponse
func (c *ClientWithResponses) PostTenantTenantIdResourceWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdResourceResponse, error) {
	rsp, err := c.PostTenantTenantIdResourceWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdResourceResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdResourceWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdResourceJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdResourceResponse, error) {
	rsp, err := c.PostTenantTenantIdResource(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdResourceResponse(rsp)
}

// PostTenantTenantIdSourcesFetchKafkaTopicWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdSourcesFetchKafkaTopicResponse
func (c *ClientWithResponses) PostTenantTenantIdSourcesFetchKafkaTopicWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdSourcesFetchKafkaTopicResponse, error) {
	rsp, err := c.PostTenantTenantIdSourcesFetchKafkaTopicWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdSourcesFetchKafkaTopicResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdSourcesFetchKafkaTopicWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdSourcesFetchKafkaTopicJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdSourcesFetchKafkaTopicResponse, error) {
	rsp, err := c.PostTenantTenantIdSourcesFetchKafkaTopic(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdSourcesFetchKafkaTopicResponse(rsp)
}

// PostTenantTenantIdSourcesPingWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdSourcesPingResponse
func (c *ClientWithResponses) PostTenantTenantIdSourcesPingWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdSourcesPingResponse, error) {
	rsp, err := c.PostTenantTenantIdSourcesPingWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdSourcesPingResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdSourcesPingWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdSourcesPingJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdSourcesPingResponse, error) {
	rsp, err := c.PostTenantTenantIdSourcesPing(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdSourcesPingResponse(rsp)
}

// PutTenantTenantIdStartWithResponse request returning *PutTenantTenantIdStartResponse
func (c *ClientWithResponses) PutTenantTenantIdStartWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PutTenantTenantIdStartResponse, error) {
	rsp, err := c.PutTenantTenantIdStart(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantTenantIdStartResponse(rsp)
}

// PutTenantTenantIdStopWithResponse request returning *PutTenantTenantIdStopResponse
func (c *ClientWithResponses) PutTenantTenantIdStopWithResponse(ctx context.Context, tenantId uint64, reqEditors ...RequestEditorFn) (*PutTenantTenantIdStopResponse, error) {
	rsp, err := c.PutTenantTenantIdStop(ctx, tenantId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantTenantIdStopResponse(rsp)
}

// PutTenantTenantIdUpdateCfgConfigIdWithResponse request returning *PutTenantTenantIdUpdateCfgConfigIdResponse
func (c *ClientWithResponses) PutTenantTenantIdUpdateCfgConfigIdWithResponse(ctx context.Context, tenantId uint64, configId openapi_types.UUID, reqEditors ...RequestEditorFn) (*PutTenantTenantIdUpdateCfgConfigIdResponse, error) {
	rsp, err := c.PutTenantTenantIdUpdateCfgConfigId(ctx, tenantId, configId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutTenantTenantIdUpdateCfgConfigIdResponse(rsp)
}

// PostTenantTenantIdUpdateVersionWithBodyWithResponse request with arbitrary body returning *PostTenantTenantIdUpdateVersionResponse
func (c *ClientWithResponses) PostTenantTenantIdUpdateVersionWithBodyWithResponse(ctx context.Context, tenantId uint64, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantTenantIdUpdateVersionResponse, error) {
	rsp, err := c.PostTenantTenantIdUpdateVersionWithBody(ctx, tenantId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdUpdateVersionResponse(rsp)
}

func (c *ClientWithResponses) PostTenantTenantIdUpdateVersionWithResponse(ctx context.Context, tenantId uint64, body PostTenantTenantIdUpdateVersionJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantTenantIdUpdateVersionResponse, error) {
	rsp, err := c.PostTenantTenantIdUpdateVersion(ctx, tenantId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantTenantIdUpdateVersionResponse(rsp)
}

// GetTenantsWithResponse request returning *GetTenantsResponse
func (c *ClientWithResponses) GetTenantsWithResponse(ctx context.Context, params *GetTenantsParams, reqEditors ...RequestEditorFn) (*GetTenantsResponse, error) {
	rsp, err := c.GetTenants(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantsResponse(rsp)
}

// PostTenantsWithBodyWithResponse request with arbitrary body returning *PostTenantsResponse
func (c *ClientWithResponses) PostTenantsWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostTenantsResponse, error) {
	rsp, err := c.PostTenantsWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantsResponse(rsp)
}

func (c *ClientWithResponses) PostTenantsWithResponse(ctx context.Context, body PostTenantsJSONRequestBody, reqEditors ...RequestEditorFn) (*PostTenantsResponse, error) {
	rsp, err := c.PostTenants(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostTenantsResponse(rsp)
}

// GetTenantsStatusWithResponse request returning *GetTenantsStatusResponse
func (c *ClientWithResponses) GetTenantsStatusWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetTenantsStatusResponse, error) {
	rsp, err := c.GetTenantsStatus(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTenantsStatusResponse(rsp)
}

// GetTiersWithResponse request returning *GetTiersResponse
func (c *ClientWithResponses) GetTiersWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetTiersResponse, error) {
	rsp, err := c.GetTiers(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTiersResponse(rsp)
}

// GetVersionWithResponse request returning *GetVersionResponse
func (c *ClientWithResponses) GetVersionWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetVersionResponse, error) {
	rsp, err := c.GetVersion(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetVersionResponse(rsp)
}

// ParseGetResponse parses an HTTP response from a GetWithResponse call
func ParseGetResponse(rsp *http.Response) (*GetResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetAvailabilityResponse parses an HTTP response from a GetAvailabilityWithResponse call
func ParseGetAvailabilityResponse(rsp *http.Response) (*GetAvailabilityResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetAvailabilityResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest AvailabilityResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest AvailabilityResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseDeleteByocClusterNameResponse parses an HTTP response from a DeleteByocClusterNameWithResponse call
func ParseDeleteByocClusterNameResponse(rsp *http.Response) (*DeleteByocClusterNameResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteByocClusterNameResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetByocClusterNameResponse parses an HTTP response from a GetByocClusterNameWithResponse call
func ParseGetByocClusterNameResponse(rsp *http.Response) (*GetByocClusterNameResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetByocClusterNameResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ManagedCluster
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePutByocClusterNameResponse parses an HTTP response from a PutByocClusterNameWithResponse call
func ParsePutByocClusterNameResponse(rsp *http.Response) (*PutByocClusterNameResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutByocClusterNameResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 409:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON409 = &dest

	}

	return response, nil
}

// ParsePostByocClusterNameManualUpdateResponse parses an HTTP response from a PostByocClusterNameManualUpdateWithResponse call
func ParsePostByocClusterNameManualUpdateResponse(rsp *http.Response) (*PostByocClusterNameManualUpdateResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostByocClusterNameManualUpdateResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostByocClusterNameUpdateResponse parses an HTTP response from a PostByocClusterNameUpdateWithResponse call
func ParsePostByocClusterNameUpdateResponse(rsp *http.Response) (*PostByocClusterNameUpdateResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostByocClusterNameUpdateResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetByocClustersResponse parses an HTTP response from a GetByocClustersWithResponse call
func ParseGetByocClustersResponse(rsp *http.Response) (*GetByocClustersResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetByocClustersResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ManagedClustersSizePage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostByocClustersResponse parses an HTTP response from a PostByocClustersWithResponse call
func ParsePostByocClustersResponse(rsp *http.Response) (*PostByocClustersResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostByocClustersResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 409:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON409 = &dest

	}

	return response, nil
}

// ParseGetEndpointsResponse parses an HTTP response from a GetEndpointsWithResponse call
func ParseGetEndpointsResponse(rsp *http.Response) (*GetEndpointsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetEndpointsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Endpoint
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseQueryErrLogResponse parses an HTTP response from a QueryErrLogWithResponse call
func ParseQueryErrLogResponse(rsp *http.Response) (*QueryErrLogResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &QueryErrLogResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ErrLogQueryResult
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParseGetMetricsResponse parses an HTTP response from a GetMetricsWithResponse call
func ParseGetMetricsResponse(rsp *http.Response) (*GetMetricsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetMetricsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Metrics
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 416:
		var dest RangeNotSatisfiable
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON416 = &dest

	}

	return response, nil
}

// ParseGetRootcaResponse parses an HTTP response from a GetRootcaWithResponse call
func ParseGetRootcaResponse(rsp *http.Response) (*GetRootcaResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetRootcaResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParsePostSourcePingResponse parses an HTTP response from a PostSourcePingWithResponse call
func ParsePostSourcePingResponse(rsp *http.Response) (*PostSourcePingResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostSourcePingResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest AvailabilityResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParseDeleteTenantResponse parses an HTTP response from a DeleteTenantWithResponse call
func ParseDeleteTenantResponse(rsp *http.Response) (*DeleteTenantResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteTenantResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 403:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON403 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetTenantResponse parses an HTTP response from a GetTenantWithResponse call
func ParseGetTenantResponse(rsp *http.Response) (*GetTenantResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Tenant
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseDeleteTenantDbusersResponse parses an HTTP response from a DeleteTenantDbusersWithResponse call
func ParseDeleteTenantDbusersResponse(rsp *http.Response) (*DeleteTenantDbusersResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteTenantDbusersResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetTenantDbusersResponse parses an HTTP response from a GetTenantDbusersWithResponse call
func ParseGetTenantDbusersResponse(rsp *http.Response) (*GetTenantDbusersResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantDbusersResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DBUsers
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantDbusersResponse parses an HTTP response from a PostTenantDbusersWithResponse call
func ParsePostTenantDbusersResponse(rsp *http.Response) (*PostTenantDbusersResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantDbusersResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DBUser
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePutTenantDbusersResponse parses an HTTP response from a PutTenantDbusersWithResponse call
func ParsePutTenantDbusersResponse(rsp *http.Response) (*PutTenantDbusersResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutTenantDbusersResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetTenantTagsResponse parses an HTTP response from a GetTenantTagsWithResponse call
func ParseGetTenantTagsResponse(rsp *http.Response) (*GetTenantTagsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantTagsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest GetImageTagResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetTenantTenantIdBackupResponse parses an HTTP response from a GetTenantTenantIdBackupWithResponse call
func ParseGetTenantTenantIdBackupResponse(rsp *http.Response) (*GetTenantTenantIdBackupResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantTenantIdBackupResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest BackupSnapshotsSizePage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdBackupResponse parses an HTTP response from a PostTenantTenantIdBackupWithResponse call
func ParsePostTenantTenantIdBackupResponse(rsp *http.Response) (*PostTenantTenantIdBackupResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdBackupResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest PostSnapshotResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	}

	return response, nil
}

// ParseDeleteTenantTenantIdBackupSnapshotIdResponse parses an HTTP response from a DeleteTenantTenantIdBackupSnapshotIdWithResponse call
func ParseDeleteTenantTenantIdBackupSnapshotIdResponse(rsp *http.Response) (*DeleteTenantTenantIdBackupSnapshotIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteTenantTenantIdBackupSnapshotIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParsePostTenantTenantIdBackupSnapshotIdRestoreResponse parses an HTTP response from a PostTenantTenantIdBackupSnapshotIdRestoreWithResponse call
func ParsePostTenantTenantIdBackupSnapshotIdRestoreResponse(rsp *http.Response) (*PostTenantTenantIdBackupSnapshotIdRestoreResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdBackupSnapshotIdRestoreResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParsePutTenantTenantIdConfigEtcdResponse parses an HTTP response from a PutTenantTenantIdConfigEtcdWithResponse call
func ParsePutTenantTenantIdConfigEtcdResponse(rsp *http.Response) (*PutTenantTenantIdConfigEtcdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutTenantTenantIdConfigEtcdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 409:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON409 = &dest

	}

	return response, nil
}

// ParsePutTenantTenantIdConfigRisingwaveResponse parses an HTTP response from a PutTenantTenantIdConfigRisingwaveWithResponse call
func ParsePutTenantTenantIdConfigRisingwaveResponse(rsp *http.Response) (*PutTenantTenantIdConfigRisingwaveResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutTenantTenantIdConfigRisingwaveResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 409:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON409 = &dest

	}

	return response, nil
}

// ParseGetTenantTenantIdDatabasesResponse parses an HTTP response from a GetTenantTenantIdDatabasesWithResponse call
func ParseGetTenantTenantIdDatabasesResponse(rsp *http.Response) (*GetTenantTenantIdDatabasesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantTenantIdDatabasesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest GetDatabasesResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdExecutionResponse parses an HTTP response from a PostTenantTenantIdExecutionWithResponse call
func ParsePostTenantTenantIdExecutionResponse(rsp *http.Response) (*PostTenantTenantIdExecutionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdExecutionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdExtensionsServerlessbackfillDisableResponse parses an HTTP response from a PostTenantTenantIdExtensionsServerlessbackfillDisableWithResponse call
func ParsePostTenantTenantIdExtensionsServerlessbackfillDisableResponse(rsp *http.Response) (*PostTenantTenantIdExtensionsServerlessbackfillDisableResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdExtensionsServerlessbackfillDisableResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdExtensionsServerlessbackfillEnableResponse parses an HTTP response from a PostTenantTenantIdExtensionsServerlessbackfillEnableWithResponse call
func ParsePostTenantTenantIdExtensionsServerlessbackfillEnableResponse(rsp *http.Response) (*PostTenantTenantIdExtensionsServerlessbackfillEnableResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdExtensionsServerlessbackfillEnableResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdExtensionsServerlessbackfillVersionResponse parses an HTTP response from a PostTenantTenantIdExtensionsServerlessbackfillVersionWithResponse call
func ParsePostTenantTenantIdExtensionsServerlessbackfillVersionResponse(rsp *http.Response) (*PostTenantTenantIdExtensionsServerlessbackfillVersionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdExtensionsServerlessbackfillVersionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest WorkflowIdResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetTenantTenantIdInfoResponse parses an HTTP response from a GetTenantTenantIdInfoWithResponse call
func ParseGetTenantTenantIdInfoResponse(rsp *http.Response) (*GetTenantTenantIdInfoResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantTenantIdInfoResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest TenantClusterInfoResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetTenantTenantIdMetricsResponse parses an HTTP response from a GetTenantTenantIdMetricsWithResponse call
func ParseGetTenantTenantIdMetricsResponse(rsp *http.Response) (*GetTenantTenantIdMetricsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantTenantIdMetricsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseDeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse parses an HTTP response from a DeleteTenantTenantIdPrivatelinkPrivateLinkIdWithResponse call
func ParseDeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse(rsp *http.Response) (*DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteTenantTenantIdPrivatelinkPrivateLinkIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParseGetTenantTenantIdPrivatelinkPrivateLinkIdResponse parses an HTTP response from a GetTenantTenantIdPrivatelinkPrivateLinkIdWithResponse call
func ParseGetTenantTenantIdPrivatelinkPrivateLinkIdResponse(rsp *http.Response) (*GetTenantTenantIdPrivatelinkPrivateLinkIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantTenantIdPrivatelinkPrivateLinkIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest PrivateLink
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdPrivatelinksResponse parses an HTTP response from a PostTenantTenantIdPrivatelinksWithResponse call
func ParsePostTenantTenantIdPrivatelinksResponse(rsp *http.Response) (*PostTenantTenantIdPrivatelinksResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdPrivatelinksResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest PostPrivateLinkResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParseDeleteTenantTenantIdRelationResponse parses an HTTP response from a DeleteTenantTenantIdRelationWithResponse call
func ParseDeleteTenantTenantIdRelationResponse(rsp *http.Response) (*DeleteTenantTenantIdRelationResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteTenantTenantIdRelationResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest FailedPreconditionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	}

	return response, nil
}

// ParseGetTenantTenantIdRelationsGetCountsResponse parses an HTTP response from a GetTenantTenantIdRelationsGetCountsWithResponse call
func ParseGetTenantTenantIdRelationsGetCountsResponse(rsp *http.Response) (*GetTenantTenantIdRelationsGetCountsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantTenantIdRelationsGetCountsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest RelationCounts
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdResourceResponse parses an HTTP response from a PostTenantTenantIdResourceWithResponse call
func ParsePostTenantTenantIdResourceResponse(rsp *http.Response) (*PostTenantTenantIdResourceResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdResourceResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdSourcesFetchKafkaTopicResponse parses an HTTP response from a PostTenantTenantIdSourcesFetchKafkaTopicWithResponse call
func ParsePostTenantTenantIdSourcesFetchKafkaTopicResponse(rsp *http.Response) (*PostTenantTenantIdSourcesFetchKafkaTopicResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdSourcesFetchKafkaTopicResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest PostSourcesFetchKafkaTopicResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdSourcesPingResponse parses an HTTP response from a PostTenantTenantIdSourcesPingWithResponse call
func ParsePostTenantTenantIdSourcesPingResponse(rsp *http.Response) (*PostTenantTenantIdSourcesPingResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdSourcesPingResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest AvailabilityResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	}

	return response, nil
}

// ParsePutTenantTenantIdStartResponse parses an HTTP response from a PutTenantTenantIdStartWithResponse call
func ParsePutTenantTenantIdStartResponse(rsp *http.Response) (*PutTenantTenantIdStartResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutTenantTenantIdStartResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 409:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON409 = &dest

	}

	return response, nil
}

// ParsePutTenantTenantIdStopResponse parses an HTTP response from a PutTenantTenantIdStopWithResponse call
func ParsePutTenantTenantIdStopResponse(rsp *http.Response) (*PutTenantTenantIdStopResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutTenantTenantIdStopResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 409:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON409 = &dest

	}

	return response, nil
}

// ParsePutTenantTenantIdUpdateCfgConfigIdResponse parses an HTTP response from a PutTenantTenantIdUpdateCfgConfigIdWithResponse call
func ParsePutTenantTenantIdUpdateCfgConfigIdResponse(rsp *http.Response) (*PutTenantTenantIdUpdateCfgConfigIdResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutTenantTenantIdUpdateCfgConfigIdResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest NotFoundResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 409:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON409 = &dest

	}

	return response, nil
}

// ParsePostTenantTenantIdUpdateVersionResponse parses an HTTP response from a PostTenantTenantIdUpdateVersionWithResponse call
func ParsePostTenantTenantIdUpdateVersionResponse(rsp *http.Response) (*PostTenantTenantIdUpdateVersionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantTenantIdUpdateVersionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 404:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON404 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 409:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON409 = &dest

	}

	return response, nil
}

// ParseGetTenantsResponse parses an HTTP response from a GetTenantsWithResponse call
func ParseGetTenantsResponse(rsp *http.Response) (*GetTenantsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest TenantSizePage
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostTenantsResponse parses an HTTP response from a PostTenantsWithResponse call
func ParsePostTenantsResponse(rsp *http.Response) (*PostTenantsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostTenantsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 202:
		var dest CreateTenantResponseBody
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON202 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 400:
		var dest BadRequestResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON400 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseGetTenantsStatusResponse parses an HTTP response from a GetTenantsStatusWithResponse call
func ParseGetTenantsStatusResponse(rsp *http.Response) (*GetTenantsStatusResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTenantsStatusResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest TenantStatusCounts
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetTiersResponse parses an HTTP response from a GetTiersWithResponse call
func ParseGetTiersResponse(rsp *http.Response) (*GetTiersResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTiersResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest Tiers
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetVersionResponse parses an HTTP response from a GetVersionWithResponse call
func ParseGetVersionResponse(rsp *http.Response) (*GetVersionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetVersionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DefaultResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+x9bW/bOPL4VyH0O2DvACVO2u4CF+D/wnHcbm7b1Bsnu3fYFllGom1eZFIlqaTZIt/9",
	"Dz5JlEzJ9FOS9vqmdWw+DGeGM8PhzPBLlNB5TgkigkdHXyKGeE4JR+qP/i3EGbzGGRb35+YH+X1CiUBE",
	"yI8wzzOcQIEp6f2XUyK/48kMzaH8lDOaIyawHg7q4TI1hrjPUXQUXVOaIUiihzia86nzAxcMk2n08BBH",
	"DH0qMENpdPSHM4bu8DG2Hej1f1EiogfZI0U8YTiXUEVHtWUAu0A54zFMz9GnAnGxhdUFgR8K8quDA3AM",
	"U2DAk8CeoAkssmcHqQGrxCsHDImCEUymQI7xEEevIc5QOmIooSTFst9zRLeGErhgSuDPqHhNC5I+P5Bf",
	"gTMqgAJOAnoOyRSdUTGGAvMJtvvsWcB6+BNQ4CmIXQAf4miM2C1O0CUpt/ZzQ/XFDIGkYAwRAbiGFmAO",
	"igpiNbSGxRWcGRpY4XqhJmmCmeSFB8w4wqn36zn8jOfF3PkNE4GmiKkf0Zyy++WrxmkUq4nLLtXAi9iI",
	"q7W8QwKOBWWedcA7fsVS9fFvDE2io+j/epVe6RnM9BaH6t/x85TLaeBf7CqnXEwZWmecv9jIdn6IIySS",
	"dPVBhrLXQxxNk/wqyWiR8k/Z6qO8SfKB7Dz+lMnB7JrWGcosyYzEZ1AS9Cqfrj7SWPcdaXYIoLEhzAKl",
	"EZHNUp8GDxzYodT2Rx8awjekgWbvMf4LvcHH/u1DaKobY4Hm4RxY3+AVkJAxeL+w9/QkcROgsG3nMtb2",
	"Mefw2jeKv2oPbIq9Y5jcFPmYwJzPqDgVaO4R7QxBgdIrKK4Kgj9fzXGWqR8mlM2h0Mj76VUUe3CpxX/Z",
	"sCiUzF5UB0jAK26guGp0ah+d3V3dIsax1qALo3IBRcFD9Yh3leUgH5cij0sajuBUKRWYZe8n0dEf3byj",
	"Wj/E3Y3ksKpRnSolcwZxqYfOyzhUj7u47oVv4ujzniQKIzCLjgQr0EMcDbKCC8TGJQkQker+j+iSYIIF",
	"hhn+C0m8jxi9xZKE6q9zBFOpxi8Qm2MChSSXPDBkSOjWiKSYTM8RpwVLkPpBkj+OLvPUNtf2rwN6xRLl",
	"JrUjeNjd3cenfvOlzdpptVwkapXN5xM5DcQ3Aaj6NqwdH08uLNCeu9Za5zaA9kKpNtvJ8SVHzAB4TNP7",
	"Vtlz7T9n618Ljpj/9xxyfkeZf228yBFr7ysQgUScNuRXuyySIxE4R8vFTTmy08mB1YUsrhDQjsYLNZ49",
	"avjxuOpqdPuzVdfjdPPBqwnuoTIkGZ1i4qdEwVE3G5QN2qnZQR31o0J5a1d+z3Eg6hq4KTvXaO2uyJm+",
	"uZK4Qkw7NvtKeIfqAUOBBdlvR/NYsul1YX9YPnK/VCWL4DKaW1bN1NnXz6opFPAacj+pOnc0Q1kXjQO3",
	"Zzl/NaB/o/poMiRpTjERKy5rRrWEbjk+hxhaRgH/3DqSaTCiTPjtXqq8BNyP99ZeXskSuDtwJTOU+FBo",
	"MJPFLiEsaI11NlblJQhjb+n01wKx+3PEi8xDmVYzMY5uYVa0mVs+n8ap/vFFHM0xqf5o7rVOu8uAU07u",
	"W9UbJE4Mdni34LdIXAJ7J0TVGC2wnM7hFF3AqevualirpkWAMW5b+ub6BU5u4ICSCZ76tMhA/jHBCRT+",
	"bcYhz96hZAYJ5vOaVfq+f3nx8/Gwfz48j+LozXjcH51Kc/Nt//QsiqPx4Lz/bm/8c3/vxY8/1f7+8fCF",
	"19CUU406zQ/Is8surcRRUjAs7keMCprQzAV33B+/vRqP30pQ9L/yCwXtxfDfF36IELut6bgWAph2PvS/",
	"hdco8x8Sb9B9+w5aPqnsbhv7Zn4HCZyi1JwqPLTXP1wZp+YVTBJakCCZ2mEFzaEatGCZd6BW0lE2DTr2",
	"KmjJ9EoYl6qngZBnGu2ZTLUrH2ajumnXspcr5FUCrkuB109schx6g0jg6Vmu2CAkriSYg7+4lUR2ogY6",
	"nMV/XGCARz5rG9DDj9sNdl0mYMvx1z1tL3V2YsIFJAkaZJD7uYbjv9Cb64CTXn2osqN31wa5SttnjiN+",
	"UwRIrJsiEA6/U5U5voDOHbLgPFgFb+UsYaB2+ki7UCZwiJRXrcIg6fKnPkvUdbhHLfN6/R0tjH6adk9n",
	"L8SsclZXNrXrkri8VmpcyTQui2o3Iz4d/g4JhhO/Bs6kcg4XUZUu9xwGPXZvp7hTYI3UwWeZrDNgdpq3",
	"7niLrgw8R1zAeV5TsSkUaE/+5NOzpRFSNaeFjrwwbUkxv/YwXjVXp3GiwPVJ3ZV8tA5xPSQJO7waDdzm",
	"u40jqzQbvIPnWIRaRXQy4Uis5Q7R85RDeAGkXBzf08ToTuXWRd0+woILOh87plL90lseXX56BRBJaIpS",
	"oJuD0rrw8UvrpcLDcoh5J7Ad5v42TL1OyD2MUrOx5EJGDN9Cgd5ictONdEoISiSQZ20rEpBNkQhQRbpd",
	"3ByzjTtqIHadfQNgDLqf8l4XhcFqb1y6AbU3X6dht2V3lN1MMnp3ugbwTt/Ynbd1Ae4lQiuazXmcEhRg",
	"ibuH+IePJRdXCvRGNvDovybf6GOCmb57Afw1EslMzXxBc5x0LkjNf1UtK3gxzUOtO86q8HV682WTTTw6",
	"ZoA2mMobBQ3bEurPc5gIylY2AZ2IQNm8EGiTISZMxVelm4wxRwJu0l/aiynMKNlgJV4NU0m8taRc1USe",
	"7mt7bXzRv7gcX12ejUfDwenr0+FJFEej4dnJ6dmbKI76g8FwdKG+PB/+azjQHwdv34+HJ14LFTkO8PWk",
	"reuvsFAOzof9Cw2R+qigOBm+HZovh+fn78+jOLo8++Xs/e9nXsha1dF2fdil86OJ9DCdUbimxFNaEms5",
	"jVqMDIuSmrVhb4EGtDAR281QHYGYiRO4usXobsHifPnCa59ys02C2pan15DWwoaiLm+8YOOYQO+FRZUw",
	"GMB9XKHcUl4nwFpWuOronedTNvyMkkIHNnfw3vp3dJ8K1BIgIc9atBDafleR2NHRjwcHB80YVtMOYALm",
	"fMOreOeKyX8Vr8H14UrryFZPdLss1he/fRF+fEUiSR1zZPH2EMFMzK64J9bmhtA7EsXRz6rJvRSRZGY+",
	"+4RkuG/cvdFBn+E8lzsjuj3YP9w/fOFbRQYF4uVVUXA3wgOtYsqmgS0ZmraFjFlP01LBVzeRTDBaB408",
	"Ok0ygg5YOi8IcSKdalFMcTQWNM+rT/rXsYDMNBx+zhU/x5G2Q51IqMt8ymBqP5df21NJOYBAhCtRrGw5",
	"TMmQwOus9dcTzBd/Hqt7owxxfgyTmwnOMn12727jm2ixlTvhOyTgOzxlJfIQF1Sh2av1uwJZKmdpJ6kx",
	"YqcqnLrIEzrHZGrxd4HnKHwbFwodK+38gsvd0jgeTRFBDGZSUOEcZZgg78qlQDtdL2qljFdpBvWUe8dR",
	"6saTXO0cV8Y5cmJBArhbxsVOU6AZGWB3eF0eujhqF9OrBckY0e5xwulfjNFzSiZ0iQOiahhud+40Kiuu",
	"wdSOsDKZrOvkt0zTKQoFymRJ00G7+PRrG81QfhG/lhx3znLsrgKnboDgCdBLA6cnAHNAqAA5o7c4RWkM",
	"Co6AmGFu2uzbfJvsXv6USrNFTECeFVNMvIefm6KDV7YlyFy5UlpbjmRZSdZ4Oa/1iNPQnO1xqSvSblD1",
	"qzwKA5jM0OrjlD0NZ/5Gs2KOOrMG5m5OUfhsVSpSa0StPkw6QC3H6qCGw205bDbx1ITmMnk7ru3bWdup",
	"s6E3x+/G6eCzFW54PYe5lgvRNk5bN+ltw1y3TVLcNsps2zyhrSWPjd35ZXV4fpsvra1yiAf11OlGft+4",
	"BHA5ZyxPEVhPjXol8mucaZ7vlKa7FboGvtVk7wLgwYjdjTTervt8Ldn8P+J+X8I864rT7mErGRtCmu6x",
	"rNAMF3jd49Wl4Jak1ceVMf+8gt0CSBAcrNjB5ds3FFZfTD0i7Kta0uMGjuqjUKAk6Puvas0QKy1SOU4G",
	"NgK6qXHM1wFZJaEZtM6llxw8CLJ1fDLuwlrdM04j3pXtsdJ0/a5cDe96sS9OHbrZ4krvn+0k2zyuz1QI",
	"tPN5XhsLYOcTSTH0KJMEWZaeoiLuOOPSJtkVyDgN9/uYagMOS4SZ4wwJRASmZIQYpqm/0S3McIrFfXsb",
	"//VwK67amDhu3Uat7OhlnyB8LKxrERtt239FCYf9iZmGes4FwGuGJN5OyS3WWfEn6BZlUszsmZ+qL44h",
	"x0ntmwvtMtXIVpecx/95P1A59+qHY0SS2RyyG++tgoSG+4JyA1JEK5z4otH9YlTfIQVkinfeOj9ZMrdv",
	"Sb+XIXjdtwdbCvPz1IWqksvGkjSmRF6Of0H3/ULMFl3eprIVMDk7AOYY3KD7ffAzgiliQAMHflDcBnTA",
	"7d//9uUG3T8c/e2LSmoWD//4ITKlpZCOylWMaWCbCZGrmhoIMsQsFNfqr9d26f/6/aI2hPq1OcaDSnY1",
	"Ny5Y6Pvlw8iJjY0O9w/2YJbPoE57RQTmODqKXu4f7L9U1BMzhZKe/McEDUnKqHgVSY7ojQpYrZUYfHFw",
	"0LYByna9Zuk7Ba0noacHnUp/DhR1sgxmKLkBYoaA2xzQifpOsyYwLLkAv1tLUC2awTnSWU5/yDNUdGSi",
	"H2zsefMi0HKbBDl26pc1OfPjOojyFmx8iKNXL16s29lhe7VEl9X++CiNd3cL/PFRGdhwyqttruKWfNS6",
	"vqfJnrkP632R6HrQ1MqQdsvUsa/rmTiBXiVON+anOHp18GodPlwDObyYzyG7LxcEIJCqBBhERBX63AQ3",
	"D/7atlgYhoIL662SsbdYO+/9L0+F3TdIhKD2wb+JpTSr9jBZY/fmhYc89UhFRyg4GnorlGkPiXyoaz3D",
	"TE+4iWS/fz46e2gTKXTzwSyjdyjdo2y6Z6y2qJ/eQpIoU3Jhd7aIt94ckgLaEB9lf+2E9Uw5iwbv1TNu",
	"JPO9c8HZESMuy0x6MAxZ478Xa/LR1yP8deSOMjlcDgTG3ooBntjPNlyC5yjBE6ziJXRvLICgagwTjmC7",
	"rMfI4Xq6VzwXFv7OvN+ZN5R5edfJxE1GDLPsTT6my8khQYP+wWyS50pjfXw0266qHdFi5G3GUG8xFzVW",
	"4q3GWohg4I8jD/ij2VWPbx/pKnlbsY9adqZNeurclcOy0eqH7XYFE3f11glJz2UblrXRNjtcLZSx38bp",
	"igBLQ+U90Z6T63tQUQFQBtyY2ppjwPCO3TN7OWJzzI23qeSO/QybEINeRqc9RbIhYzr2w+vg+Z3BPEdM",
	"wvSW3mDw88XFCPRHp2APzITI+VGvN2VwAgncT+i8l9KE9zJ6g3s6MrbH0AQxRBLUgznu/Z+acU822KO3",
	"iO3BPQbJFO3RiY1Ar3PtrwbCt3S6CtOa8p9tNtL6mqXMFG8fvI6/giOQYYKuJANDTLjU0xOcSQ2f0SmP",
	"Adqf7oM/P5gkrCucfoiODv+M5Vd6tg/R0Ydohqezq/9STK7gPM90iTFMyYdIt0SSiKrhRD+9ICjIIeMI",
	"yB0CcnifUZh+iP6UTPTnB7Uc1RypbCvZ908n6LaeEBaXyWPl8o0ZOkecSz3mi8jtQuAS+jTSrUyXBcwl",
	"tMhScI2ABvc0jYGEV/6v+wADXwzUDU3PLlZ5JGOgkGbbRMFL4AKyunVRh/f89eDly5f/7Jn/zyChxj0t",
	"UArKbAug5wF/V+OBOYKEgw/FwcFL9P/+EVs7TvELJQjMaMEAnNIo9mdukCIz78FobAYuBpF0i0tBJHUX",
	"kjTWQejdlsFPMdP5rI1F2Hjya5jc3Ok7JsvbE8rMN+WP4ey7aFmWcx0eHMQeyTLHRL8zcfjYym6h7qRH",
	"63kaBZ6ePO/9PMTRj7prfZJTY64AXdhP77tNlaaC2WxhKQ6A1CcAAqVPlAo1GU1GTWZ02qUiMzrdl6JN",
	"6cZ5VU6nzZayFXeClJIeb6Vje/wk6q2k9DoWn5JjF7hhLoakmXUIp62Ol9v78+dzMDRstG2DNI5eHf60",
	"vKPvnaMtGLNm/1TmKzg9cbai3V4d29E00VvSf+RhlApdGN5ruEo4xAwB2QwM+tJyQPZe8hxzTKa/w1tU",
	"HpFVVtQMc1CmFy5s+XM9oZ8bFievTcyLJEGcT4osu38MOWlRbZD0UYk1bSb1VP7u0Zfy9F8HYiQVuaob",
	"Y8wqqbiT8ppXg/YDr933LmCrKisz0imyu3IgLBYH2pbroPX6d13luAkZbSqh9g8UKRZ7hKZQwHKBvh0i",
	"ygIB3dfAJtt0a77Xl1+N7zW1t8bN8/ZpWp22jQekeRHfLrzMiXzfYF021F/tldgvcR91XD630WV76sfm",
	"GT8rd8hUXTbvhCJGnazmR9iK3yDMl/ax2rY959WDkO17Ypo/By+JE4e24yCdde9+Op7RfCq2N7LIFoQB",
	"EomS/Umd2S1bdHC7HUI9p2Gl0FI58yQMtEvb2r4n4nto1UUyB0+jhtamqHbidl3fNCm6C/Or7QWlcANs",
	"i1T2EVnXAAHptaGyscG/ZhlRXiTVpcQdFjNgXigB48vR8PxyPDzvnb0vP8fAVK077p29tx/tl6Zx9QcY",
	"9cfj39+fn7TJnUUTNJR3NVEU97bEdD0G77bFdO/03vHrZLm+cribTfQDB05psi2zhgmDeXBsID3DlyWa",
	"6wJO+S6tZN/7Mh55MzUeB33zBZhyMtxpJwMtUqAqyAABp1sKBHbw9MXq44fetXpnMQBppod+l/F7kEbz",
	"hcrOIA11Y5tloEzpARrtwNYz5vubEdlQscMk0C0cY2Bp6NgWbbZllscCc/k8C9tzAvlKXLe8PW7olDOa",
	"oLRg6vFx5bhG6b4THrLY0ZxHdb4l4DN798h05TxwjSaUIT2W/Ft4Jns0ptBXlJ1uoUWZ0ftSVeN+CD12",
	"1ik9rsp5PyZHxt7RuQtMyPj+tKaPfu5d5BA7H0jNm7BtXPY4PFCd+9ZmAmlWlAUmVtryFSOcmyH+t/hh",
	"XIoBCgwSlc0OJozO9UWDBefRGMLAYUkTzhm6jlzPVgHpNtwtH+jadUPzEs1jKqf2A4NAn0UvzyBuKJgm",
	"iRc0h1w6QOTWlt0zY+7r4J3hxeDk6l3/31fnw18vh+OLq+P/XAzHR+BDdHjw4tWH6MMHopr8evn+on91",
	"3B/8Mjw7aTaKlh88PHzm3isBjkgKIL8nyYxRQgsODCokF0oVew8IugNqMXohhabg/mY3jU8QUdnvXM06",
	"rmHP6WNxC1Q2/aob4bzq+fVvhwoN+4LOs+aueDxWds9Y3wpDt69p+2xde5s17LxYvvr6+Hy8u+O9/ylb",
	"D+dfcp2pMf75/e/gpH/RP+6Ph2OQ0PkcEhWhyGf0Tp0NS9xuqODf/YbRXZB3uDoMhul1ZCvtt0cD6GL8",
	"CEDAP2Xaz2gLzQJ2BxKGUkQEhuo9tWWmYVnZ//mIwPW5pu2lgu8uxC3GLtS51RRn5z1elme/NuXZe6mq",
	"zx6YRLdBbkLoMagsJc/HC8CeGFh36BZpqfDhkWj9JEG50JklX6uP2iBUx3tV6rPiGDChDFRcAywldHBS",
	"YImFVVkSka+GI4fkO0NukyE1PrfFj25SmAkksnlhpgJTbAoolYFGON2TDLanOG8vowlUBV4rPtsSjzuv",
	"PT57Jv+tzIhd/UhUVfw/3H+hSvR4DklBh57vu2nd8hLr7CZpkRse3QeGAWytqAzfIHC4f7B/sP/cNp2t",
	"HhV2JFMvaXwrp7HuB048HD6WBy7JGzZxXiJPR3pjbq5tNjyCYfNUSWB4YRiNA5JM6mTuTjl5Kkqv7UQa",
	"FVnW3NM2cWCr1AtIN3AIWGZkhNIx109jZpjc9L7k1TuZa1yijaqhRu5A0WOWxdhW7LonytpgB8gFgut7",
	"gNOVfFoOcsPDGlfH7fZkmftq6nOMsd4mPTpCqx/zki9foO3a93xLtztf2ex9rLiLkQvk7rJvWp5qf2Rj",
	"uOtFds+uMzGUDh0Be3oZKU3YMulBUL0lTUkfk7fF5VcqTQ3ZYLzfRoP1d6wNxfTzOTOP49Z1WB2VKaM5",
	"sA3B3+dQ3GJ0x2Og6gXw2EDOdV4+j8H+/v4/5BIM+LUQOVsUZbmatO/2fgtu5BNGc/vUgV7Vdy9ylxdZ",
	"l6SQ/7v3Ihu4lS378t4Uiapkf5hRYWnG35R9v5VzWONx7JYgyHLzJ7bZJsmqFptdIqxstNppi7mv/QVq",
	"0fIttW9AzlSrs6vi66nsxzt0BJZa3k05vlIXW8ZxMxE3uIl3eMqbFTpOYNaqkw3hehMkktkvcHIDL2iO",
	"k/br29eyocnmFrJpyDWtzqnmrxuTfCObwL+6J0xg6gKq25Jdt1Rg4M1sO0ubjvtNPgyWxpaRH6kUgZe/",
	"TWWCb4ipv5dBCGU/FR4fHME3NgW/ntKw8wTMWX+3rh82gxxcI0QAJlhgWF75fEUxcArPAJaOfDGDzrq4",
	"oHmONtG9rTp3tWwJCcgqzEPz58A7u6nH8XxYh+YAlsk40FNkdXvcQvNwZtFj7SWTae+LfY7+IZx79NXn",
	"YDId2Kfsn97FmlSg7DiL4hyV6VR0Aswbs4DQFHGPvPuag34XK1JtM/h3NWb9zYnpCDwpX9Y6fiVGVf2t",
	"KyeSZfFhq+bzVVssVP/1vPLhFKq3Z+O7sjK9oCbveUtR6gFOuO+V5atnQndaUN5mO1eU54DeEZQqn4i5",
	"jVitIrUlfGgpk13dYlmXmDmkPNkdlr6UKh10IRdYlX/qa3W11RbCdXZBR4GvIIYyl1otttuI0Vuserii",
	"ple9yNtZSpIU82tdCj3FE1XWvMwHFyWXtgmrsX2geNeiwH172MM96lXPqsS8QXtzQdsoUbGSAPAaKHjJ",
	"MyP6KdBdohS3FJBSLIGbtaNWquqpONCxOtoW6Yat7uaxR0VqCZtWogXLoqNI1eu/PYwcujZX9t6CygG8",
	"poVwdkHNyuPqXfL1+nq6OrbyyTGwZYJsQXBTiyZgxnkZT1cr0xzUN6NTp2NGp9HDx4f/HwAA//++ZjfX",
	"AMQAAA==",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
