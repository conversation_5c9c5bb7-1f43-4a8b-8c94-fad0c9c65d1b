# Development

## Prerequisites

RisingWave Cloud services are written mostly in Golang, you need to pre-install
several tools in your system.

Compilation requirements:

- [golang](https://go.dev/doc/install)
- [docker](https://docs.docker.com/engine/install/)
- [kind](https://kind.sigs.k8s.io/docs/user/quick-start/)
- [aws](https://docs.aws.amazon.com/zh_cn/cli/latest/userguide/getting-started-install.html#getting-started-install-instructions)
- [helm](https://helm.sh/docs/intro/install/)

### Go Workspaces

If you open RisingWave Cloud's repository in your editor (e.g. VS Code), you may
get the following error:

> Error loading workspace: You are outside of a module and outside of
> $GOPATH/src. If you are using modules, please open your editor to a directory
> in your module. [...]

One can resolve this issue by setting up a
[Go workspace](https://github.com/golang/tools/blob/master/gopls/doc/workspace.md#go-workspaces-go-118).
This is done by first running `go work init` and then `go work use <list>` with
the list of directories which contain a Go module (i.e., which contain a
`go.mod` file). At the time of writing, the commands below contain all such
directories.

```bash
go work init
go work use account/ admin/ billing/ cli/ mgmt/ onebox/ proxy/ shared/ test/ pgweb/
```

After restarting your editor, the error should be gone.

### Code Architecture

Each folder in cloud repo means one integrity service or tool. If you have
problems, please send them to team channel or contact corresponding code owner.

- [docs](/docs/): contains RisingWave cloud documents that are intended to be
  used by contributors to understand our project, development process, and how
  we design and implement RisingWave Cloud.
- [test](/test/) test for each service & function
- [api](/api/v1): put backend yaml file here. Write API definition in this
  folder.
- [onebox](/onebox): code related to tool onebox, if you meet problem during
  onebox and want to check code, the command line entrypoint in `./jobs/jobs.go`
- [cli](/cli): code related to tool cli
- [proxy](/proxy): code related to tool proxy
- [accout](/account): account service
- [mgmt](/mgmt): managerment service
- [portal](/portal): portal front end code
- [pgweb](/pgweb/): backend service for the Risingwave SQL console,
  which users can run queries against their instances interactively
- [shared](/shared/): contains all common libraries shared by services/projects
- [log](/log/): will appear when you run service locally, the log of each
  service will save at here

## Onebox

Onebox is a tool that allows you to compile and run your code in one command
without dependencies on real cloud environments. This is mostly useful to
develop and run end-to-end workflows and management operations without specific
dependencies on the cloud resources like S3 and autoscalers.

Run `make install` to install onebox, then run `make start` to start a local
development environment.

Common onebox usages:

```bash
# destroy and start a new dev env (start a new Kubernetes cluster and databases and install k8s components in the new cluster)
onebox setup

# start/restart all services (account service, API service, and workflow engine)
onebox dev

# clean all data (this will not destroy the cluster and databases, but the running services will be killed)
onebox clean

# shutdown all service and dev env
onebox shutdown

# check current running services
onebox proc ls

# watch the stdout of a service
onebox proc log <service name>

# check the manual
onebox <sub command> --help # e.g. onebox --help, onebox dev --help
```

Refer to [Onebox Readme](../onebox/README.md) for more information.

## Infrastructure

### MSDB (Management System Database)

The persistent layer of the management system. It stores all the information
about a tenant and also stores information about the running workflow. MSDB is
the single source of truth of all tenants, clusters, users, and workflow status.

### ASDB (Account Service Database)

The persistent layer of the account service. It stores all information of all
users, including the regions and the names of the tenants they have. ASDB is the
single source of truth for all user accounts.

### Cluster

The Kubernetes cluster running RisingWave instances. It also contains several
necessary components, including the RisingWave operator, rwproxy, etc.

## Services

As you can see in `onebox proc ls`, there are 4 services running in your local
machine.

### API service

The API service is a part of the management system. Each region will have one
management system, and each management system will manage multiple clusters in
its region. Users send requests to the API service to manage the RisingWave
instances and the cluster.

### Workflow engine

The Workflow engine is also a part of the management system. It is responsible
for running the asynchronous task, such as creating a new tenant or deleting an
existing tenant. It can also run cron jobs and work like a message queue.

### Account service

The account service is responsible for validating the request token. For
instance, if users want to access the API service, they need to get the token
from the account service at first. Once the API service receives token from
users, it will check if the token is valid via the API exposed by the account
service. Account service is also responsible for user notification, e.g. email.

### Pgweb

Pgweb is designed to allow users to run SQL directly in their tenants on
the website.

### Admin service

The admin service is used for operation.

### Ports

We have some conventions when defining ports:

- The first two digits are used for different service components. (e.g. 80xx for
  API service, 81xx for account service)
- The last two digits are used for different purposes. (e.g. xx80 for HTTP
  service, xx81 for admin HTTP service)

We have used the following ports:

| service           | http | admin | metrics |
| :---------------- | ---: | ----: | ------: |
| api service       | 8080 |  8081 |    8090 |
| account service   | 8180 |  8181 |    8190 |
| web console/pgweb | 8280 |       |    8290 |
| workflow engine   |      |       |    8390 |
| admin service     | 8480 |       |    8490 |
| billing service   | 8580 |  8581 |         |

## Tools

### RWC CLI tool

To easily interact with RisingWave cloud management services, we developed a
command line tool called [RWC CLI](../cli/README.md). This tool is developed by
ourselves.

### Postman

You also can use [Postman](https://www.postman.com/) to send request to API.

### Sqlc

Use [sqlc](https://sqlc.dev/) to generate code for database access

### Wire

[Tutorial](https://github.com/google/wire/blob/main/_tutorial/README.md)

### Psql

Use [psql](https://www.postgresguide.com/utilities/psql/) to access the database
in container, after you launch the service. Take an example:

- If you use Dokcer App, Click `Containers`, find `onebox-asdb`, in `Actions`
  click `Open in terminal`, use
  `psql "postgres://rwc_root:postgres@localhost:5432/asdb"` in cmd to connect
  database in account service.
- Or see [general methods](/docs/develop.md#connecting-to-msdb-or-asdb) using
  onebox

### Harness

Use [Harness](https://app.harness.io/) to deploy the service. If needed, ask
your colleagues to invite you to risingwave labs account. After the invitation,
you can log in through SSO using your company email.

## API Specs

1. Open to `/api/v1/*.yaml` to get the detailed specs of each api
2. Register a fake account with `/api/v1/auth/register`
3. Login with `/api/v1/auth/login` to get a Bearer Token
4. Now you can create a new tenant (aka risingwave instance) with
   `/api/v1/tenants`

If you use Postman:

1. Open to `/api/v1/*.yaml` to get the detailed specs of each api, use
   `make start` to make sure service launch correctly. In yaml file, we use
   openapi package.
2. Try `GET` url `http://localhost:8180/api/v1/version` in Postman. If the
   response is `status:200`, it means your local service launched correctly.
   Here and following url we use `8081` as an example. If you want to try other
   APIs, check the corresponding port.
3. Register a fake account with `/api/v1/auth/register`. Use `POST` and
   configure content in `raw json` format.
4. Login with `:8081/api/v1/auth/login` to get a JWT Bearer Token
5. Try `GET` `:8081/api/v1/auth/ping` , you should get `msg: Unauthorized`
   because we have not configured JWT in Header. Set key `Authorization` and
   value `Bearer {your JWT Token}` in header and try again. You should get a
   correct result.

## Creating a new API

1. The OpenAPI3 spec in `/api` is the single source of truth. To create a new
   api, edit `api/v1/*.yaml` to include the specifications of your new API.
   `acc.yaml` means account service, `mgmt.yaml` means management service.
2. We maintain the reusable OpenAPI parts of as separate files. However, most
   OpenAPI tools don't support the multi-file approach.
   [bundle.sh](../api/bundle.sh) if for bundling relevant parts of an API
   definition into a single file in `api/v1/bundle/*.yaml`. We use the bundled
   file in other tools.
3. `make gen-spec` - To generate the specs, cd `/shared` and run `make gen-spec`
   (or `make codegen` to generate all depending codes). `/shared/spec` will be
   updated with the model structs that you are required for your new api.
   `make gen-spec` will bundle spec files as necessary.
4. Update the Controller and Router to complete your API
5. Enrich `rwc` cli tool as well to support your new API.

## Adding a workflow

If you intend to add a new workflow to support a kind of management operation,
please refer to [Workflow Doc](./rfc/2022_8_23_Workflow.md) and
[ProvisionWorkflow](../mgmt/internal/workflowimpl/provision/provision.go).

## Database schema and query

Database schemas and queries are generated by
[sqlc](https://docs.sqlc.dev/en/stable/). Their sources are at
`/mgmt/sql` and `/account/sql`. There are two directories `migrations`
and `queries` under it. `migrations` defines the table schemas, and `queries`
defines the sql query statements.

`migrations` is a series of migration sql scripts. Each migration contains a
version number that increase by 1 than the previous one. A migration includes
two files with suffixes `.up.sql` and `.down.sql`. They are used to upgrade or
downgrade the schema version. If you want to modify the schema of msdb or asdb:

1. Create upgrade and downgrade scripts for the new migration. For example, if
   the last version is `0005`, you should create `0006_my_schema_update.up.sql`
   and `0006_my_schema_update.down.sql`. Make sure the migration can upgrade and
   downgrade successfully. **The previous migration scripts should not be
   edited, especially when that version has been deployed!**
2. Run `make gen-sql` to generate codes. New `models.go` will be generated in
   `model` package.

Database schema is crucial. And we have a separate pipeline to roll out database
schema change. For more information, please refer to
[database_schema.md](database_schema.md)

If you want to add new queries:

1. Add your query sql in `sql/queries/<a sql file>.sql`
2. Run `make gen-sql` to generate codes. A new method will be generated in
   `querier` interface. We have a mock querier for testing, `make gen-mock` to
   generate the mock querier. (Or `make codegen`, it generates all codes)

## Connecting to MSDB or ASDB

While you develop the cloud control plane, you might need to looks at the
database objects and records to understand the data model.

There are multiple ways to connect to msdb or asdb:

```
# for msdb
onebox attach msdb
psql "postgres://rwc_root:postgres@localhost:5432/msdb?sslmode=disable"
PGPASSWD=postgres docker exec -it onebox-msdb psql -h localhost -U rwc_root -d msdb

msdb=# select * from tenants;

# for asdb
onebox attach asdb
psql "postgres://rwc_root:postgres@localhost:5433/asdb?sslmode=disable"
PGPASSWD=postgres docker exec -it onebox-asdb psql -h localhost -U rwc_root -d asdb

msdb=# select * from users;
```

## Add a new email template

The email template is generated by [mjml](https://mjml.io/). The generated
template is managed by account service.

1. Create a new mjml template `new-template.mjml` in `/mjml/src`. Generate html
   template `new-template.html` by running `pnpm install && pnpm run build` in
   `/mjml`.
2. Copy the html template `new-template.html` to account service
   `/account/assets/templates/`. Register the template in
   [account service](../account/email/const.go)

## Testing

### Unit test

Unit tests are defined in Go format.

The file name should end with `_test.go` and contain test function
`func TestFoo(t *testing.T) {}`. Any test satisfies the format will be executed
during `make ut` by default.

If you want to define a test that don't belong to unit test (e.g. test for
operating resources on aws, or stress test takes long time), you should avoid
the test being compiled by adding build flag `!ut` in the file header.

```golang
//go:build !ut

package bar_test

func TestBar(t *testing.T) {
	// ...
}
```

### CLI test

```bash
cd cli
onebox setup
onebox d
make e2e-test
```

### Onebox test

Use `onebox test` to run all tests defined in onebox. Including:

- End-to-end test
- Workflow test

```bash
onebox setup
onebox test e2e
```

After each test finished, the environment will be cleaned for running the next
test.

To debug tests, you can run those tests individually. The environment will be
kept after test finished.

```bash
# make sure you have run `onebox setup` before any test
onebox test e2e
onebox test workflow TestExpireTenant # run single test method defined in workflow test
```

To create a new type of test, please refer to
[def_test](../onebox/jobs/def_testing.go). To create a new unit test in existing
type, add code [here](/test/e2e/)

### Dev Stack test

The DEV stack is an environment for development that exactly resembles a
production environment. You can deploy your local changes to dev stack for E2E
testing on real cloud environments.

The detailed instruction can be found at [dev stack usage doc](./dev_stack.md)

### Convention

#### Code Format

Recommend to follow the
[Google Style Guides](https://google.github.io/styleguide/).

- Go: [Google Style guide](https://google.github.io/styleguide/go/decisions),
  [Go Code Review Comments](https://github.com/golang/go/wiki/CodeReviewComments)
  and [effective go](https://go.dev/doc/effective_go). We need to have a wider
  agreement and awareness within the team on this. Will update it when the team
  gets a consensus.
- [Typescript](https://google.github.io/styleguide/tsguide.html)
- [Shell](https://google.github.io/styleguide/shellguide.html)

For format tools, please check [makefile](/Makefile), find the corresponding
lint command you need.

- For account service, we use [golangci-lint](https://golangci-lint.run/) at
  [here](/account/Makefile#L82)

#### Issues, PRs

- [Issues](/docs/contribute.md#create-an-issue): There are templates for issues
  and there are requirements for issue labeling.
- [PRs](/docs/contribute.md#submit-a-pr): We alse have templates when you create
  them.

#### CI Pipline

Check details at [here](/.github/workflows).
