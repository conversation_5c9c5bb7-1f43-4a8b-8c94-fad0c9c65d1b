# RisingWave Cloud project

This project aims to build [RisingWave](https://github.com/risingwavelabs/risingwave) cloud services to provide a full fledged managed RisingWave databases on the cloud, to help customers easily provision, manage, and use RisingWave databases for their own workloads. For more information, please refer to [our docs](docs/README.md).

## Architecture

![Cloud Architecture](./docs/assets/readme/cloud-arch.png)

## Quick Start

**Get onebox**

Please refer to [Onebox README](onebox/README.md) for quick installation.

Then you can provision all necessary components to start development using onebox

```bash
onebox setup
onebox d
```

**Get RWC (RisingWave Cloud CLI)**

Please refer to [Installing RisingWave Cloud CLI](cli/README.md#install-via-convenient-script) for quick installation.
Check onebox documentation for more details.

**Set up your local RW instance**

To use the default setup, do... 

```bash 
make start-rw
```

To do a manual setup, do...

```bash
rwc context account set --url http://localhost:8180/api/v1
rwc context region set --name local
rwc auth register --account <EMAIL> --password Password123!
rwc auth login --account <EMAIL> --password Password123!

# create your cluster, e.g.
rwc cluster create --name you-test --tier Test --compute t-cn --meta t-mt --compactor t-cp --frontend t-ft --etcd t-et

# Another possible cluster configuration
# rwc cluster create --name you-test2 --tier Free --compute f-1c2g --compactor f-1c1g --frontend f-1c1g --meta f-1c1g --etcd f-1c1g

# wait until the cluster was status Running
rwc cluster list

# create the new user
rwc cluster dbuser create --name you-test --username my-user --password my-password123
rwc cluster dbuser list --name you-test

# get the endpoint of your cluster
rwc cluster endpoint list --name you-test
``` 

**Create your resources on the Cloud**

You can create your own accounts and resources using our provisioned RisingWave Cloud environments on the cloud.


**For newcomers**

Newcomers are encouraged to read [this onboarding document](docs/onboarding.md) to get more familiar with the team.

**For developers**

If you are ready to develop your own services, please refer to [How to contribute](docs/contribute.md).

## RWC commands

**Context**

Configure your RWC CLI context, use `rwc context set url` and `rwc context set region` set the account service endpoint and management region

```bash
rwc context list
rwc context region list
rwc context account set --url localhost:8180/api/v1
rwc context  region set --name local
```

**Auth**

Manage the RWC CLI's authentication state.

```bash
rwc auth register --account --password
rwc auth login    --account --password
rwc auth delete   --account
rwc auth update   --account --cur-password --password
```

**Tier**

You can choose a cluster plan and configure cluster resources according to your needs when creating a cluster.

```bash
rwc tier list
```

**Cluster** 

A cluster in RisingWave Cloud provides the necessary resources for hosting independent data repositories and streaming pipelines.

Within a cluster, you can create and manage database users and databases.

You can manually control your risingwave clusters on the RisingWave Cloud by RWC CLI.

```bash
rwc cluster list

rwc cluster      stop --name
rwc cluster    create --name --sku --config ...
rwc cluster    delete --name
rwc cluster     start --name
rwc cluster  describe --name
rwc cluster meta-info --name

rwc cluster scale --name ## WIP

rwc cluster   update-etcd --name --config
rwc cluster update-config --name --config-name
rwc cluster update-config --name --config-path
```

**dbusers**

In RisingWave Database, a database user is similar to a database user or role in Postgres,
allowing a person or application to access a specific database or set of databases within a cluster.

Also, you connect and log in to a cluster as one of its database users.

```bash 
rwc cluster dbuser   list --cluster-name
rwc cluster dbuser create --cluster-name --dbusername --dbpassword
rwc cluster dbuser delete --cluster-name --dbusername
rwc cluster dbuser update --cluster-name --dbusername --dbpassword
```


**endpoints**

Get the endpoint of the risingwave cluster's frontend

```bash
rwc cluster endpoint list
```

**risingwave config**

RisingWave configuration, use `rwc cluster config create` to create a custom risingwave config from local file

```bash
rwc cluster config   list
rwc cluster config create --name
rwc cluster config delete --name
```
